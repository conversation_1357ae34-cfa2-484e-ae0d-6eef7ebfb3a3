# 自动化编译系统 (AutoCompile System)

基于多智能体协作的C/C++项目自动化编译系统，为二进制安全领域提供高质量数据集。

## 系统架构

### 核心智能体
- **主控智能体 (MasterAgent)**: 整体编译流程调度和决策
- **项目分析智能体 (ProjectAnalyzer)**: 项目结构和依赖分析  
- **错误处理智能体 (ErrorSolver)**: 编译错误诊断和解决

### 技术特点
- 🤖 多智能体协作架构，采用ReAct框架
- 🐳 Docker容器化编译，支持多版本Ubuntu自动切换
- 🔍 智能依赖分析，结合ccscanner工具和LLM文档分析
- 🔗 SSH持久连接，实时监控编译过程
- 🔄 循环依赖处理，最大递归深度限制
- 📦 编译产物智能识别，基于Git差异检测

## 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install -r requirements.txt

# 构建Docker镜像
cd docker
bash build.sh

# 确保ccscanner工具可用
# 请参考reference_method/ccscanner目录
```

### 2. 配置设置

编辑 `config.py` 文件，配置LLM API密钥和其他参数：

```python
# LLM API配置
LLM1_API_KEY = "your-api-key-here"
LLM2_API_KEY = "your-api-key-here" 
LLM3_API_KEY = "your-api-key-here"

# 数据集输出路径
DATASET_BASE_PATH = "/data/autocompile"
```

### 3. 运行编译

```bash
# 批量编译（从projects_url.txt读取项目列表）
python main.py

# 编译单个项目
python main.py --single-project https://github.com/FFmpeg/FFmpeg.git

# 指定项目列表文件
python main.py --projects-file my_projects.txt

# 指定输出路径
python main.py --dataset-path /custom/path
```

## 项目结构

```
autocompile-master/
├── main.py                 # 主程序入口
├── config.py              # 系统配置
├── MasterAgent.py         # 主控智能体
├── ProjectAnalyzer.py     # 项目分析智能体
├── ErrorSolver.py         # 错误处理智能体
├── tools.py               # 核心工具类集合
├── projects_url.txt       # 项目URL列表
├── requirements.txt       # Python依赖
├── docker/                # Docker镜像配置
│   ├── Dockerfile.ubuntu18.04
│   ├── Dockerfile.ubuntu20.04
│   ├── Dockerfile.ubuntu22.04
│   └── build.sh
├── docs/                  # 文档
│   └── 自动化编译方案总览.md
└── reference_method/      # 参考实现
    ├── AutoCompiler/
    ├── ccscanner/
    └── ...
```

## 工作流程

1. **项目初始化**: 下载项目并创建编译副本（UUID标识）
2. **环境准备**: 构建Docker容器，建立SSH持久连接
3. **项目分析**: 分析项目结构、依赖和构建系统
4. **依赖安装**: 按优先级安装依赖（系统级→语言级→源码编译）
5. **编译执行**: 执行构建命令，实时监控编译过程
6. **错误处理**: 智能诊断和解决编译错误
7. **版本切换**: 遇到兼容性问题时自动切换Ubuntu版本
8. **产物收集**: 识别和保存编译生成的二进制文件

## 输出结果

编译完成后，系统会在指定目录生成：

```
dataset/
├── projects/              # 项目源码
├── binaries/             # 编译产物
│   └── project_name/
│       ├── executable
│       ├── library.so
│       └── metadata.json
├── logs/                 # 编译日志
│   └── project_name_compilation.json
└── compilation_report.json  # 总体报告
```

## 配置说明

### LLM配置
系统支持差异化的LLM配置，针对不同智能体的任务特点：

- **主控智能体**: 平衡决策稳定性与灵活性 (temperature=0.7)
- **项目分析智能体**: 高准确性分析任务 (temperature=0.3)  
- **错误处理智能体**: 创造性问题解决 (temperature=0.8)

### Docker镜像
支持多版本Ubuntu镜像自动切换：
- Ubuntu 18.04: 兼容老旧项目
- Ubuntu 20.04: 默认版本，平衡兼容性
- Ubuntu 22.04: 支持最新特性

## 故障排除

### 常见问题

1. **Docker容器创建失败**
   - 检查Docker服务是否运行
   - 确认镜像已正确构建

2. **SSH连接超时**
   - 检查容器网络配置
   - 确认SSH服务已启动

3. **ccscanner工具不可用**
   - 检查ccscanner是否在PATH中
   - 参考reference_method/ccscanner安装说明

4. **LLM API调用失败**
   - 检查API密钥配置
   - 确认网络连接正常

### 日志分析
系统会生成详细的日志文件：
- `autocompile.log`: 系统运行日志
- `logs/project_name_compilation.json`: 单个项目编译日志
- `compilation_report.json`: 批量编译结果报告

## 贡献指南

欢迎提交Issue和Pull Request来改进系统。

## 许可证

本项目采用MIT许可证。
