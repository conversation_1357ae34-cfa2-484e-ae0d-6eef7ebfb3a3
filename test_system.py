#!/usr/bin/env python3
# test_system.py - 系统测试脚本
import os
import sys
import json
import logging
from main import AutoCompileSystem

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_ffmpeg_compilation():
    """测试FFmpeg项目编译"""
    print("=" * 60)
    print("Testing AutoCompile System with FFmpeg")
    print("=" * 60)
    
    # 初始化系统
    system = AutoCompileSystem("./test_output")
    
    # FFmpeg项目URL
    ffmpeg_url = "https://github.com/FFmpeg/FFmpeg.git"
    
    try:
        print(f"Starting compilation test for: {ffmpeg_url}")
        result = system.compile_single_project(ffmpeg_url)
        
        print("\n" + "=" * 60)
        print("COMPILATION TEST RESULT")
        print("=" * 60)
        print(json.dumps(result, indent=2))
        
        if result["status"] == "COMPILATION-SUCCESS":
            print("\n✅ FFmpeg compilation test PASSED!")
        else:
            print("\n❌ FFmpeg compilation test FAILED!")
            
        return result
        
    except Exception as e:
        print(f"\n❌ Test failed with exception: {str(e)}")
        return {"status": "COMPILATION-FAIL", "error": str(e)}

def test_line_ending_fixer():
    """测试行尾符修复工具"""
    print("\n" + "=" * 60)
    print("Testing LineEndingFixer")
    print("=" * 60)

    try:
        from tools import LineEndingFixer

        # 创建测试文件
        test_file = "./test_line_endings.txt"
        with open(test_file, 'wb') as f:
            f.write(b"#!/bin/bash\r\necho 'Hello World'\r\n")

        fixer = LineEndingFixer()

        # 检测行尾符
        detected = fixer.detect_line_endings(test_file)
        print(f"Detected line endings: {detected}")

        # 修复行尾符
        result = fixer.fix_line_endings(test_file, 'LF')
        print(f"Fix result: {result}")

        # 验证修复结果
        with open(test_file, 'rb') as f:
            content = f.read()

        if b'\r\n' not in content and b'\n' in content:
            print("✅ Line ending fix successful")
            os.unlink(test_file)
            return True
        else:
            print("❌ Line ending fix failed")
            return False

    except Exception as e:
        print(f"❌ LineEndingFixer test failed: {str(e)}")
        return False

def test_error_solver():
    """测试错误处理智能体"""
    print("\n" + "=" * 60)
    print("Testing ErrorSolver")
    print("=" * 60)

    try:
        from ErrorSolver import ErrorSolver

        solver = ErrorSolver("test_project")

        # 测试错误分析
        test_error = "fatal error: stdio.h: No such file or directory"
        analysis_result = solver.analyze_error_pattern(test_error, "compile")

        print(f"Error analysis result: {analysis_result}")

        import json
        analysis_data = json.loads(analysis_result)

        if analysis_data.get("analysis_status") == "success":
            print("✅ Error analysis successful")
            return True
        else:
            print("❌ Error analysis failed")
            return False

    except Exception as e:
        print(f"❌ ErrorSolver test failed: {str(e)}")
        return False

def test_docker_shell():
    """测试Docker Shell功能"""
    print("\n" + "=" * 60)
    print("Testing Docker Shell")
    print("=" * 60)

    try:
        from tools import InteractiveDockerShell

        # 检查Docker是否可用
        import docker
        client = docker.from_env()
        client.ping()

        # 创建Docker Shell实例
        docker_shell = InteractiveDockerShell("20.04")

        # 测试简单命令
        result = docker_shell.execute_command("echo 'Hello Docker'")
        print(f"Simple command result: {result}")

        # 测试复杂命令
        complex_result = docker_shell.execute_command("echo 'test' | wc -l")
        print(f"Complex command result: {complex_result}")

        # 清理
        docker_shell.close()

        if "Hello Docker" in result:
            print("✅ Docker Shell test successful")
            return True
        else:
            print("❌ Docker Shell test failed")
            return False

    except Exception as e:
        print(f"❌ Docker Shell test failed: {str(e)}")
        return False

def test_simple_project():
    """测试一个简单的C项目"""
    print("\n" + "=" * 60)
    print("Testing with a simple C project")
    print("=" * 60)

    # 创建一个简单的测试项目
    test_project_dir = "./test_simple_project"
    os.makedirs(test_project_dir, exist_ok=True)

    # 创建简单的C程序
    with open(os.path.join(test_project_dir, "hello.c"), "w", newline='\n') as f:
        f.write("""#include <stdio.h>

int main() {
    printf("Hello, AutoCompile System!\\n");
    return 0;
}
""")

    # 创建Makefile
    with open(os.path.join(test_project_dir, "Makefile"), "w", newline='\n') as f:
        f.write("""CC=gcc
CFLAGS=-Wall -g

hello: hello.c
\t$(CC) $(CFLAGS) -o hello hello.c

clean:
\trm -f hello

.PHONY: clean
""")

    # 创建README
    with open(os.path.join(test_project_dir, "README.md"), "w", newline='\n') as f:
        f.write("""# Simple Test Project

A simple C program for testing the AutoCompile system.

## Build Instructions

```bash
make
```

## Dependencies

- gcc
- make
""")

    print(f"Created simple test project at: {test_project_dir}")

    # 测试编译
    try:
        from MasterAgent import MasterAgent

        master_agent = MasterAgent("simple_test", "", test_project_dir)
        result = master_agent.compile_project()

        print(f"\nSimple project compilation result: {result}")
        return result

    except Exception as e:
        print(f"Simple project test failed: {str(e)}")
        return "COMPILATION-FAIL"

def main():
    """主测试函数"""
    print("AutoCompile System Test Suite")
    print("=" * 60)
    
    # 检查Docker是否可用
    try:
        import docker
        client = docker.from_env()
        client.ping()
        print("✅ Docker is available")
    except Exception as e:
        print(f"❌ Docker is not available: {str(e)}")
        print("Please ensure Docker is installed and running")
        return
    
    # 检查Docker镜像
    try:
        images = client.images.list()
        autocompiler_images = [img for img in images if any('autocompiler' in tag for tag in img.tags)]
        if autocompiler_images:
            print(f"✅ Found {len(autocompiler_images)} AutoCompiler Docker images")
        else:
            print("❌ No AutoCompiler Docker images found")
            print("Please run: cd docker && bash build.sh")
            return
    except Exception as e:
        print(f"❌ Error checking Docker images: {str(e)}")
        return
    
    # 运行测试
    test_results = []

    # 测试1: 行尾符修复工具
    print("\n" + "=" * 60)
    print("TEST 1: LineEndingFixer")
    print("=" * 60)
    line_ending_result = test_line_ending_fixer()
    test_results.append(("LineEndingFixer", line_ending_result))

    # 测试2: 错误处理智能体
    print("\n" + "=" * 60)
    print("TEST 2: ErrorSolver")
    print("=" * 60)
    error_solver_result = test_error_solver()
    test_results.append(("ErrorSolver", error_solver_result))

    # 测试3: Docker Shell
    print("\n" + "=" * 60)
    print("TEST 3: Docker Shell")
    print("=" * 60)
    docker_shell_result = test_docker_shell()
    test_results.append(("Docker Shell", docker_shell_result))

    # 测试4: 简单项目编译
    print("\n" + "=" * 60)
    print("TEST 4: Simple C Project")
    print("=" * 60)
    simple_result = test_simple_project()
    test_results.append(("Simple Project", simple_result))

    # 测试5: FFmpeg项目（如果用户确认）
    user_input = input("\nDo you want to test FFmpeg compilation? (y/N): ").strip().lower()
    if user_input == 'y':
        ffmpeg_result = test_ffmpeg_compilation()
        test_results.append(("FFmpeg", ffmpeg_result))
    else:
        print("Skipping FFmpeg test")
    
    # 总结结果
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    for test_name, result in test_results:
        if isinstance(result, dict):
            status = result.get("status", "UNKNOWN")
        else:
            status = result
            
        if "SUCCESS" in str(status):
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\nTest completed!")

if __name__ == "__main__":
    main()
