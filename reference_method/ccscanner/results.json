{"target": "tests/", "extractors": [{"deps": [{"depname": "catch2", "version": "2.5.0", "version_op": null, "unified_name": "catch2", "extractor_type": "cmake::cpm", "context": "cpmaddpackage(\"gh:catchorg/catch2@2.5.0\")", "confidence": "High"}, {"depname": "range-v3", "version": "0.11.0", "version_op": null, "unified_name": "range-v3", "extractor_type": "cmake::cpm", "context": "cpmaddpackage(\"gh:ericniebler/range-v3#0.11.0\")", "confidence": "High"}, {"depname": "yaml-cpp", "version": "0.6.3", "version_op": null, "unified_name": "yaml-cpp", "extractor_type": "cmake::cpm", "context": "cpmaddpackage(\"gh:jbeder/yaml-cpp#yaml-cpp-0.6.3@0.6.3\")", "confidence": "High"}, {"depname": "json", "version": "3.9.1", "version_op": null, "unified_name": "json", "extractor_type": "cmake::cpm", "context": "cpmaddpackage(\n  name nlohmann_json\n  version 3.9.1\n  github_repository nlohmann/json\n  options \n    \"json_buildtests off\"\n)", "confidence": "High"}, {"depname": "boost", "version": "1.77.0", "version_op": null, "unified_name": "boost", "extractor_type": "cmake::cpm", "context": "cpmaddpackage(\n  name boost\n  version 1.77.0\n  github_repository \"boostorg/boost\"\n  git_tag \"boost-1.77.0\"\n)", "confidence": "High"}, {"depname": "cxxopts", "version": "2.2.1", "version_op": null, "unified_name": "cxxopts", "extractor_type": "cmake::cpm", "context": "cpmaddpackage(\n  github_repository jarro2783/cxxopts\n  version 2.2.1\n  options \"cxxopts_build_examples no\" \"cxxopts_build_tests no\" \"cxxopts_enable_install yes\"\n)", "confidence": "High"}, {"depname": "lua", "version": "5.3.5", "version_op": null, "unified_name": "lua", "extractor_type": "cmake::cpm", "context": "cpmaddpackage(\n  name lua\n  git_repository https://github.com/lua/lua.git\n  version 5.3.5\n  download_only yes\n)", "confidence": "High"}, {"depname": "catch2", "version": "2.5.0", "version_op": null, "unified_name": "catch2", "extractor_type": "cmake::cpm", "context": "cpmaddpackage(\"gh:catchorg/catch2@2.5.0\")", "confidence": "High"}], "type": "cmake", "libs": []}, {"deps": [{"depname": "powershell", "version": null, "version_op": null, "unified_name": "powershell", "extractor_type": "cmake", "context": "find_package(powershell required)", "confidence": "High"}, {"depname": "qt5core", "version": null, "version_op": null, "unified_name": "qt5core", "extractor_type": "cmake", "context": "find_package(qt5core ${qt_find_package_options})", "confidence": "High"}, {"depname": "${package}", "version": null, "version_op": null, "unified_name": "${package}", "extractor_type": "cmake", "context": "find_package(${package} ${${package}_options})", "confidence": "High"}, {"depname": "${package}", "version": null, "version_op": null, "unified_name": "${package}", "extractor_type": "cmake", "context": "find_package(${package})", "confidence": "High"}, {"depname": "msvc_redist", "version": null, "version_op": null, "unified_name": "msvc_redist", "extractor_type": "cmake", "context": "find_package( msvc_redist )", "confidence": "High"}, {"depname": "nsis", "version": null, "version_op": null, "unified_name": "nsis", "extractor_type": "cmake", "context": "find_package( nsis )", "confidence": "High"}, {"depname": "wix", "version": null, "version_op": null, "unified_name": "wix", "extractor_type": "cmake", "context": "find_package( wix )", "confidence": "High"}, {"depname": "portableapps", "version": null, "version_op": null, "unified_name": "portableapps", "extractor_type": "cmake", "context": "find_package( portableapps )", "confidence": "High"}, {"depname": "wireshark", "version": null, "version_op": null, "unified_name": "wireshark", "extractor_type": "cmake", "context": "project(wireshark c cxx)", "confidence": ""}], "type": "cmake", "libs": [{"filenames": ["applicationservices"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_library (apple_application_services_library applicationservices)"}, {"filenames": ["appkit"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_library (apple_appkit_library appkit)"}, {"filenames": ["corefoundation"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_library (apple_core_foundation_library corefoundation)"}, {"filenames": ["systemconfiguration"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_library (apple_system_configuration_library systemconfiguration)"}, {"filenames": ["ccache"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_program(ccache_executable ccache)"}, {"filenames": ["7z"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_program(zip_executable 7z\n\t\tpath \"$env{programfiles}/7-zip\" \"$env{programw6432}/7-zip\"\n\t\tdoc \"path to the 7z utility.\"\n\t)"}, {"filenames": ["windeployqt"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_program(qt_windeployqt_executable windeployqt\n\t\t\thints \"${_qmake_location}\"\n\t\t\tdoc \"path to the windeployqt utility.\"\n\t\t)"}, {"filenames": ["rpmbuild"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_program(rpmbuild_executable rpmbuild)"}, {"filenames": ["git"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_program(git_executable git)"}, {"filenames": ["bash"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_program(bash_executable bash)"}, {"filenames": ["hardening-check"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_program(hardening_check_executable hardening-check\n\t\tdoc \"path to the hardening-check utility.\"\n\t)"}, {"filenames": ["shellcheck"], "version": "", "fromfile": "tests/test_data/CMakeLists.txt", "content": "find_program(shellcheck_executable shellcheck\n\tdoc \"path to the shellcheck utility.\"\n)"}]}, {"deps": [{"depname": "cxxopts", "version": null, "version_op": null, "unified_name": "cxxopts", "extractor_type": "xmake", "context": "cxxopts", "confidence": "High"}, {"depname": "<PERSON><PERSON><PERSON>", "version": null, "version_op": null, "unified_name": "<PERSON><PERSON><PERSON>", "extractor_type": "xmake", "context": "<PERSON><PERSON><PERSON>", "confidence": "High"}, {"depname": "hopscotch-map", "version": null, "version_op": null, "unified_name": "hopscotch-map", "extractor_type": "xmake", "context": "hopscotch-map", "confidence": "High"}, {"depname": "n<PERSON><PERSON>_json", "version": null, "version_op": null, "unified_name": "n<PERSON><PERSON>_json", "extractor_type": "xmake", "context": "n<PERSON><PERSON>_json", "confidence": "High"}, {"depname": "tl_expected", "version": null, "version_op": null, "unified_name": "tl_expected", "extractor_type": "xmake", "context": "tl_expected", "confidence": "High"}, {"depname": "tl_function_ref", "version": null, "version_op": null, "unified_name": "tl_function_ref", "extractor_type": "xmake", "context": "tl_function_ref", "confidence": "High"}, {"depname": "fmt", "version": null, "version_op": null, "unified_name": "fmt", "extractor_type": "xmake", "context": "fmt", "confidence": "High"}, {"depname": "libcurl", "version": null, "version_op": null, "unified_name": "curl", "extractor_type": "xmake", "context": "libcurl", "confidence": "High"}, {"depname": "nazar<PERSON><PERSON>e", "version": "2021.08.28", "version_op": null, "unified_name": "nazar<PERSON><PERSON>e", "extractor_type": "xmake", "context": "nazaraengine 2021.08.28", "confidence": "High"}, {"depname": "nazaraengine~server", "version": "2021.08.28", "version_op": null, "unified_name": "nazaraengine~server", "extractor_type": "xmake", "context": "nazaraengine~server 2021.08.28", "confidence": "High"}, {"depname": "sol2", "version": "v3.2.1", "version_op": null, "unified_name": "sol2", "extractor_type": "xmake", "context": "sol2 v3.2.1", "confidence": "High"}, {"depname": "stackwalker", "version": "master", "version_op": null, "unified_name": "stackwalker", "extractor_type": "xmake", "context": "stackwalker master", "confidence": "High"}], "type": "xmake"}, {"deps": [{"depname": "ws2_32", "version": null, "version_op": null, "unified_name": "ws2_32", "extractor_type": "ms", "context": "tests/test_data/pthread.vcxproj", "confidence": "High"}, {"depname": "ws2_32", "version": null, "version_op": null, "unified_name": "ws2_32", "extractor_type": "ms", "context": "tests/test_data/pthread.vcxproj", "confidence": "High"}, {"depname": "ws2_32", "version": null, "version_op": null, "unified_name": "ws2_32", "extractor_type": "ms", "context": "tests/test_data/pthread.vcxproj", "confidence": "High"}, {"depname": "ws2_32", "version": null, "version_op": null, "unified_name": "ws2_32", "extractor_type": "ms", "context": "tests/test_data/pthread.vcxproj", "confidence": "High"}, {"depname": "ws2_32", "version": null, "version_op": null, "unified_name": "ws2_32", "extractor_type": "ms", "context": "tests/test_data/pthread.vcxproj", "confidence": "High"}, {"depname": "ws2_32", "version": null, "version_op": null, "unified_name": "ws2_32", "extractor_type": "ms", "context": "tests/test_data/pthread.vcxproj", "confidence": "High"}, {"depname": "ws2_32", "version": null, "version_op": null, "unified_name": "ws2_32", "extractor_type": "ms", "context": "tests/test_data/pthread.vcxproj", "confidence": "High"}, {"depname": "ws2_32", "version": null, "version_op": null, "unified_name": "ws2_32", "extractor_type": "ms", "context": "tests/test_data/pthread.vcxproj", "confidence": "High"}], "type": "ms"}, {"deps": [{"depname": "build2", "version": ">= 0.14.0-", "version_op": null, "unified_name": "build2", "extractor_type": "build2", "context": "tests/test_data/manifest", "confidence": "High"}, {"depname": "bpkg", "version": ">= 0.14.0-", "version_op": null, "unified_name": "bpkg", "extractor_type": "build2", "context": "tests/test_data/manifest", "confidence": "High"}, {"depname": "libodb", "version": "[2.5.0-b.22.1 2.5.0-b.23)", "version_op": null, "unified_name": "odb", "extractor_type": "build2", "context": "tests/test_data/manifest", "confidence": "High"}, {"depname": "libodb-sqlite", "version": "[2.5.0-b.22.1 2.5.0-b.23)", "version_op": null, "unified_name": "odb-sqlite", "extractor_type": "build2", "context": "tests/test_data/manifest", "confidence": "High"}, {"depname": "libsqlite3", "version": "^3.21.0 ; ATTACH in transaction", "version_op": null, "unified_name": "sqlite3", "extractor_type": "build2", "context": "tests/test_data/manifest", "confidence": "High"}, {"depname": "libbutl", "version": "[0.15.0-a.0.1 0.15.0-a.1)", "version_op": null, "unified_name": "butl", "extractor_type": "build2", "context": "tests/test_data/manifest", "confidence": "High"}, {"depname": "libbpkg", "version": "[0.15.0-a.0.1 0.15.0-a.1)", "version_op": null, "unified_name": "bpkg", "extractor_type": "build2", "context": "tests/test_data/manifest", "confidence": "High"}, {"depname": "build2", "version": "[0.15.0-a.0.1 0.15.0-a.1)", "version_op": null, "unified_name": "build2", "extractor_type": "build2", "context": "tests/test_data/manifest", "confidence": "High"}], "type": "build2"}, {"deps": [{"depname": "common", "version": null, "version_op": null, "unified_name": "common", "extractor_type": "bazel", "context": "tests/test_data/bazel.build", "confidence": "High"}, {"depname": "hello-greet", "version": null, "version_op": null, "unified_name": "hello-greet", "extractor_type": "bazel", "context": "tests/test_data/bazel.build:\":hello-greet\"", "confidence": "High"}, {"depname": "hello-greet", "version": null, "version_op": null, "unified_name": "hello-greet", "extractor_type": "bazel", "context": "tests/test_data/bazel.build:\":hello-greet\"", "confidence": "High"}, {"depname": "hello-time", "version": null, "version_op": null, "unified_name": "hello-time", "extractor_type": "bazel", "context": "tests/test_data/bazel.build:\"//lib:hello-time\"", "confidence": "High"}], "type": "bazel"}, {"deps": [{"depname": "krb5", "version": null, "version_op": null, "unified_name": "krb5", "extractor_type": "gitsubmod", "context": "tests/test_data", "confidence": "High"}, {"depname": "engine", "version": null, "version_op": null, "unified_name": "engine", "extractor_type": "gitsubmod", "context": "tests/test_data", "confidence": "High"}, {"depname": "oqs-provider", "version": null, "version_op": null, "unified_name": "oqs-provider", "extractor_type": "gitsubmod", "context": "tests/test_data", "confidence": "High"}], "type": "gitsubmod"}, {"deps": [{"depname": "libdrm", "version": "2.4.60", "version_op": ">=", "unified_name": "drm", "extractor_type": "meson", "context": "tests/test_data/meson.build", "confidence": "High"}, {"depname": "x11", "version": null, "version_op": null, "unified_name": "x11", "extractor_type": "meson", "context": "tests/test_data/meson.build", "confidence": "High"}, {"depname": "xext", "version": null, "version_op": null, "unified_name": "xext", "extractor_type": "meson", "context": "tests/test_data/meson.build", "confidence": "High"}, {"depname": "xfixes", "version": null, "version_op": null, "unified_name": "xfixes", "extractor_type": "meson", "context": "tests/test_data/meson.build", "confidence": "High"}, {"depname": "gl", "version": null, "version_op": null, "unified_name": "gl", "extractor_type": "meson", "context": "tests/test_data/meson.build", "confidence": "High"}, {"depname": "wayland-client", "version": "1.11.0", "version_op": ">=", "unified_name": "wayland-client", "extractor_type": "meson", "context": "tests/test_data/meson.build", "confidence": "High"}], "type": "meson"}, {"deps": [{"depname": "acme-widgets", "version": "^1.4.3", "version_op": null, "unified_name": "acme-widgets", "extractor_type": "dds", "context": "tests/test_data/package.json5", "confidence": "High"}, {"depname": "acme-gizmos", "version": "~5.6.5", "version_op": null, "unified_name": "acme-gizmos", "extractor_type": "dds", "context": "tests/test_data/package.json5", "confidence": "High"}, {"depname": "acme-utils", "version": "^3.3.0", "version_op": null, "unified_name": "acme-utils", "extractor_type": "dds", "context": "tests/test_data/package.json5", "confidence": "High"}], "type": "dds"}]}