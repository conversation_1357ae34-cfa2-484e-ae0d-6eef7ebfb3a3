: 1
name: bpkg
version: 0.15.0-a.0.z
project: build2
summary: build2 package dependency manager
license: MIT
topics: package dependency management, build toolchain
description-file: README
changes-file: NEWS
url: https://build2.org
doc-url: https://build2.org/doc.xhtml
src-url: https://git.build2.org/cgit/bpkg/tree/
email: <EMAIL>
build-warning-email: <EMAIL>
builds: host
requires: c++14
depends: * build2 >= 0.14.0-
depends: * bpkg >= 0.14.0-
# @@ DEP Should probably become conditional dependency.
#requires: ? cli ; Only required if changing .cli files.
depends: libodb [2.5.0-b.22.1 2.5.0-b.23)
depends: libodb-sqlite [2.5.0-b.22.1 2.5.0-b.23)
depends: libsqlite3 ^3.21.0 ; ATTACH in transaction
depends: libbutl [0.15.0-a.0.1 0.15.0-a.1)
depends: libbpkg [0.15.0-a.0.1 0.15.0-a.1)
depends: build2 [0.15.0-a.0.1 0.15.0-a.1)