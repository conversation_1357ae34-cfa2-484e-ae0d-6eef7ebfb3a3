2024-09-05 23:21:26,810 - cxxcrafter -raylib - INFO - Parsing Module Starts
2024-09-05 23:21:39,579 - cxxcrafter -raylib - INFO - Parsing Module Finishes
2024-09-05 23:21:39,579 - cxxcrafter -raylib - INFO - Generation Module Starts
2024-09-05 23:21:39,580 - generation_module -raylib - INFO - Starting Dockerfile generation process...
2024-09-05 23:21:39,580 - generation_module -raylib - INFO - Generating system prompt...
2024-09-05 23:21:39,580 - generation_module -raylib - INFO - Performing inference...
2024-09-05 23:21:47,981 - generation_module -raylib - INFO - Extracting Dockerfile content...
2024-09-05 23:21:54,479 - generation_module -raylib - INFO - Extracting Dockerfile content...
2024-09-05 23:21:54,519 - generation_module -raylib - INFO - Starting Copying the Repo to Dockerfile_Playground
2024-09-05 23:23:12,034 - generation_module -raylib - INFO - Finish Copying
2024-09-05 23:23:12,035 - generation_module -raylib - INFO - Finish generating the initial dockerfile
2024-09-05 23:23:12,035 - cxxcrafter -raylib - INFO - Generation Module Finishes
2024-09-05 23:23:12,035 - cxxcrafter -raylib - INFO - Execution Module Starts
2024-09-05 23:23:15,061 - execution_module.docker_manager -raylib - INFO - Step 1/15 : FROM ubuntu:20.04
2024-09-05 23:23:15,061 - execution_module.docker_manager -raylib - INFO -  ---> 5f5250218d28

2024-09-05 23:23:15,061 - execution_module.docker_manager -raylib - INFO - Step 2/15 : ENV DEBIAN_FRONTEND=noninteractive
2024-09-05 23:23:15,061 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:23:15,061 - execution_module.docker_manager -raylib - INFO -  ---> defc00a448c4

2024-09-05 23:23:15,061 - execution_module.docker_manager -raylib - INFO - Step 3/15 : RUN sed -i 's|http://archive.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> 7134b497e1a1

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO - Step 4/15 : RUN sed -i 's|http://security.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> ea57a31cfcfc

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO - Step 5/15 : RUN apt-get update
2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> 4041e8821132

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO - Step 6/15 : RUN apt-get upgrade -y
2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> afe371a706bb

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO - Step 7/15 : RUN apt-get install -y build-essential
2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> 29b06df4a18c

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO - Step 8/15 : RUN apt-get install -y software-properties-common
2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO -  ---> 760c93dbc806

2024-09-05 23:23:15,062 - execution_module.docker_manager -raylib - INFO - Step 9/15 : RUN apt-get install -y cmake
2024-09-05 23:24:34,472 - execution_module.docker_manager -raylib - INFO -  ---> Running in 4d86ca1c079f

2024-09-05 23:24:35,964 - execution_module.docker_manager -raylib - INFO - Reading package lists...
2024-09-05 23:24:36,568 - execution_module.docker_manager -raylib - INFO - Building dependency tree...
2024-09-05 23:24:36,666 - execution_module.docker_manager -raylib - INFO - 
Reading state information...
2024-09-05 23:24:36,818 - execution_module.docker_manager -raylib - INFO - The following additional packages will be installed:
  cmake-data libarchive13 libcurl4 libjsoncpp1 libnghttp2-14 librhash0

2024-09-05 23:24:36,818 - execution_module.docker_manager -raylib - INFO -   librtmp1 libssh-4 libuv1

2024-09-05 23:24:36,818 - execution_module.docker_manager -raylib - INFO - Suggested packages:
  cmake-doc ninja-build lrzip

2024-09-05 23:24:36,838 - execution_module.docker_manager -raylib - INFO - The following NEW packages will be installed:

2024-09-05 23:24:36,838 - execution_module.docker_manager -raylib - INFO -   cmake cmake-data libarchive13 libcurl4 libjsoncpp1 libnghttp2-14 librhash0

2024-09-05 23:24:36,839 - execution_module.docker_manager -raylib - INFO -   librtmp1 libssh-4 libuv1

2024-09-05 23:24:36,904 - execution_module.docker_manager -raylib - INFO - 0 upgraded, 10 newly installed, 0 to remove and 0 not upgraded.
Need to get 6419 kB of archives.
After this operation, 31.0 MB of additional disk space will be used.
Get:1 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libuv1 amd64 1.34.2-1ubuntu1.5 [80.9 kB]

2024-09-05 23:24:36,974 - execution_module.docker_manager -raylib - INFO - Get:2 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 cmake-data all 3.16.3-1ubuntu1.20.04.1 [1613 kB]

2024-09-05 23:24:37,680 - execution_module.docker_manager -raylib - INFO - Get:3 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libarchive13 amd64 3.4.0-2ubuntu1.2 [327 kB]

2024-09-05 23:24:37,947 - execution_module.docker_manager -raylib - INFO - Get:4 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libnghttp2-14 amd64 1.40.0-1ubuntu0.3 [79.9 kB]

2024-09-05 23:24:38,026 - execution_module.docker_manager -raylib - INFO - Get:5 http://mirrors.aliyun.com/ubuntu focal/main amd64 librtmp1 amd64 2.4+20151223.gitfa8646d.1-2build1 [54.9 kB]

2024-09-05 23:24:38,104 - execution_module.docker_manager -raylib - INFO - Get:6 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libssh-4 amd64 0.9.3-2ubuntu2.5 [171 kB]

2024-09-05 23:24:38,211 - execution_module.docker_manager -raylib - INFO - Get:7 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libcurl4 amd64 7.68.0-1ubuntu2.23 [235 kB]

2024-09-05 23:24:38,427 - execution_module.docker_manager -raylib - INFO - Get:8 http://mirrors.aliyun.com/ubuntu focal/main amd64 libjsoncpp1 amd64 1.7.4-3.1ubuntu2 [75.6 kB]

2024-09-05 23:24:38,480 - execution_module.docker_manager -raylib - INFO - Get:9 http://mirrors.aliyun.com/ubuntu focal/main amd64 librhash0 amd64 1.3.9-1 [113 kB]

2024-09-05 23:24:38,573 - execution_module.docker_manager -raylib - INFO - Get:10 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 cmake amd64 3.16.3-1ubuntu1.20.04.1 [3668 kB]

2024-09-05 23:24:40,336 - execution_module.docker_manager -raylib - INFO - [91mdebconf: delaying package configuration, since apt-utils is not installed
[0m
2024-09-05 23:24:40,408 - execution_module.docker_manager -raylib - INFO - Fetched 6419 kB in 3s (2132 kB/s)

2024-09-05 23:24:40,570 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libuv1:amd64.
(Reading database ... 
2024-09-05 23:24:40,572 - execution_module.docker_manager -raylib - INFO - (Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
2024-09-05 23:24:40,573 - execution_module.docker_manager -raylib - INFO - (Reading database ... 65%
2024-09-05 23:24:40,573 - execution_module.docker_manager -raylib - INFO - (Reading database ... 70%
2024-09-05 23:24:40,574 - execution_module.docker_manager -raylib - INFO - (Reading database ... 75%
2024-09-05 23:24:40,621 - execution_module.docker_manager -raylib - INFO - (Reading database ... 80%
2024-09-05 23:24:40,678 - execution_module.docker_manager -raylib - INFO - (Reading database ... 85%
2024-09-05 23:24:40,701 - execution_module.docker_manager -raylib - INFO - (Reading database ... 90%
2024-09-05 23:24:40,723 - execution_module.docker_manager -raylib - INFO - (Reading database ... 95%
2024-09-05 23:24:40,736 - execution_module.docker_manager -raylib - INFO - (Reading database ... 100%
(Reading database ... 18676 files and directories currently installed.)

2024-09-05 23:24:40,737 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../0-libuv1_1.34.2-1ubuntu1.5_amd64.deb ...

2024-09-05 23:24:40,836 - execution_module.docker_manager -raylib - INFO - Unpacking libuv1:amd64 (1.34.2-1ubuntu1.5) ...

2024-09-05 23:24:41,222 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package cmake-data.

2024-09-05 23:24:41,223 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../1-cmake-data_3.16.3-1ubuntu1.20.04.1_all.deb ...

2024-09-05 23:24:41,267 - execution_module.docker_manager -raylib - INFO - Unpacking cmake-data (3.16.3-1ubuntu1.20.04.1) ...

2024-09-05 23:24:41,782 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libarchive13:amd64.

2024-09-05 23:24:41,787 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../2-libarchive13_3.4.0-2ubuntu1.2_amd64.deb ...

2024-09-05 23:24:41,842 - execution_module.docker_manager -raylib - INFO - Unpacking libarchive13:amd64 (3.4.0-2ubuntu1.2) ...

2024-09-05 23:24:42,194 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libnghttp2-14:amd64.

2024-09-05 23:24:42,196 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../3-libnghttp2-14_1.40.0-1ubuntu0.3_amd64.deb ...

2024-09-05 23:24:42,233 - execution_module.docker_manager -raylib - INFO - Unpacking libnghttp2-14:amd64 (1.40.0-1ubuntu0.3) ...

2024-09-05 23:24:42,520 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package librtmp1:amd64.

2024-09-05 23:24:42,521 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../4-librtmp1_2.4+20151223.gitfa8646d.1-2build1_amd64.deb ...

2024-09-05 23:24:42,569 - execution_module.docker_manager -raylib - INFO - Unpacking librtmp1:amd64 (2.4+20151223.gitfa8646d.1-2build1) ...

2024-09-05 23:24:42,835 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libssh-4:amd64.

2024-09-05 23:24:42,836 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../5-libssh-4_0.9.3-2ubuntu2.5_amd64.deb ...

2024-09-05 23:24:42,879 - execution_module.docker_manager -raylib - INFO - Unpacking libssh-4:amd64 (0.9.3-2ubuntu2.5) ...

2024-09-05 23:24:43,150 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libcurl4:amd64.

2024-09-05 23:24:43,152 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../6-libcurl4_7.68.0-1ubuntu2.23_amd64.deb ...

2024-09-05 23:24:43,187 - execution_module.docker_manager -raylib - INFO - Unpacking libcurl4:amd64 (7.68.0-1ubuntu2.23) ...

2024-09-05 23:24:44,357 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libjsoncpp1:amd64.

2024-09-05 23:24:44,384 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../7-libjsoncpp1_1.7.4-3.1ubuntu2_amd64.deb ...

2024-09-05 23:24:44,625 - execution_module.docker_manager -raylib - INFO - Unpacking libjsoncpp1:amd64 (1.7.4-3.1ubuntu2) ...

2024-09-05 23:24:49,863 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package librhash0:amd64.

2024-09-05 23:24:49,865 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../8-librhash0_1.3.9-1_amd64.deb ...

2024-09-05 23:24:50,129 - execution_module.docker_manager -raylib - INFO - Unpacking librhash0:amd64 (1.3.9-1) ...

2024-09-05 23:24:51,451 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package cmake.

2024-09-05 23:24:51,453 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../9-cmake_3.16.3-1ubuntu1.20.04.1_amd64.deb ...

2024-09-05 23:24:51,659 - execution_module.docker_manager -raylib - INFO - Unpacking cmake (3.16.3-1ubuntu1.20.04.1) ...

2024-09-05 23:24:54,224 - execution_module.docker_manager -raylib - INFO - Setting up libarchive13:amd64 (3.4.0-2ubuntu1.2) ...

2024-09-05 23:24:55,350 - execution_module.docker_manager -raylib - INFO - Setting up libnghttp2-14:amd64 (1.40.0-1ubuntu0.3) ...

2024-09-05 23:24:56,314 - execution_module.docker_manager -raylib - INFO - Setting up libuv1:amd64 (1.34.2-1ubuntu1.5) ...

2024-09-05 23:24:56,925 - execution_module.docker_manager -raylib - INFO - Setting up librtmp1:amd64 (2.4+20151223.gitfa8646d.1-2build1) ...

2024-09-05 23:24:57,512 - execution_module.docker_manager -raylib - INFO - Setting up libssh-4:amd64 (0.9.3-2ubuntu2.5) ...

2024-09-05 23:24:58,423 - execution_module.docker_manager -raylib - INFO - Setting up librhash0:amd64 (1.3.9-1) ...

2024-09-05 23:24:59,006 - execution_module.docker_manager -raylib - INFO - Setting up libcurl4:amd64 (7.68.0-1ubuntu2.23) ...

2024-09-05 23:24:59,325 - execution_module.docker_manager -raylib - INFO - Setting up cmake-data (3.16.3-1ubuntu1.20.04.1) ...

2024-09-05 23:24:59,696 - execution_module.docker_manager -raylib - INFO - Setting up libjsoncpp1:amd64 (1.7.4-3.1ubuntu2) ...

2024-09-05 23:25:00,036 - execution_module.docker_manager -raylib - INFO - Setting up cmake (3.16.3-1ubuntu1.20.04.1) ...

2024-09-05 23:25:00,874 - execution_module.docker_manager -raylib - INFO - Processing triggers for libc-bin (2.31-0ubuntu9.16) ...

2024-09-05 23:26:03,361 - execution_module.docker_manager -raylib - INFO -  ---> 0e6b9331b4ee

2024-09-05 23:26:03,362 - execution_module.docker_manager -raylib - INFO - Step 10/15 : RUN mkdir /tmp/raylib
2024-09-05 23:26:07,046 - execution_module.docker_manager -raylib - INFO -  ---> Running in 5354343848b7

2024-09-05 23:26:16,066 - execution_module.docker_manager -raylib - INFO -  ---> 90025e5cf54f

2024-09-05 23:26:16,066 - execution_module.docker_manager -raylib - INFO - Step 11/15 : COPY ./raylib /tmp/raylib
2024-09-05 23:27:14,915 - execution_module.docker_manager -raylib - INFO -  ---> 9973eb115152

2024-09-05 23:27:14,915 - execution_module.docker_manager -raylib - INFO - Step 12/15 : WORKDIR /tmp/raylib
2024-09-05 23:27:15,402 - execution_module.docker_manager -raylib - INFO -  ---> Running in 78c627e5bbd2

2024-09-05 23:27:16,078 - execution_module.docker_manager -raylib - INFO -  ---> ac48004c3bdc

2024-09-05 23:27:16,079 - execution_module.docker_manager -raylib - INFO - Step 13/15 : RUN cmake -B build
2024-09-05 23:27:16,837 - execution_module.docker_manager -raylib - INFO -  ---> Running in d89830cf0167

2024-09-05 23:27:17,531 - execution_module.docker_manager -raylib - INFO - -- The C compiler identification is GNU 9.4.0

2024-09-05 23:27:17,577 - execution_module.docker_manager -raylib - INFO - -- The CXX compiler identification is GNU 9.4.0

2024-09-05 23:27:17,581 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc

2024-09-05 23:27:17,657 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc -- works

2024-09-05 23:27:17,658 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info

2024-09-05 23:27:17,723 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info - done

2024-09-05 23:27:17,731 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features

2024-09-05 23:27:17,732 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features - done

2024-09-05 23:27:17,733 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++

2024-09-05 23:27:17,796 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++ -- works

2024-09-05 23:27:17,796 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info

2024-09-05 23:27:17,887 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info - done

2024-09-05 23:27:17,896 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features

2024-09-05 23:27:17,897 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features - done

2024-09-05 23:27:17,898 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES

2024-09-05 23:27:17,949 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES - Success

2024-09-05 23:27:17,949 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=pointer-arith can be used -- compiles

2024-09-05 23:27:17,949 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=implicit-function-declaration can be used -- compiles

2024-09-05 23:27:17,949 - execution_module.docker_manager -raylib - INFO - -- Testing if -fno-strict-aliasing can be used -- compiles

2024-09-05 23:27:17,954 - execution_module.docker_manager -raylib - INFO - -- Setting build type to 'Debug' as none was specified.

2024-09-05 23:27:17,954 - execution_module.docker_manager -raylib - INFO - -- Using raylib's GLFW

2024-09-05 23:27:17,956 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h

2024-09-05 23:27:18,012 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h - found

2024-09-05 23:27:18,012 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD

2024-09-05 23:27:18,102 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed

2024-09-05 23:27:18,102 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads

2024-09-05 23:27:18,515 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads - not found

2024-09-05 23:27:18,515 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread

2024-09-05 23:27:18,610 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread - found

2024-09-05 23:27:18,611 - execution_module.docker_manager -raylib - INFO - -- Found Threads: TRUE  

2024-09-05 23:27:18,611 - execution_module.docker_manager -raylib - INFO - -- Including Wayland support
-- Including X11 support

2024-09-05 23:27:18,613 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create

2024-09-05 23:27:18,801 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create - found

2024-09-05 23:27:18,801 - execution_module.docker_manager -raylib - INFO - [91mCMake Error at src/external/glfw/src/CMakeLists.txt:76 (message):
  Failed to find wayland-scanner


[0m
2024-09-05 23:27:18,803 - execution_module.docker_manager -raylib - INFO - -- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".

2024-09-05 23:27:24,643 - cxxcrafter -raylib - INFO - Execution Module Finishes
2024-09-05 23:27:24,643 - cxxcrafter -raylib - ERROR - Execution failed with error: -- Looking for memfd_create
-- Looking for memfd_create - found
[91mCMake Error at src/external/glfw/src/CMakeLists.txt:76 (message):
  Failed to find wayland-scanner


[0m-- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".
The command '/bin/sh -c cmake -B build' returned a non-zero code: 1
2024-09-05 23:27:24,643 - cxxcrafter -raylib - INFO - Modifier Module Starts
2024-09-05 23:27:29,425 - cxxcrafter -raylib - INFO - Modifier Module Finishes
2024-09-05 23:27:29,425 - cxxcrafter -raylib - INFO - Execution Module Starts
2024-09-05 23:27:31,414 - execution_module.docker_manager -raylib - INFO - Step 1/16 : FROM ubuntu:20.04
2024-09-05 23:27:31,414 - execution_module.docker_manager -raylib - INFO -  ---> 5f5250218d28

2024-09-05 23:27:31,415 - execution_module.docker_manager -raylib - INFO - Step 2/16 : ENV DEBIAN_FRONTEND=noninteractive
2024-09-05 23:27:31,415 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:27:31,415 - execution_module.docker_manager -raylib - INFO -  ---> defc00a448c4

2024-09-05 23:27:31,415 - execution_module.docker_manager -raylib - INFO - Step 3/16 : RUN sed -i 's|http://archive.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:27:31,415 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:27:31,415 - execution_module.docker_manager -raylib - INFO -  ---> 7134b497e1a1

2024-09-05 23:27:31,415 - execution_module.docker_manager -raylib - INFO - Step 4/16 : RUN sed -i 's|http://security.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:27:31,415 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:27:31,415 - execution_module.docker_manager -raylib - INFO -  ---> ea57a31cfcfc

2024-09-05 23:27:31,415 - execution_module.docker_manager -raylib - INFO - Step 5/16 : RUN apt-get update
2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO -  ---> 4041e8821132

2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO - Step 6/16 : RUN apt-get upgrade -y
2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO -  ---> afe371a706bb

2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO - Step 7/16 : RUN apt-get install -y build-essential
2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO -  ---> 29b06df4a18c

2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO - Step 8/16 : RUN apt-get install -y software-properties-common
2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO -  ---> 760c93dbc806

2024-09-05 23:27:31,416 - execution_module.docker_manager -raylib - INFO - Step 9/16 : RUN apt-get install -y cmake
2024-09-05 23:27:31,417 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:27:31,417 - execution_module.docker_manager -raylib - INFO -  ---> 0e6b9331b4ee

2024-09-05 23:27:31,417 - execution_module.docker_manager -raylib - INFO - Step 10/16 : RUN apt-get install -y wayland wayland-tools
2024-09-05 23:28:34,019 - execution_module.docker_manager -raylib - INFO -  ---> Running in d97bb1c01a2c

2024-09-05 23:28:35,451 - execution_module.docker_manager -raylib - INFO - Reading package lists...
2024-09-05 23:28:36,224 - execution_module.docker_manager -raylib - INFO - Building dependency tree...
2024-09-05 23:28:36,315 - execution_module.docker_manager -raylib - INFO - 
Reading state information...

2024-09-05 23:28:36,328 - execution_module.docker_manager -raylib - INFO - [91mE: Unable to locate package wayland
E: Unable to locate package wayland-tools
[0m
2024-09-05 23:28:54,105 - cxxcrafter -raylib - INFO - Execution Module Finishes
2024-09-05 23:28:54,105 - cxxcrafter -raylib - ERROR - Execution failed with error: Reading package lists...Building dependency tree...
Reading state information...
[91mE: Unable to locate package wayland
E: Unable to locate package wayland-tools
[0mThe command '/bin/sh -c apt-get install -y wayland wayland-tools' returned a non-zero code: 100
2024-09-05 23:28:54,105 - cxxcrafter -raylib - INFO - Modifier Module Starts
2024-09-05 23:28:59,168 - cxxcrafter -raylib - INFO - Modifier Module Finishes
2024-09-05 23:28:59,168 - cxxcrafter -raylib - INFO - Execution Module Starts
2024-09-05 23:29:59,294 - execution_module.docker_manager -raylib - INFO - Step 1/16 : FROM ubuntu:20.04
2024-09-05 23:29:59,294 - execution_module.docker_manager -raylib - INFO -  ---> 5f5250218d28

2024-09-05 23:29:59,294 - execution_module.docker_manager -raylib - INFO - Step 2/16 : ENV DEBIAN_FRONTEND=noninteractive
2024-09-05 23:29:59,294 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:29:59,295 - execution_module.docker_manager -raylib - INFO -  ---> defc00a448c4

2024-09-05 23:29:59,295 - execution_module.docker_manager -raylib - INFO - Step 3/16 : RUN sed -i 's|http://archive.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:29:59,295 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:29:59,295 - execution_module.docker_manager -raylib - INFO -  ---> 7134b497e1a1

2024-09-05 23:29:59,295 - execution_module.docker_manager -raylib - INFO - Step 4/16 : RUN sed -i 's|http://security.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:29:59,295 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:29:59,295 - execution_module.docker_manager -raylib - INFO -  ---> ea57a31cfcfc

2024-09-05 23:29:59,295 - execution_module.docker_manager -raylib - INFO - Step 5/16 : RUN apt-get update
2024-09-05 23:29:59,295 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:29:59,295 - execution_module.docker_manager -raylib - INFO -  ---> 4041e8821132

2024-09-05 23:29:59,295 - execution_module.docker_manager -raylib - INFO - Step 6/16 : RUN apt-get upgrade -y
2024-09-05 23:29:59,296 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:29:59,296 - execution_module.docker_manager -raylib - INFO -  ---> afe371a706bb

2024-09-05 23:29:59,296 - execution_module.docker_manager -raylib - INFO - Step 7/16 : RUN apt-get install -y build-essential
2024-09-05 23:29:59,296 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:29:59,296 - execution_module.docker_manager -raylib - INFO -  ---> 29b06df4a18c

2024-09-05 23:29:59,296 - execution_module.docker_manager -raylib - INFO - Step 8/16 : RUN apt-get install -y software-properties-common
2024-09-05 23:29:59,296 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:29:59,296 - execution_module.docker_manager -raylib - INFO -  ---> 760c93dbc806

2024-09-05 23:29:59,296 - execution_module.docker_manager -raylib - INFO - Step 9/16 : RUN apt-get install -y cmake
2024-09-05 23:29:59,296 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:29:59,297 - execution_module.docker_manager -raylib - INFO -  ---> 0e6b9331b4ee

2024-09-05 23:29:59,297 - execution_module.docker_manager -raylib - INFO - Step 10/16 : RUN apt-get install -y libwayland-dev wayland-protocols
2024-09-05 23:30:52,759 - execution_module.docker_manager -raylib - INFO -  ---> Running in 3be01933b760

2024-09-05 23:30:54,161 - execution_module.docker_manager -raylib - INFO - Reading package lists...
2024-09-05 23:30:54,936 - execution_module.docker_manager -raylib - INFO - Building dependency tree...
2024-09-05 23:30:55,069 - execution_module.docker_manager -raylib - INFO - 
Reading state information...

2024-09-05 23:30:55,130 - execution_module.docker_manager -raylib - INFO - The following additional packages will be installed:
  libwayland-bin libwayland-client0 libwayland-cursor0 libwayland-egl1

2024-09-05 23:30:55,203 - execution_module.docker_manager -raylib - INFO -   libwayland-server0

2024-09-05 23:30:55,203 - execution_module.docker_manager -raylib - INFO - Suggested packages:
  libwayland-doc

2024-09-05 23:30:55,279 - execution_module.docker_manager -raylib - INFO - The following NEW packages will be installed:
  libwayland-bin libwayland-client0 libwayland-cursor0 libwayland-dev

2024-09-05 23:30:55,279 - execution_module.docker_manager -raylib - INFO -   libwayland-egl1 libwayland-server0 wayland-protocols

2024-09-05 23:30:55,415 - execution_module.docker_manager -raylib - INFO - 0 upgraded, 7 newly installed, 0 to remove and 0 not upgraded.
Need to get 216 kB of archives.
After this operation, 1390 kB of additional disk space will be used.
Get:1 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-client0 amd64 1.18.0-1ubuntu0.1 [23.9 kB]

2024-09-05 23:30:55,484 - execution_module.docker_manager -raylib - INFO - Get:2 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-cursor0 amd64 1.18.0-1ubuntu0.1 [10.3 kB]

2024-09-05 23:30:55,885 - execution_module.docker_manager -raylib - INFO - Get:3 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-egl1 amd64 1.18.0-1ubuntu0.1 [5596 B]

2024-09-05 23:30:55,990 - execution_module.docker_manager -raylib - INFO - Get:4 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-server0 amd64 1.18.0-1ubuntu0.1 [31.3 kB]

2024-09-05 23:30:56,066 - execution_module.docker_manager -raylib - INFO - Get:5 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-bin amd64 1.18.0-1ubuntu0.1 [20.2 kB]

2024-09-05 23:30:56,834 - execution_module.docker_manager -raylib - INFO - Get:6 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-dev amd64 1.18.0-1ubuntu0.1 [64.6 kB]

2024-09-05 23:30:56,953 - execution_module.docker_manager -raylib - INFO - Get:7 http://mirrors.aliyun.com/ubuntu focal/main amd64 wayland-protocols all 1.20-1 [60.3 kB]

2024-09-05 23:30:59,172 - execution_module.docker_manager -raylib - INFO - [91mdebconf: delaying package configuration, since apt-utils is not installed
[0m
2024-09-05 23:31:00,472 - execution_module.docker_manager -raylib - INFO - Fetched 216 kB in 2s (125 kB/s)

2024-09-05 23:31:02,235 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-client0:amd64.
(Reading database ... 
2024-09-05 23:31:02,239 - execution_module.docker_manager -raylib - INFO - (Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
2024-09-05 23:31:02,241 - execution_module.docker_manager -raylib - INFO - (Reading database ... 65%
2024-09-05 23:31:02,242 - execution_module.docker_manager -raylib - INFO - (Reading database ... 70%
2024-09-05 23:31:02,242 - execution_module.docker_manager -raylib - INFO - (Reading database ... 75%
2024-09-05 23:31:02,243 - execution_module.docker_manager -raylib - INFO - (Reading database ... 80%
2024-09-05 23:31:02,243 - execution_module.docker_manager -raylib - INFO - (Reading database ... 85%
2024-09-05 23:31:02,243 - execution_module.docker_manager -raylib - INFO - (Reading database ... 90%
2024-09-05 23:31:02,245 - execution_module.docker_manager -raylib - INFO - (Reading database ... 95%
2024-09-05 23:31:02,245 - execution_module.docker_manager -raylib - INFO - (Reading database ... 100%
(Reading database ... 21508 files and directories currently installed.)

2024-09-05 23:31:02,246 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../0-libwayland-client0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:31:03,111 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:07,780 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-cursor0:amd64.

2024-09-05 23:31:07,782 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../1-libwayland-cursor0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:31:08,487 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:12,667 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-egl1:amd64.

2024-09-05 23:31:12,676 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../2-libwayland-egl1_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:31:12,902 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:14,423 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-server0:amd64.

2024-09-05 23:31:14,446 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../3-libwayland-server0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:31:14,643 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:15,809 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-bin.

2024-09-05 23:31:15,810 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../4-libwayland-bin_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:31:15,999 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:17,162 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-dev:amd64.

2024-09-05 23:31:17,163 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../5-libwayland-dev_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:31:17,430 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:19,330 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package wayland-protocols.

2024-09-05 23:31:19,332 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../6-wayland-protocols_1.20-1_all.deb ...

2024-09-05 23:31:19,463 - execution_module.docker_manager -raylib - INFO - Unpacking wayland-protocols (1.20-1) ...

2024-09-05 23:31:21,276 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:21,780 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:22,299 - execution_module.docker_manager -raylib - INFO - Setting up wayland-protocols (1.20-1) ...

2024-09-05 23:31:22,920 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:23,460 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:23,974 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:24,725 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:31:25,303 - execution_module.docker_manager -raylib - INFO - Processing triggers for libc-bin (2.31-0ubuntu9.16) ...

2024-09-05 23:31:53,416 - execution_module.docker_manager -raylib - INFO -  ---> 6d8e38a0d86b

2024-09-05 23:31:53,416 - execution_module.docker_manager -raylib - INFO - Step 11/16 : RUN mkdir /tmp/raylib
2024-09-05 23:31:53,878 - execution_module.docker_manager -raylib - INFO -  ---> Running in debcf1ab83bb

2024-09-05 23:31:55,597 - execution_module.docker_manager -raylib - INFO -  ---> d9ffbd66a141

2024-09-05 23:31:55,598 - execution_module.docker_manager -raylib - INFO - Step 12/16 : COPY ./raylib /tmp/raylib
2024-09-05 23:32:44,403 - execution_module.docker_manager -raylib - INFO -  ---> 5b54c438776a

2024-09-05 23:32:44,403 - execution_module.docker_manager -raylib - INFO - Step 13/16 : WORKDIR /tmp/raylib
2024-09-05 23:35:10,800 - execution_module.docker_manager -raylib - INFO -  ---> Running in 5c903a558592

2024-09-05 23:35:12,347 - execution_module.docker_manager -raylib - INFO -  ---> 2d388688ec6e

2024-09-05 23:35:12,347 - execution_module.docker_manager -raylib - INFO - Step 14/16 : RUN cmake -B build
2024-09-05 23:35:12,932 - execution_module.docker_manager -raylib - INFO -  ---> Running in d1df0463f9e6

2024-09-05 23:35:14,148 - execution_module.docker_manager -raylib - INFO - -- The C compiler identification is GNU 9.4.0

2024-09-05 23:35:14,199 - execution_module.docker_manager -raylib - INFO - -- The CXX compiler identification is GNU 9.4.0

2024-09-05 23:35:14,214 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc

2024-09-05 23:35:14,274 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc -- works

2024-09-05 23:35:14,275 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info

2024-09-05 23:35:14,331 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info - done

2024-09-05 23:35:14,347 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features

2024-09-05 23:35:14,347 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features - done

2024-09-05 23:35:14,356 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++

2024-09-05 23:35:14,432 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++ -- works

2024-09-05 23:35:14,433 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info

2024-09-05 23:35:14,503 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info - done

2024-09-05 23:35:14,511 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features

2024-09-05 23:35:14,511 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features - done

2024-09-05 23:35:14,512 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES

2024-09-05 23:35:14,572 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES - Success

2024-09-05 23:35:14,572 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=pointer-arith can be used -- compiles

2024-09-05 23:35:14,572 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=implicit-function-declaration can be used -- compiles

2024-09-05 23:35:14,573 - execution_module.docker_manager -raylib - INFO - -- Testing if -fno-strict-aliasing can be used -- compiles

2024-09-05 23:35:14,578 - execution_module.docker_manager -raylib - INFO - -- Setting build type to 'Debug' as none was specified.

2024-09-05 23:35:14,578 - execution_module.docker_manager -raylib - INFO - -- Using raylib's GLFW

2024-09-05 23:35:14,581 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h

2024-09-05 23:35:14,855 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h - found

2024-09-05 23:35:14,855 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD

2024-09-05 23:35:15,089 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed

2024-09-05 23:35:15,090 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads

2024-09-05 23:35:15,158 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads - not found

2024-09-05 23:35:15,159 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread

2024-09-05 23:35:15,249 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread - found

2024-09-05 23:35:15,250 - execution_module.docker_manager -raylib - INFO - -- Found Threads: TRUE  

2024-09-05 23:35:15,250 - execution_module.docker_manager -raylib - INFO - -- Including Wayland support
-- Including X11 support

2024-09-05 23:35:15,252 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create

2024-09-05 23:35:15,373 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create - found

2024-09-05 23:35:15,380 - execution_module.docker_manager -raylib - INFO - -- Could NOT find PkgConfig (missing: PKG_CONFIG_EXECUTABLE) 

2024-09-05 23:35:15,381 - execution_module.docker_manager -raylib - INFO - [91mCMake Error at /usr/share/cmake-3.16/Modules/FindPkgConfig.cmake:511 (message):
  pkg-config tool not found
Call Stack (most recent call first):
  /usr/share/cmake-3.16/Modules/FindPkgConfig.cmake:643 (_pkg_check_modules_internal)
  src/external/glfw/src/CMakeLists.txt:163 (pkg_check_modules)


[0m
2024-09-05 23:35:15,418 - execution_module.docker_manager -raylib - INFO - [91mCMake Error at /usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake:146 (message):
  Could NOT find X11 (missing: X11_X11_INCLUDE_PATH X11_X11_LIB)
Call Stack (most recent call first):
  /usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake:393 (_FPHSA_FAILURE_MESSAGE)
  /usr/share/cmake-3.16/Modules/FindX11.cmake:366 (find_package_handle_standard_args)
  src/external/glfw/src/CMakeLists.txt:181 (find_package)


[0m
2024-09-05 23:35:15,419 - execution_module.docker_manager -raylib - INFO - -- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".

2024-09-05 23:35:15,833 - cxxcrafter -raylib - INFO - Execution Module Finishes
2024-09-05 23:35:15,834 - cxxcrafter -raylib - ERROR - Execution failed with error: -- Could NOT find PkgConfig (missing: PKG_CONFIG_EXECUTABLE) 
[91mCMake Error at /usr/share/cmake-3.16/Modules/FindPkgConfig.cmake:511 (message):
  pkg-config tool not found
Call Stack (most recent call first):
  /usr/share/cmake-3.16/Modules/FindPkgConfig.cmake:643 (_pkg_check_modules_internal)
  src/external/glfw/src/CMakeLists.txt:163 (pkg_check_modules)


[0m[91mCMake Error at /usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake:146 (message):
  Could NOT find X11 (missing: X11_X11_INCLUDE_PATH X11_X11_LIB)
Call Stack (most recent call first):
  /usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake:393 (_FPHSA_FAILURE_MESSAGE)
  /usr/share/cmake-3.16/Modules/FindX11.cmake:366 (find_package_handle_standard_args)
  src/external/glfw/src/CMakeLists.txt:181 (find_package)


[0m-- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".
The command '/bin/sh -c cmake -B build' returned a non-zero code: 1
2024-09-05 23:35:15,834 - cxxcrafter -raylib - INFO - Modifier Module Starts
2024-09-05 23:35:23,813 - cxxcrafter -raylib - INFO - Modifier Module Finishes
2024-09-05 23:35:23,813 - cxxcrafter -raylib - INFO - Execution Module Starts
2024-09-05 23:35:25,273 - execution_module.docker_manager -raylib - INFO - Step 1/17 : FROM ubuntu:20.04
2024-09-05 23:35:25,397 - execution_module.docker_manager -raylib - INFO -  ---> 5f5250218d28

2024-09-05 23:35:25,409 - execution_module.docker_manager -raylib - INFO - Step 2/17 : ENV DEBIAN_FRONTEND=noninteractive
2024-09-05 23:35:25,749 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:35:25,749 - execution_module.docker_manager -raylib - INFO -  ---> defc00a448c4

2024-09-05 23:35:25,749 - execution_module.docker_manager -raylib - INFO - Step 3/17 : RUN sed -i 's|http://archive.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:35:25,750 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:35:25,750 - execution_module.docker_manager -raylib - INFO -  ---> 7134b497e1a1

2024-09-05 23:35:25,750 - execution_module.docker_manager -raylib - INFO - Step 4/17 : RUN sed -i 's|http://security.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:35:25,750 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:35:25,750 - execution_module.docker_manager -raylib - INFO -  ---> ea57a31cfcfc

2024-09-05 23:35:25,750 - execution_module.docker_manager -raylib - INFO - Step 5/17 : RUN apt-get update
2024-09-05 23:35:25,750 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:35:25,750 - execution_module.docker_manager -raylib - INFO -  ---> 4041e8821132

2024-09-05 23:35:25,750 - execution_module.docker_manager -raylib - INFO - Step 6/17 : RUN apt-get upgrade -y
2024-09-05 23:35:25,750 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:35:25,750 - execution_module.docker_manager -raylib - INFO -  ---> afe371a706bb

2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO - Step 7/17 : RUN apt-get install -y build-essential
2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO -  ---> 29b06df4a18c

2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO - Step 8/17 : RUN apt-get install -y software-properties-common
2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO -  ---> 760c93dbc806

2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO - Step 9/17 : RUN apt-get install -y cmake
2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO -  ---> 0e6b9331b4ee

2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO - Step 10/17 : RUN apt-get install -y libwayland-dev wayland-protocols
2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO -  ---> 6d8e38a0d86b

2024-09-05 23:35:25,751 - execution_module.docker_manager -raylib - INFO - Step 11/17 : RUN apt-get install -y pkg-config libx11-dev
2024-09-05 23:36:22,758 - execution_module.docker_manager -raylib - INFO -  ---> Running in e5d1208991a4

2024-09-05 23:36:23,946 - execution_module.docker_manager -raylib - INFO - Reading package lists...
2024-09-05 23:36:24,695 - execution_module.docker_manager -raylib - INFO - Building dependency tree...
2024-09-05 23:36:24,809 - execution_module.docker_manager -raylib - INFO - 
Reading state information...
2024-09-05 23:36:24,924 - execution_module.docker_manager -raylib - INFO - The following additional packages will be installed:

2024-09-05 23:36:24,944 - execution_module.docker_manager -raylib - INFO -   libbsd0 libpthread-stubs0-dev libx11-6 libx11-data libxau-dev libxau6

2024-09-05 23:36:24,944 - execution_module.docker_manager -raylib - INFO -   libxcb1 libxcb1-dev libxdmcp-dev libxdmcp6 x11proto-core-dev x11proto-dev
  xorg-sgml-doctools xtrans-dev

2024-09-05 23:36:24,944 - execution_module.docker_manager -raylib - INFO - Suggested packages:
  libx11-doc libxcb-doc

2024-09-05 23:36:25,016 - execution_module.docker_manager -raylib - INFO - The following NEW packages will be installed:

2024-09-05 23:36:25,016 - execution_module.docker_manager -raylib - INFO -   libbsd0 libpthread-stubs0-dev libx11-6 libx11-data libx11-dev libxau-dev

2024-09-05 23:36:25,017 - execution_module.docker_manager -raylib - INFO -   libxau6 libxcb1 libxcb1-dev libxdmcp-dev libxdmcp6 pkg-config
  x11proto-core-dev x11proto-dev xorg-sgml-doctools xtrans-dev

2024-09-05 23:36:25,069 - execution_module.docker_manager -raylib - INFO - 0 upgraded, 16 newly installed, 0 to remove and 0 not upgraded.
Need to get 2290 kB of archives.
After this operation, 9197 kB of additional disk space will be used.
Get:1 http://mirrors.aliyun.com/ubuntu focal/main amd64 libbsd0 amd64 0.10.0-1 [45.4 kB]

2024-09-05 23:36:25,126 - execution_module.docker_manager -raylib - INFO - Get:2 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau6 amd64 1:1.0.9-0ubuntu1 [7488 B]

2024-09-05 23:36:25,172 - execution_module.docker_manager -raylib - INFO - Get:3 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp6 amd64 1:1.1.3-0ubuntu1 [10.6 kB]

2024-09-05 23:36:25,217 - execution_module.docker_manager -raylib - INFO - Get:4 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1 amd64 1.14-2 [44.7 kB]

2024-09-05 23:36:25,378 - execution_module.docker_manager -raylib - INFO - Get:5 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-data all 2:1.6.9-2ubuntu1.6 [114 kB]

2024-09-05 23:36:25,468 - execution_module.docker_manager -raylib - INFO - Get:6 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-6 amd64 2:1.6.9-2ubuntu1.6 [577 kB]

2024-09-05 23:36:25,721 - execution_module.docker_manager -raylib - INFO - Get:7 http://mirrors.aliyun.com/ubuntu focal/main amd64 libpthread-stubs0-dev amd64 0.4-1 [5384 B]

2024-09-05 23:36:25,855 - execution_module.docker_manager -raylib - INFO - Get:8 http://mirrors.aliyun.com/ubuntu focal/main amd64 xorg-sgml-doctools all 1:1.11-1 [12.9 kB]

2024-09-05 23:36:25,893 - execution_module.docker_manager -raylib - INFO - Get:9 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-dev all 2019.2-1ubuntu1 [594 kB]

2024-09-05 23:36:26,104 - execution_module.docker_manager -raylib - INFO - Get:10 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-core-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-05 23:36:26,181 - execution_module.docker_manager -raylib - INFO - Get:11 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau-dev amd64 1:1.0.9-0ubuntu1 [9552 B]

2024-09-05 23:36:26,282 - execution_module.docker_manager -raylib - INFO - Get:12 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp-dev amd64 1:1.1.3-0ubuntu1 [25.3 kB]

2024-09-05 23:36:26,398 - execution_module.docker_manager -raylib - INFO - Get:13 http://mirrors.aliyun.com/ubuntu focal/main amd64 xtrans-dev all 1.4.0-1 [68.9 kB]

2024-09-05 23:36:26,626 - execution_module.docker_manager -raylib - INFO - Get:14 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1-dev amd64 1.14-2 [80.5 kB]

2024-09-05 23:36:26,793 - execution_module.docker_manager -raylib - INFO - Get:15 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-dev amd64 2:1.6.9-2ubuntu1.6 [648 kB]

2024-09-05 23:36:27,035 - execution_module.docker_manager -raylib - INFO - Get:16 http://mirrors.aliyun.com/ubuntu focal/main amd64 pkg-config amd64 0.29.1-0ubuntu4 [45.5 kB]

2024-09-05 23:36:27,407 - execution_module.docker_manager -raylib - INFO - [91mdebconf: delaying package configuration, since apt-utils is not installed
[0m
2024-09-05 23:36:27,771 - execution_module.docker_manager -raylib - INFO - Fetched 2290 kB in 2s (1128 kB/s)

2024-09-05 23:36:28,169 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libbsd0:amd64.
(Reading database ... 
2024-09-05 23:36:28,175 - execution_module.docker_manager -raylib - INFO - (Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
2024-09-05 23:36:28,184 - execution_module.docker_manager -raylib - INFO - (Reading database ... 65%
2024-09-05 23:36:28,192 - execution_module.docker_manager -raylib - INFO - (Reading database ... 70%
2024-09-05 23:36:28,208 - execution_module.docker_manager -raylib - INFO - (Reading database ... 75%
2024-09-05 23:36:28,209 - execution_module.docker_manager -raylib - INFO - (Reading database ... 80%
2024-09-05 23:36:28,210 - execution_module.docker_manager -raylib - INFO - (Reading database ... 85%
2024-09-05 23:36:28,210 - execution_module.docker_manager -raylib - INFO - (Reading database ... 90%
2024-09-05 23:36:28,213 - execution_module.docker_manager -raylib - INFO - (Reading database ... 95%
2024-09-05 23:36:28,214 - execution_module.docker_manager -raylib - INFO - (Reading database ... 100%
(Reading database ... 
2024-09-05 23:36:28,214 - execution_module.docker_manager -raylib - INFO - 21613 files and directories currently installed.)

2024-09-05 23:36:28,215 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../00-libbsd0_0.10.0-1_amd64.deb ...

2024-09-05 23:36:28,684 - execution_module.docker_manager -raylib - INFO - Unpacking libbsd0:amd64 (0.10.0-1) ...

2024-09-05 23:36:29,610 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau6:amd64.
2024-09-05 23:36:29,610 - execution_module.docker_manager -raylib - INFO - 

2024-09-05 23:36:29,612 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../01-libxau6_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-05 23:36:29,811 - execution_module.docker_manager -raylib - INFO - Unpacking libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:36:30,556 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp6:amd64.

2024-09-05 23:36:30,559 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../02-libxdmcp6_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-05 23:36:30,603 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:36:31,487 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1:amd64.

2024-09-05 23:36:31,488 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../03-libxcb1_1.14-2_amd64.deb ...

2024-09-05 23:36:31,718 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1:amd64 (1.14-2) ...

2024-09-05 23:36:32,309 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-data.

2024-09-05 23:36:32,311 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../04-libx11-data_2%3a1.6.9-2ubuntu1.6_all.deb ...

2024-09-05 23:36:32,544 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:36:33,718 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-6:amd64.

2024-09-05 23:36:33,719 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../05-libx11-6_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-05 23:36:33,912 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:36:34,407 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libpthread-stubs0-dev:amd64.

2024-09-05 23:36:34,409 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../06-libpthread-stubs0-dev_0.4-1_amd64.deb ...

2024-09-05 23:36:34,495 - execution_module.docker_manager -raylib - INFO - Unpacking libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-05 23:36:34,694 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xorg-sgml-doctools.

2024-09-05 23:36:34,696 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../07-xorg-sgml-doctools_1%3a1.11-1_all.deb ...

2024-09-05 23:36:34,733 - execution_module.docker_manager -raylib - INFO - Unpacking xorg-sgml-doctools (1:1.11-1) ...

2024-09-05 23:36:35,256 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-dev.

2024-09-05 23:36:35,284 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../08-x11proto-dev_2019.2-1ubuntu1_all.deb ...

2024-09-05 23:36:35,431 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-dev (2019.2-1ubuntu1) ...

2024-09-05 23:36:36,661 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-core-dev.

2024-09-05 23:36:36,664 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../09-x11proto-core-dev_2019.2-1ubuntu1_all.deb ...

2024-09-05 23:36:36,692 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-05 23:36:36,912 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau-dev:amd64.

2024-09-05 23:36:36,915 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../10-libxau-dev_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-05 23:36:36,953 - execution_module.docker_manager -raylib - INFO - Unpacking libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:36:37,172 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp-dev:amd64.

2024-09-05 23:36:37,174 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../11-libxdmcp-dev_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-05 23:36:37,221 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:36:37,400 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xtrans-dev.

2024-09-05 23:36:37,402 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../12-xtrans-dev_1.4.0-1_all.deb ...

2024-09-05 23:36:37,441 - execution_module.docker_manager -raylib - INFO - Unpacking xtrans-dev (1.4.0-1) ...

2024-09-05 23:36:37,641 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1-dev:amd64.

2024-09-05 23:36:37,646 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../13-libxcb1-dev_1.14-2_amd64.deb ...

2024-09-05 23:36:37,701 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1-dev:amd64 (1.14-2) ...

2024-09-05 23:36:37,962 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-dev:amd64.

2024-09-05 23:36:37,965 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../14-libx11-dev_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-05 23:36:38,004 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:36:38,407 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package pkg-config.
Preparing to unpack .../15-pkg-config_0.29.1-0ubuntu4_amd64.deb ...

2024-09-05 23:36:38,464 - execution_module.docker_manager -raylib - INFO - Unpacking pkg-config (0.29.1-0ubuntu4) ...

2024-09-05 23:36:38,793 - execution_module.docker_manager -raylib - INFO - Setting up libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:36:38,915 - execution_module.docker_manager -raylib - INFO - Setting up libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-05 23:36:39,033 - execution_module.docker_manager -raylib - INFO - Setting up xtrans-dev (1.4.0-1) ...

2024-09-05 23:36:39,293 - execution_module.docker_manager -raylib - INFO - Setting up libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:36:39,430 - execution_module.docker_manager -raylib - INFO - Setting up pkg-config (0.29.1-0ubuntu4) ...

2024-09-05 23:36:39,765 - execution_module.docker_manager -raylib - INFO - Setting up xorg-sgml-doctools (1:1.11-1) ...

2024-09-05 23:36:39,886 - execution_module.docker_manager -raylib - INFO - Setting up libbsd0:amd64 (0.10.0-1) ...

2024-09-05 23:36:39,996 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-dev (2019.2-1ubuntu1) ...

2024-09-05 23:36:40,117 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:36:40,215 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1:amd64 (1.14-2) ...

2024-09-05 23:36:40,319 - execution_module.docker_manager -raylib - INFO - Setting up libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:36:40,431 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:36:40,565 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-05 23:36:40,735 - execution_module.docker_manager -raylib - INFO - Setting up libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:36:40,868 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1-dev:amd64 (1.14-2) ...

2024-09-05 23:36:40,968 - execution_module.docker_manager -raylib - INFO - Setting up libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:36:41,077 - execution_module.docker_manager -raylib - INFO - Processing triggers for libc-bin (2.31-0ubuntu9.16) ...

2024-09-05 23:39:07,236 - execution_module.docker_manager -raylib - INFO -  ---> eb1a5ae06b7b

2024-09-05 23:39:07,237 - execution_module.docker_manager -raylib - INFO - Step 12/17 : RUN mkdir /tmp/raylib
2024-09-05 23:39:08,858 - execution_module.docker_manager -raylib - INFO -  ---> Running in 5e5a286fa711

2024-09-05 23:39:27,970 - execution_module.docker_manager -raylib - INFO -  ---> 8cb3c3ec6f45

2024-09-05 23:39:27,970 - execution_module.docker_manager -raylib - INFO - Step 13/17 : COPY ./raylib /tmp/raylib
2024-09-05 23:40:09,126 - execution_module.docker_manager -raylib - INFO -  ---> ed73b515cf75

2024-09-05 23:40:09,126 - execution_module.docker_manager -raylib - INFO - Step 14/17 : WORKDIR /tmp/raylib
2024-09-05 23:40:10,445 - execution_module.docker_manager -raylib - INFO -  ---> Running in 6c587aafa484

2024-09-05 23:40:43,308 - execution_module.docker_manager -raylib - INFO -  ---> 7b9defeccd81

2024-09-05 23:40:43,308 - execution_module.docker_manager -raylib - INFO - Step 15/17 : RUN cmake -B build
2024-09-05 23:40:52,449 - execution_module.docker_manager -raylib - INFO -  ---> Running in 145b96f23f30

2024-09-05 23:40:53,807 - execution_module.docker_manager -raylib - INFO - -- The C compiler identification is GNU 9.4.0

2024-09-05 23:40:53,847 - execution_module.docker_manager -raylib - INFO - -- The CXX compiler identification is GNU 9.4.0

2024-09-05 23:40:53,851 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc

2024-09-05 23:40:54,335 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc -- works

2024-09-05 23:40:54,336 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info

2024-09-05 23:40:54,460 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info - done

2024-09-05 23:40:54,466 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features

2024-09-05 23:40:54,467 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features - done

2024-09-05 23:40:54,468 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++

2024-09-05 23:40:54,580 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++ -- works

2024-09-05 23:40:54,581 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info

2024-09-05 23:40:54,692 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info - done

2024-09-05 23:40:54,700 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features

2024-09-05 23:40:54,700 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features - done

2024-09-05 23:40:54,701 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES

2024-09-05 23:40:54,954 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES - Success

2024-09-05 23:40:54,954 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=pointer-arith can be used -- compiles

2024-09-05 23:40:54,954 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=implicit-function-declaration can be used -- compiles

2024-09-05 23:40:54,954 - execution_module.docker_manager -raylib - INFO - -- Testing if -fno-strict-aliasing can be used -- compiles

2024-09-05 23:40:54,962 - execution_module.docker_manager -raylib - INFO - -- Setting build type to 'Debug' as none was specified.

2024-09-05 23:40:54,962 - execution_module.docker_manager -raylib - INFO - -- Using raylib's GLFW

2024-09-05 23:40:54,965 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h

2024-09-05 23:40:55,034 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h - found

2024-09-05 23:40:55,035 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD

2024-09-05 23:40:55,253 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed

2024-09-05 23:40:55,254 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads

2024-09-05 23:40:55,459 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads - not found

2024-09-05 23:40:55,459 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread

2024-09-05 23:40:55,527 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread - found

2024-09-05 23:40:55,528 - execution_module.docker_manager -raylib - INFO - -- Found Threads: TRUE  

2024-09-05 23:40:55,528 - execution_module.docker_manager -raylib - INFO - -- Including Wayland support
-- Including X11 support

2024-09-05 23:40:55,530 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create

2024-09-05 23:40:55,583 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create - found

2024-09-05 23:40:55,587 - execution_module.docker_manager -raylib - INFO - -- Found PkgConfig: /usr/bin/pkg-config (found version "0.29.1") 

2024-09-05 23:40:55,596 - execution_module.docker_manager -raylib - INFO - -- Checking for modules 'wayland-client>=0.2.7;wayland-cursor>=0.2.7;wayland-egl>=0.2.7;xkbcommon>=0.5.0'

2024-09-05 23:40:55,596 - execution_module.docker_manager -raylib - INFO - --   No package 'xkbcommon' found

2024-09-05 23:40:55,596 - execution_module.docker_manager -raylib - INFO - [91mCMake Error at /usr/share/cmake-3.16/Modules/FindPkgConfig.cmake:463 (message):
  A required package was not found
Call Stack (most recent call first):
  /usr/share/cmake-3.16/Modules/FindPkgConfig.cmake:643 (_pkg_check_modules_internal)
  src/external/glfw/src/CMakeLists.txt:163 (pkg_check_modules)


[0m
2024-09-05 23:40:55,596 - execution_module.docker_manager -raylib - INFO - -- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".

2024-09-05 23:40:56,828 - cxxcrafter -raylib - INFO - Execution Module Finishes
2024-09-05 23:40:56,828 - cxxcrafter -raylib - ERROR - Execution failed with error: -- Checking for modules 'wayland-client>=0.2.7;wayland-cursor>=0.2.7;wayland-egl>=0.2.7;xkbcommon>=0.5.0'
--   No package 'xkbcommon' found
[91mCMake Error at /usr/share/cmake-3.16/Modules/FindPkgConfig.cmake:463 (message):
  A required package was not found
Call Stack (most recent call first):
  /usr/share/cmake-3.16/Modules/FindPkgConfig.cmake:643 (_pkg_check_modules_internal)
  src/external/glfw/src/CMakeLists.txt:163 (pkg_check_modules)


[0m-- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".
The command '/bin/sh -c cmake -B build' returned a non-zero code: 1
2024-09-05 23:40:56,829 - cxxcrafter -raylib - INFO - Modifier Module Starts
2024-09-05 23:41:05,637 - cxxcrafter -raylib - INFO - Modifier Module Finishes
2024-09-05 23:41:05,637 - cxxcrafter -raylib - INFO - Execution Module Starts
2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO - Step 1/16 : FROM ubuntu:20.04
2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO -  ---> 5f5250218d28

2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO - Step 2/16 : ENV DEBIAN_FRONTEND=noninteractive
2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO -  ---> defc00a448c4

2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO - Step 3/16 : RUN sed -i 's|http://archive.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO -  ---> 7134b497e1a1

2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO - Step 4/16 : RUN sed -i 's|http://security.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO -  ---> ea57a31cfcfc

2024-09-05 23:41:07,559 - execution_module.docker_manager -raylib - INFO - Step 5/16 : RUN apt-get update
2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO -  ---> 4041e8821132

2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO - Step 6/16 : RUN apt-get upgrade -y
2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO -  ---> afe371a706bb

2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO - Step 7/16 : RUN apt-get install -y build-essential
2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO -  ---> 29b06df4a18c

2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO - Step 8/16 : RUN apt-get install -y software-properties-common
2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO -  ---> 760c93dbc806

2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO - Step 9/16 : RUN apt-get install -y cmake
2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO -  ---> 0e6b9331b4ee

2024-09-05 23:41:07,560 - execution_module.docker_manager -raylib - INFO - Step 10/16 : RUN apt-get install -y libwayland-dev wayland-protocols pkg-config libx11-dev libxkbcommon-dev
2024-09-05 23:41:34,549 - execution_module.docker_manager -raylib - INFO -  ---> Running in b35100d0cb05

2024-09-05 23:41:35,355 - execution_module.docker_manager -raylib - INFO - Reading package lists...
2024-09-05 23:41:36,241 - execution_module.docker_manager -raylib - INFO - Building dependency tree...
2024-09-05 23:41:36,353 - execution_module.docker_manager -raylib - INFO - 
Reading state information...
2024-09-05 23:41:36,463 - execution_module.docker_manager -raylib - INFO - The following additional packages will be installed:

2024-09-05 23:41:36,483 - execution_module.docker_manager -raylib - INFO -   libbsd0 libpthread-stubs0-dev libwayland-bin libwayland-client0
  libwayland-cursor0 libwayland-egl1 libwayland-server0 libx11-6 libx11-data

2024-09-05 23:41:36,483 - execution_module.docker_manager -raylib - INFO -   libxau-dev libxau6 libxcb1 libxcb1-dev libxdmcp-dev libxdmcp6 libxkbcommon0
  x11proto-core-dev x11proto-dev xkb-data xorg-sgml-doctools xtrans-dev

2024-09-05 23:41:36,483 - execution_module.docker_manager -raylib - INFO - Suggested packages:
  libwayland-doc libx11-doc libxcb-doc

2024-09-05 23:41:36,518 - execution_module.docker_manager -raylib - INFO - The following NEW packages will be installed:

2024-09-05 23:41:36,518 - execution_module.docker_manager -raylib - INFO -   libbsd0 libpthread-stubs0-dev libwayland-bin libwayland-client0
  libwayland-cursor0 libwayland-dev libwayland-egl1 libwayland-server0
  libx11-6 libx11-data libx11-dev libxau-dev libxau6 libxcb1 libxcb1-dev

2024-09-05 23:41:36,518 - execution_module.docker_manager -raylib - INFO -   libxdmcp-dev libxdmcp6 libxkbcommon-dev libxkbcommon0 pkg-config
  wayland-protocols x11proto-core-dev x11proto-dev xkb-data xorg-sgml-doctools
  xtrans-dev

2024-09-05 23:41:36,564 - execution_module.docker_manager -raylib - INFO - 0 upgraded, 26 newly installed, 0 to remove and 0 not upgraded.
Need to get 2999 kB of archives.
After this operation, 15.3 MB of additional disk space will be used.
Get:1 http://mirrors.aliyun.com/ubuntu focal/main amd64 libbsd0 amd64 0.10.0-1 [45.4 kB]

2024-09-05 23:41:36,619 - execution_module.docker_manager -raylib - INFO - Get:2 http://mirrors.aliyun.com/ubuntu focal/main amd64 xkb-data all 2.29-2 [349 kB]

2024-09-05 23:41:36,850 - execution_module.docker_manager -raylib - INFO - Get:3 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau6 amd64 1:1.0.9-0ubuntu1 [7488 B]

2024-09-05 23:41:36,917 - execution_module.docker_manager -raylib - INFO - Get:4 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp6 amd64 1:1.1.3-0ubuntu1 [10.6 kB]

2024-09-05 23:41:36,969 - execution_module.docker_manager -raylib - INFO - Get:5 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1 amd64 1.14-2 [44.7 kB]

2024-09-05 23:41:37,130 - execution_module.docker_manager -raylib - INFO - Get:6 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-data all 2:1.6.9-2ubuntu1.6 [114 kB]

2024-09-05 23:41:37,278 - execution_module.docker_manager -raylib - INFO - Get:7 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-6 amd64 2:1.6.9-2ubuntu1.6 [577 kB]

2024-09-05 23:41:37,452 - execution_module.docker_manager -raylib - INFO - Get:8 http://mirrors.aliyun.com/ubuntu focal/main amd64 libpthread-stubs0-dev amd64 0.4-1 [5384 B]

2024-09-05 23:41:37,495 - execution_module.docker_manager -raylib - INFO - Get:9 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-client0 amd64 1.18.0-1ubuntu0.1 [23.9 kB]

2024-09-05 23:41:37,582 - execution_module.docker_manager -raylib - INFO - Get:10 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-cursor0 amd64 1.18.0-1ubuntu0.1 [10.3 kB]

2024-09-05 23:41:37,920 - execution_module.docker_manager -raylib - INFO - Get:11 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-egl1 amd64 1.18.0-1ubuntu0.1 [5596 B]

2024-09-05 23:41:37,974 - execution_module.docker_manager -raylib - INFO - Get:12 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-server0 amd64 1.18.0-1ubuntu0.1 [31.3 kB]

2024-09-05 23:41:38,020 - execution_module.docker_manager -raylib - INFO - Get:13 http://mirrors.aliyun.com/ubuntu focal/main amd64 xorg-sgml-doctools all 1:1.11-1 [12.9 kB]

2024-09-05 23:41:38,072 - execution_module.docker_manager -raylib - INFO - Get:14 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-dev all 2019.2-1ubuntu1 [594 kB]

2024-09-05 23:41:38,422 - execution_module.docker_manager -raylib - INFO - Get:15 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-core-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-05 23:41:38,479 - execution_module.docker_manager -raylib - INFO - Get:16 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau-dev amd64 1:1.0.9-0ubuntu1 [9552 B]

2024-09-05 23:41:38,520 - execution_module.docker_manager -raylib - INFO - Get:17 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp-dev amd64 1:1.1.3-0ubuntu1 [25.3 kB]

2024-09-05 23:41:38,930 - execution_module.docker_manager -raylib - INFO - Get:18 http://mirrors.aliyun.com/ubuntu focal/main amd64 xtrans-dev all 1.4.0-1 [68.9 kB]

2024-09-05 23:41:39,131 - execution_module.docker_manager -raylib - INFO - Get:19 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1-dev amd64 1.14-2 [80.5 kB]

2024-09-05 23:41:39,250 - execution_module.docker_manager -raylib - INFO - Get:20 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-dev amd64 2:1.6.9-2ubuntu1.6 [648 kB]

2024-09-05 23:41:39,531 - execution_module.docker_manager -raylib - INFO - Get:21 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon0 amd64 0.10.0-1 [98.4 kB]

2024-09-05 23:41:39,643 - execution_module.docker_manager -raylib - INFO - Get:22 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon-dev amd64 0.10.0-1 [45.4 kB]

2024-09-05 23:41:39,702 - execution_module.docker_manager -raylib - INFO - Get:23 http://mirrors.aliyun.com/ubuntu focal/main amd64 pkg-config amd64 0.29.1-0ubuntu4 [45.5 kB]

2024-09-05 23:41:39,756 - execution_module.docker_manager -raylib - INFO - Get:24 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-bin amd64 1.18.0-1ubuntu0.1 [20.2 kB]

2024-09-05 23:41:39,801 - execution_module.docker_manager -raylib - INFO - Get:25 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-dev amd64 1.18.0-1ubuntu0.1 [64.6 kB]

2024-09-05 23:41:39,860 - execution_module.docker_manager -raylib - INFO - Get:26 http://mirrors.aliyun.com/ubuntu focal/main amd64 wayland-protocols all 1.20-1 [60.3 kB]

2024-09-05 23:41:40,058 - execution_module.docker_manager -raylib - INFO - [91mdebconf: delaying package configuration, since apt-utils is not installed
[0m
2024-09-05 23:41:40,115 - execution_module.docker_manager -raylib - INFO - Fetched 2999 kB in 3s (893 kB/s)

2024-09-05 23:41:40,265 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libbsd0:amd64.
(Reading database ... 
2024-09-05 23:41:40,267 - execution_module.docker_manager -raylib - INFO - (Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
2024-09-05 23:41:40,267 - execution_module.docker_manager -raylib - INFO - (Reading database ... 55%
(Reading database ... 60%
2024-09-05 23:41:40,267 - execution_module.docker_manager -raylib - INFO - (Reading database ... 65%
2024-09-05 23:41:40,268 - execution_module.docker_manager -raylib - INFO - (Reading database ... 70%
2024-09-05 23:41:40,268 - execution_module.docker_manager -raylib - INFO - (Reading database ... 75%
2024-09-05 23:41:40,329 - execution_module.docker_manager -raylib - INFO - (Reading database ... 80%
2024-09-05 23:41:40,371 - execution_module.docker_manager -raylib - INFO - (Reading database ... 85%
2024-09-05 23:41:40,397 - execution_module.docker_manager -raylib - INFO - (Reading database ... 90%
2024-09-05 23:41:40,419 - execution_module.docker_manager -raylib - INFO - (Reading database ... 95%
2024-09-05 23:41:40,422 - execution_module.docker_manager -raylib - INFO - (Reading database ... 100%
(Reading database ... 21508 files and directories currently installed.)

2024-09-05 23:41:40,422 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../00-libbsd0_0.10.0-1_amd64.deb ...

2024-09-05 23:41:40,486 - execution_module.docker_manager -raylib - INFO - Unpacking libbsd0:amd64 (0.10.0-1) ...

2024-09-05 23:41:40,723 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xkb-data.

2024-09-05 23:41:40,724 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../01-xkb-data_2.29-2_all.deb ...

2024-09-05 23:41:40,760 - execution_module.docker_manager -raylib - INFO - Unpacking xkb-data (2.29-2) ...

2024-09-05 23:41:44,497 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau6:amd64.

2024-09-05 23:41:44,498 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../02-libxau6_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-05 23:41:44,704 - execution_module.docker_manager -raylib - INFO - Unpacking libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:41:46,292 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp6:amd64.

2024-09-05 23:41:46,293 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../03-libxdmcp6_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-05 23:41:46,466 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:41:48,211 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1:amd64.

2024-09-05 23:41:48,212 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../04-libxcb1_1.14-2_amd64.deb ...

2024-09-05 23:41:48,434 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1:amd64 (1.14-2) ...

2024-09-05 23:41:48,862 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-data.

2024-09-05 23:41:48,864 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../05-libx11-data_2%3a1.6.9-2ubuntu1.6_all.deb ...

2024-09-05 23:41:48,910 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:41:49,286 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-6:amd64.

2024-09-05 23:41:49,290 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../06-libx11-6_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-05 23:41:49,367 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:41:51,386 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libpthread-stubs0-dev:amd64.

2024-09-05 23:41:51,387 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../07-libpthread-stubs0-dev_0.4-1_amd64.deb ...

2024-09-05 23:41:52,700 - execution_module.docker_manager -raylib - INFO - Unpacking libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-05 23:41:56,386 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-client0:amd64.

2024-09-05 23:41:56,387 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../08-libwayland-client0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:41:57,255 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:42:02,292 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-cursor0:amd64.

2024-09-05 23:42:02,294 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../09-libwayland-cursor0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:42:02,862 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:42:05,801 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-egl1:amd64.

2024-09-05 23:42:05,802 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../10-libwayland-egl1_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:42:06,087 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:42:07,044 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-server0:amd64.

2024-09-05 23:42:07,045 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../11-libwayland-server0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:42:07,119 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:42:07,457 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xorg-sgml-doctools.

2024-09-05 23:42:07,458 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../12-xorg-sgml-doctools_1%3a1.11-1_all.deb ...

2024-09-05 23:42:07,508 - execution_module.docker_manager -raylib - INFO - Unpacking xorg-sgml-doctools (1:1.11-1) ...

2024-09-05 23:42:08,318 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-dev.

2024-09-05 23:42:08,320 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../13-x11proto-dev_2019.2-1ubuntu1_all.deb ...

2024-09-05 23:42:08,465 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-dev (2019.2-1ubuntu1) ...

2024-09-05 23:42:11,447 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-core-dev.

2024-09-05 23:42:11,464 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../14-x11proto-core-dev_2019.2-1ubuntu1_all.deb ...

2024-09-05 23:42:11,960 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-05 23:42:12,907 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau-dev:amd64.

2024-09-05 23:42:12,908 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../15-libxau-dev_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-05 23:42:12,967 - execution_module.docker_manager -raylib - INFO - Unpacking libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:42:14,131 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp-dev:amd64.

2024-09-05 23:42:14,133 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../16-libxdmcp-dev_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-05 23:42:14,230 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:42:16,307 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xtrans-dev.

2024-09-05 23:42:16,309 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../17-xtrans-dev_1.4.0-1_all.deb ...

2024-09-05 23:42:16,893 - execution_module.docker_manager -raylib - INFO - Unpacking xtrans-dev (1.4.0-1) ...

2024-09-05 23:42:22,664 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1-dev:amd64.

2024-09-05 23:42:22,667 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../18-libxcb1-dev_1.14-2_amd64.deb ...

2024-09-05 23:42:25,960 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1-dev:amd64 (1.14-2) ...

2024-09-05 23:42:42,076 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-dev:amd64.

2024-09-05 23:42:42,079 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../19-libx11-dev_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-05 23:42:44,830 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:42:48,425 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon0:amd64.

2024-09-05 23:42:48,431 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../20-libxkbcommon0_0.10.0-1_amd64.deb ...

2024-09-05 23:42:48,658 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-05 23:42:49,640 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon-dev:amd64.

2024-09-05 23:42:49,642 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../21-libxkbcommon-dev_0.10.0-1_amd64.deb ...

2024-09-05 23:42:50,108 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-05 23:42:54,924 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package pkg-config.

2024-09-05 23:42:54,977 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../22-pkg-config_0.29.1-0ubuntu4_amd64.deb ...

2024-09-05 23:42:55,626 - execution_module.docker_manager -raylib - INFO - Unpacking pkg-config (0.29.1-0ubuntu4) ...

2024-09-05 23:42:58,487 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-bin.

2024-09-05 23:42:58,531 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../23-libwayland-bin_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:42:59,114 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-05 23:43:02,096 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-dev:amd64.

2024-09-05 23:43:02,109 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../24-libwayland-dev_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:43:02,423 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:43:04,938 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package wayland-protocols.

2024-09-05 23:43:04,940 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../25-wayland-protocols_1.20-1_all.deb ...

2024-09-05 23:43:05,239 - execution_module.docker_manager -raylib - INFO - Unpacking wayland-protocols (1.20-1) ...

2024-09-05 23:43:07,842 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:43:09,409 - execution_module.docker_manager -raylib - INFO - Setting up libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:43:10,303 - execution_module.docker_manager -raylib - INFO - Setting up xkb-data (2.29-2) ...

2024-09-05 23:43:11,069 - execution_module.docker_manager -raylib - INFO - Setting up libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-05 23:43:12,057 - execution_module.docker_manager -raylib - INFO - Setting up xtrans-dev (1.4.0-1) ...

2024-09-05 23:43:13,187 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-05 23:43:14,299 - execution_module.docker_manager -raylib - INFO - Setting up libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:43:15,335 - execution_module.docker_manager -raylib - INFO - Setting up pkg-config (0.29.1-0ubuntu4) ...

2024-09-05 23:43:17,524 - execution_module.docker_manager -raylib - INFO - Setting up wayland-protocols (1.20-1) ...

2024-09-05 23:43:18,762 - execution_module.docker_manager -raylib - INFO - Setting up xorg-sgml-doctools (1:1.11-1) ...

2024-09-05 23:43:20,116 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:43:21,327 - execution_module.docker_manager -raylib - INFO - Setting up libbsd0:amd64 (0.10.0-1) ...

2024-09-05 23:43:22,437 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-05 23:43:23,627 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:43:24,947 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-dev (2019.2-1ubuntu1) ...

2024-09-05 23:43:26,236 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:43:27,135 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1:amd64 (1.14-2) ...

2024-09-05 23:43:27,761 - execution_module.docker_manager -raylib - INFO - Setting up libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:43:28,467 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-05 23:43:28,687 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:43:28,835 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-05 23:43:28,987 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:43:29,919 - execution_module.docker_manager -raylib - INFO - Setting up libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:43:30,772 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1-dev:amd64 (1.14-2) ...

2024-09-05 23:43:32,019 - execution_module.docker_manager -raylib - INFO - Setting up libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:43:33,452 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:43:34,398 - execution_module.docker_manager -raylib - INFO - Processing triggers for libc-bin (2.31-0ubuntu9.16) ...

2024-09-05 23:43:38,018 - execution_module.docker_manager -raylib - INFO -  ---> f1b6170af75e

2024-09-05 23:43:38,018 - execution_module.docker_manager -raylib - INFO - Step 11/16 : RUN mkdir /tmp/raylib
2024-09-05 23:43:38,702 - execution_module.docker_manager -raylib - INFO -  ---> Running in 41e5f2cafdc9

2024-09-05 23:45:21,941 - execution_module.docker_manager -raylib - INFO -  ---> 844af82db02b

2024-09-05 23:45:21,941 - execution_module.docker_manager -raylib - INFO - Step 12/16 : COPY ./raylib /tmp/raylib
2024-09-05 23:47:35,539 - execution_module.docker_manager -raylib - INFO -  ---> 84ccf928ec25

2024-09-05 23:47:35,539 - execution_module.docker_manager -raylib - INFO - Step 13/16 : WORKDIR /tmp/raylib
2024-09-05 23:49:32,454 - execution_module.docker_manager -raylib - INFO -  ---> Running in e66421cb616e

2024-09-05 23:49:34,193 - execution_module.docker_manager -raylib - INFO -  ---> 98430c466f07

2024-09-05 23:49:34,193 - execution_module.docker_manager -raylib - INFO - Step 14/16 : RUN cmake -B build
2024-09-05 23:49:34,650 - execution_module.docker_manager -raylib - INFO -  ---> Running in 2089d451d955

2024-09-05 23:49:35,813 - execution_module.docker_manager -raylib - INFO - -- The C compiler identification is GNU 9.4.0

2024-09-05 23:49:35,876 - execution_module.docker_manager -raylib - INFO - -- The CXX compiler identification is GNU 9.4.0

2024-09-05 23:49:35,880 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc

2024-09-05 23:49:35,996 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc -- works

2024-09-05 23:49:35,998 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info

2024-09-05 23:49:36,171 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info - done

2024-09-05 23:49:36,192 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features

2024-09-05 23:49:36,192 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features - done

2024-09-05 23:49:36,209 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++

2024-09-05 23:49:36,285 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++ -- works

2024-09-05 23:49:36,286 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info

2024-09-05 23:49:36,348 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info - done

2024-09-05 23:49:36,357 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features

2024-09-05 23:49:36,357 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features - done

2024-09-05 23:49:36,358 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES

2024-09-05 23:49:36,430 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES - Success

2024-09-05 23:49:36,430 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=pointer-arith can be used -- compiles

2024-09-05 23:49:36,430 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=implicit-function-declaration can be used -- compiles

2024-09-05 23:49:36,430 - execution_module.docker_manager -raylib - INFO - -- Testing if -fno-strict-aliasing can be used -- compiles

2024-09-05 23:49:36,460 - execution_module.docker_manager -raylib - INFO - -- Setting build type to 'Debug' as none was specified.

2024-09-05 23:49:36,460 - execution_module.docker_manager -raylib - INFO - -- Using raylib's GLFW

2024-09-05 23:49:36,468 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h

2024-09-05 23:49:36,974 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h - found

2024-09-05 23:49:36,974 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD

2024-09-05 23:49:37,098 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed

2024-09-05 23:49:37,098 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads

2024-09-05 23:49:37,223 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads - not found

2024-09-05 23:49:37,224 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread

2024-09-05 23:49:37,315 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread - found

2024-09-05 23:49:37,317 - execution_module.docker_manager -raylib - INFO - -- Found Threads: TRUE  

2024-09-05 23:49:37,317 - execution_module.docker_manager -raylib - INFO - -- Including Wayland support
-- Including X11 support

2024-09-05 23:49:37,321 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create

2024-09-05 23:49:37,596 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create - found

2024-09-05 23:49:37,609 - execution_module.docker_manager -raylib - INFO - -- Found PkgConfig: /usr/bin/pkg-config (found version "0.29.1") 

2024-09-05 23:49:37,611 - execution_module.docker_manager -raylib - INFO - -- Checking for modules 'wayland-client>=0.2.7;wayland-cursor>=0.2.7;wayland-egl>=0.2.7;xkbcommon>=0.5.0'

2024-09-05 23:49:37,635 - execution_module.docker_manager -raylib - INFO - --   Found wayland-client, version 1.18.0

2024-09-05 23:49:37,641 - execution_module.docker_manager -raylib - INFO - --   Found wayland-cursor, version 1.18.0

2024-09-05 23:49:37,652 - execution_module.docker_manager -raylib - INFO - --   Found wayland-egl, version 18.1.0

2024-09-05 23:49:37,672 - execution_module.docker_manager -raylib - INFO - --   Found xkbcommon, version 0.10.0

2024-09-05 23:49:37,786 - execution_module.docker_manager -raylib - INFO - -- Found X11: /usr/include   

2024-09-05 23:49:37,786 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so

2024-09-05 23:49:38,004 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so - found

2024-09-05 23:49:38,005 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname

2024-09-05 23:49:38,138 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname - found

2024-09-05 23:49:38,139 - execution_module.docker_manager -raylib - INFO - -- Looking for connect

2024-09-05 23:49:38,346 - execution_module.docker_manager -raylib - INFO - -- Looking for connect - found

2024-09-05 23:49:38,346 - execution_module.docker_manager -raylib - INFO - -- Looking for remove

2024-09-05 23:49:38,591 - execution_module.docker_manager -raylib - INFO - -- Looking for remove - found

2024-09-05 23:49:38,591 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat

2024-09-05 23:49:38,857 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat - found

2024-09-05 23:49:38,859 - execution_module.docker_manager -raylib - INFO - [91mCMake Error at src/external/glfw/src/CMakeLists.txt:186 (message):
  RandR headers not found; install libxrandr development package


[0m
2024-09-05 23:49:38,860 - execution_module.docker_manager -raylib - INFO - -- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".

2024-09-05 23:49:41,450 - cxxcrafter -raylib - INFO - Execution Module Finishes
2024-09-05 23:49:41,451 - cxxcrafter -raylib - ERROR - Execution failed with error: -- Looking for shmat
-- Looking for shmat - found
[91mCMake Error at src/external/glfw/src/CMakeLists.txt:186 (message):
  RandR headers not found; install libxrandr development package


[0m-- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".
The command '/bin/sh -c cmake -B build' returned a non-zero code: 1
2024-09-05 23:49:41,451 - cxxcrafter -raylib - INFO - Modifier Module Starts
2024-09-05 23:49:45,104 - cxxcrafter -raylib - INFO - Modifier Module Finishes
2024-09-05 23:49:45,104 - cxxcrafter -raylib - INFO - Execution Module Starts
2024-09-05 23:49:49,638 - execution_module.docker_manager -raylib - INFO - Step 1/16 : FROM ubuntu:20.04
2024-09-05 23:49:49,680 - execution_module.docker_manager -raylib - INFO -  ---> 5f5250218d28

2024-09-05 23:49:49,680 - execution_module.docker_manager -raylib - INFO - Step 2/16 : ENV DEBIAN_FRONTEND=noninteractive
2024-09-05 23:49:49,680 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:49:49,680 - execution_module.docker_manager -raylib - INFO -  ---> defc00a448c4

2024-09-05 23:49:49,680 - execution_module.docker_manager -raylib - INFO - Step 3/16 : RUN sed -i 's|http://archive.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:49:49,680 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO -  ---> 7134b497e1a1

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO - Step 4/16 : RUN sed -i 's|http://security.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO -  ---> ea57a31cfcfc

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO - Step 5/16 : RUN apt-get update
2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO -  ---> 4041e8821132

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO - Step 6/16 : RUN apt-get upgrade -y
2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO -  ---> afe371a706bb

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO - Step 7/16 : RUN apt-get install -y build-essential
2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO -  ---> 29b06df4a18c

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO - Step 8/16 : RUN apt-get install -y software-properties-common
2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO -  ---> 760c93dbc806

2024-09-05 23:49:49,681 - execution_module.docker_manager -raylib - INFO - Step 9/16 : RUN apt-get install -y cmake
2024-09-05 23:49:49,682 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-05 23:49:49,682 - execution_module.docker_manager -raylib - INFO -  ---> 0e6b9331b4ee

2024-09-05 23:49:49,682 - execution_module.docker_manager -raylib - INFO - Step 10/16 : RUN apt-get install -y libwayland-dev wayland-protocols pkg-config libx11-dev libxkbcommon-dev libxrandr-dev
2024-09-05 23:52:07,333 - execution_module.docker_manager -raylib - INFO -  ---> Running in 49a73d69abb3

2024-09-05 23:52:08,500 - execution_module.docker_manager -raylib - INFO - Reading package lists...
2024-09-05 23:52:09,364 - execution_module.docker_manager -raylib - INFO - Building dependency tree...
2024-09-05 23:52:09,482 - execution_module.docker_manager -raylib - INFO - 
Reading state information...
2024-09-05 23:52:09,601 - execution_module.docker_manager -raylib - INFO - The following additional packages will be installed:

2024-09-05 23:52:09,602 - execution_module.docker_manager -raylib - INFO -   libbsd0 libpthread-stubs0-dev libwayland-bin libwayland-client0
  libwayland-cursor0 libwayland-egl1 libwayland-server0 libx11-6 libx11-data
  libxau-dev libxau6 libxcb1 libxcb1-dev libxdmcp-dev libxdmcp6 libxext-dev

2024-09-05 23:52:09,602 - execution_module.docker_manager -raylib - INFO -   libxext6 libxkbcommon0 libxrandr2 libxrender-dev libxrender1
  x11proto-core-dev x11proto-dev x11proto-randr-dev x11proto-xext-dev xkb-data
  xorg-sgml-doctools xtrans-dev

2024-09-05 23:52:09,603 - execution_module.docker_manager -raylib - INFO - Suggested packages:
  libwayland-doc libx11-doc libxcb-doc libxext-doc

2024-09-05 23:52:09,650 - execution_module.docker_manager -raylib - INFO - The following NEW packages will be installed:

2024-09-05 23:52:09,834 - execution_module.docker_manager -raylib - INFO -   libbsd0 libpthread-stubs0-dev libwayland-bin libwayland-client0
  libwayland-cursor0 libwayland-dev libwayland-egl1 libwayland-server0
  libx11-6 libx11-data libx11-dev libxau-dev libxau6 libxcb1 libxcb1-dev
  libxdmcp-dev libxdmcp6 libxext-dev libxext6 libxkbcommon-dev libxkbcommon0

2024-09-05 23:52:09,834 - execution_module.docker_manager -raylib - INFO -   libxrandr-dev libxrandr2 libxrender-dev libxrender1 pkg-config
  wayland-protocols x11proto-core-dev x11proto-dev x11proto-randr-dev
  x11proto-xext-dev xkb-data xorg-sgml-doctools xtrans-dev

2024-09-05 23:52:09,915 - execution_module.docker_manager -raylib - INFO - 0 upgraded, 34 newly installed, 0 to remove and 0 not upgraded.
Need to get 3202 kB of archives.
After this operation, 16.1 MB of additional disk space will be used.
Get:1 http://mirrors.aliyun.com/ubuntu focal/main amd64 libbsd0 amd64 0.10.0-1 [45.4 kB]

2024-09-05 23:52:10,161 - execution_module.docker_manager -raylib - INFO - Get:2 http://mirrors.aliyun.com/ubuntu focal/main amd64 xkb-data all 2.29-2 [349 kB]

2024-09-05 23:52:10,317 - execution_module.docker_manager -raylib - INFO - Get:3 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau6 amd64 1:1.0.9-0ubuntu1 [7488 B]

2024-09-05 23:52:10,362 - execution_module.docker_manager -raylib - INFO - Get:4 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp6 amd64 1:1.1.3-0ubuntu1 [10.6 kB]

2024-09-05 23:52:10,453 - execution_module.docker_manager -raylib - INFO - Get:5 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1 amd64 1.14-2 [44.7 kB]

2024-09-05 23:52:10,609 - execution_module.docker_manager -raylib - INFO - Get:6 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-data all 2:1.6.9-2ubuntu1.6 [114 kB]

2024-09-05 23:52:10,862 - execution_module.docker_manager -raylib - INFO - Get:7 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-6 amd64 2:1.6.9-2ubuntu1.6 [577 kB]

2024-09-05 23:52:10,982 - execution_module.docker_manager -raylib - INFO - Get:8 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxext6 amd64 2:1.3.4-0ubuntu1 [29.1 kB]

2024-09-05 23:52:11,048 - execution_module.docker_manager -raylib - INFO - Get:9 http://mirrors.aliyun.com/ubuntu focal/main amd64 libpthread-stubs0-dev amd64 0.4-1 [5384 B]

2024-09-05 23:52:11,089 - execution_module.docker_manager -raylib - INFO - Get:10 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-client0 amd64 1.18.0-1ubuntu0.1 [23.9 kB]

2024-09-05 23:52:11,181 - execution_module.docker_manager -raylib - INFO - Get:11 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-cursor0 amd64 1.18.0-1ubuntu0.1 [10.3 kB]

2024-09-05 23:52:11,228 - execution_module.docker_manager -raylib - INFO - Get:12 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-egl1 amd64 1.18.0-1ubuntu0.1 [5596 B]

2024-09-05 23:52:11,367 - execution_module.docker_manager -raylib - INFO - Get:13 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-server0 amd64 1.18.0-1ubuntu0.1 [31.3 kB]

2024-09-05 23:52:11,423 - execution_module.docker_manager -raylib - INFO - Get:14 http://mirrors.aliyun.com/ubuntu focal/main amd64 xorg-sgml-doctools all 1:1.11-1 [12.9 kB]

2024-09-05 23:52:11,572 - execution_module.docker_manager -raylib - INFO - Get:15 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-dev all 2019.2-1ubuntu1 [594 kB]

2024-09-05 23:52:11,886 - execution_module.docker_manager -raylib - INFO - Get:16 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-core-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-05 23:52:12,052 - execution_module.docker_manager -raylib - INFO - Get:17 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau-dev amd64 1:1.0.9-0ubuntu1 [9552 B]

2024-09-05 23:52:12,091 - execution_module.docker_manager -raylib - INFO - Get:18 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp-dev amd64 1:1.1.3-0ubuntu1 [25.3 kB]

2024-09-05 23:52:12,228 - execution_module.docker_manager -raylib - INFO - Get:19 http://mirrors.aliyun.com/ubuntu focal/main amd64 xtrans-dev all 1.4.0-1 [68.9 kB]

2024-09-05 23:52:12,401 - execution_module.docker_manager -raylib - INFO - Get:20 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1-dev amd64 1.14-2 [80.5 kB]

2024-09-05 23:52:12,495 - execution_module.docker_manager -raylib - INFO - Get:21 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-dev amd64 2:1.6.9-2ubuntu1.6 [648 kB]

2024-09-05 23:52:12,800 - execution_module.docker_manager -raylib - INFO - Get:22 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-xext-dev all 2019.2-1ubuntu1 [2616 B]

2024-09-05 23:52:12,870 - execution_module.docker_manager -raylib - INFO - Get:23 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxext-dev amd64 2:1.3.4-0ubuntu1 [82.2 kB]

2024-09-05 23:52:13,121 - execution_module.docker_manager -raylib - INFO - Get:24 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon0 amd64 0.10.0-1 [98.4 kB]

2024-09-05 23:52:13,236 - execution_module.docker_manager -raylib - INFO - Get:25 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon-dev amd64 0.10.0-1 [45.4 kB]

2024-09-05 23:52:13,351 - execution_module.docker_manager -raylib - INFO - Get:26 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrender1 amd64 1:0.9.10-1 [18.7 kB]

2024-09-05 23:52:13,490 - execution_module.docker_manager -raylib - INFO - Get:27 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrandr2 amd64 2:1.5.2-0ubuntu1 [18.5 kB]

2024-09-05 23:52:13,620 - execution_module.docker_manager -raylib - INFO - Get:28 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-randr-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-05 23:52:13,731 - execution_module.docker_manager -raylib - INFO - Get:29 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrender-dev amd64 1:0.9.10-1 [24.9 kB]

2024-09-05 23:52:13,907 - execution_module.docker_manager -raylib - INFO - Get:30 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrandr-dev amd64 2:1.5.2-0ubuntu1 [25.0 kB]

2024-09-05 23:52:13,999 - execution_module.docker_manager -raylib - INFO - Get:31 http://mirrors.aliyun.com/ubuntu focal/main amd64 pkg-config amd64 0.29.1-0ubuntu4 [45.5 kB]

2024-09-05 23:52:14,178 - execution_module.docker_manager -raylib - INFO - Get:32 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-bin amd64 1.18.0-1ubuntu0.1 [20.2 kB]

2024-09-05 23:52:14,443 - execution_module.docker_manager -raylib - INFO - Get:33 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-dev amd64 1.18.0-1ubuntu0.1 [64.6 kB]

2024-09-05 23:52:14,525 - execution_module.docker_manager -raylib - INFO - Get:34 http://mirrors.aliyun.com/ubuntu focal/main amd64 wayland-protocols all 1.20-1 [60.3 kB]

2024-09-05 23:52:15,726 - execution_module.docker_manager -raylib - INFO - [91mdebconf: delaying package configuration, since apt-utils is not installed
[0m
2024-09-05 23:52:16,008 - execution_module.docker_manager -raylib - INFO - Fetched 3202 kB in 5s (677 kB/s)

2024-09-05 23:52:17,484 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libbsd0:amd64.
(Reading database ... 
2024-09-05 23:52:17,500 - execution_module.docker_manager -raylib - INFO - (Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
2024-09-05 23:52:17,500 - execution_module.docker_manager -raylib - INFO - (Reading database ... 65%
2024-09-05 23:52:17,500 - execution_module.docker_manager -raylib - INFO - (Reading database ... 70%
2024-09-05 23:52:17,500 - execution_module.docker_manager -raylib - INFO - (Reading database ... 75%
2024-09-05 23:52:17,920 - execution_module.docker_manager -raylib - INFO - (Reading database ... 80%
2024-09-05 23:52:18,558 - execution_module.docker_manager -raylib - INFO - (Reading database ... 85%
2024-09-05 23:52:18,630 - execution_module.docker_manager -raylib - INFO - (Reading database ... 90%
2024-09-05 23:52:18,739 - execution_module.docker_manager -raylib - INFO - (Reading database ... 95%
2024-09-05 23:52:18,746 - execution_module.docker_manager -raylib - INFO - (Reading database ... 100%
(Reading database ... 21508 files and directories currently installed.)

2024-09-05 23:52:18,746 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../00-libbsd0_0.10.0-1_amd64.deb ...

2024-09-05 23:52:19,197 - execution_module.docker_manager -raylib - INFO - Unpacking libbsd0:amd64 (0.10.0-1) ...

2024-09-05 23:52:20,167 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xkb-data.

2024-09-05 23:52:20,170 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../01-xkb-data_2.29-2_all.deb ...

2024-09-05 23:52:20,388 - execution_module.docker_manager -raylib - INFO - Unpacking xkb-data (2.29-2) ...

2024-09-05 23:52:21,461 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau6:amd64.

2024-09-05 23:52:21,462 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../02-libxau6_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-05 23:52:21,720 - execution_module.docker_manager -raylib - INFO - Unpacking libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:52:23,873 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp6:amd64.

2024-09-05 23:52:23,875 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../03-libxdmcp6_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-05 23:52:24,330 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:52:31,780 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1:amd64.

2024-09-05 23:52:31,783 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../04-libxcb1_1.14-2_amd64.deb ...

2024-09-05 23:52:32,137 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1:amd64 (1.14-2) ...

2024-09-05 23:52:40,010 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-data.

2024-09-05 23:52:40,011 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../05-libx11-data_2%3a1.6.9-2ubuntu1.6_all.deb ...

2024-09-05 23:52:41,057 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:52:45,937 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-6:amd64.

2024-09-05 23:52:45,938 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../06-libx11-6_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-05 23:52:46,851 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:52:52,295 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxext6:amd64.

2024-09-05 23:52:52,312 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../07-libxext6_2%3a1.3.4-0ubuntu1_amd64.deb ...

2024-09-05 23:52:53,863 - execution_module.docker_manager -raylib - INFO - Unpacking libxext6:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-05 23:53:04,367 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libpthread-stubs0-dev:amd64.

2024-09-05 23:53:04,370 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../08-libpthread-stubs0-dev_0.4-1_amd64.deb ...

2024-09-05 23:53:06,104 - execution_module.docker_manager -raylib - INFO - Unpacking libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-05 23:53:15,677 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-client0:amd64.

2024-09-05 23:53:15,680 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../09-libwayland-client0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:53:15,959 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:53:19,789 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-cursor0:amd64.

2024-09-05 23:53:19,791 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../10-libwayland-cursor0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:53:20,127 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:53:22,471 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-egl1:amd64.

2024-09-05 23:53:22,476 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../11-libwayland-egl1_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:53:22,592 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:53:23,628 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-server0:amd64.

2024-09-05 23:53:23,630 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../12-libwayland-server0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:53:23,829 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:53:25,584 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xorg-sgml-doctools.

2024-09-05 23:53:25,587 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../13-xorg-sgml-doctools_1%3a1.11-1_all.deb ...

2024-09-05 23:53:25,675 - execution_module.docker_manager -raylib - INFO - Unpacking xorg-sgml-doctools (1:1.11-1) ...

2024-09-05 23:53:26,670 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-dev.

2024-09-05 23:53:26,672 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../14-x11proto-dev_2019.2-1ubuntu1_all.deb ...

2024-09-05 23:53:26,921 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-dev (2019.2-1ubuntu1) ...

2024-09-05 23:53:27,812 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-core-dev.

2024-09-05 23:53:27,814 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../15-x11proto-core-dev_2019.2-1ubuntu1_all.deb ...

2024-09-05 23:53:28,160 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-05 23:53:29,224 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau-dev:amd64.

2024-09-05 23:53:29,227 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../16-libxau-dev_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-05 23:53:29,464 - execution_module.docker_manager -raylib - INFO - Unpacking libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:53:32,069 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp-dev:amd64.

2024-09-05 23:53:32,070 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../17-libxdmcp-dev_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-05 23:53:32,769 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:53:34,645 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xtrans-dev.

2024-09-05 23:53:34,668 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../18-xtrans-dev_1.4.0-1_all.deb ...

2024-09-05 23:53:34,937 - execution_module.docker_manager -raylib - INFO - Unpacking xtrans-dev (1.4.0-1) ...

2024-09-05 23:53:35,977 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1-dev:amd64.

2024-09-05 23:53:35,987 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../19-libxcb1-dev_1.14-2_amd64.deb ...

2024-09-05 23:53:36,348 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1-dev:amd64 (1.14-2) ...

2024-09-05 23:53:37,333 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-dev:amd64.

2024-09-05 23:53:37,341 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../20-libx11-dev_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-05 23:53:37,594 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:53:38,447 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-xext-dev.

2024-09-05 23:53:38,448 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../21-x11proto-xext-dev_2019.2-1ubuntu1_all.deb ...

2024-09-05 23:53:38,623 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-xext-dev (2019.2-1ubuntu1) ...

2024-09-05 23:53:39,622 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxext-dev:amd64.

2024-09-05 23:53:39,640 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../22-libxext-dev_2%3a1.3.4-0ubuntu1_amd64.deb ...

2024-09-05 23:53:39,924 - execution_module.docker_manager -raylib - INFO - Unpacking libxext-dev:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-05 23:53:43,300 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon0:amd64.

2024-09-05 23:53:43,313 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../23-libxkbcommon0_0.10.0-1_amd64.deb ...

2024-09-05 23:53:43,643 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-05 23:53:44,866 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon-dev:amd64.

2024-09-05 23:53:44,868 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../24-libxkbcommon-dev_0.10.0-1_amd64.deb ...

2024-09-05 23:53:44,942 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-05 23:53:45,768 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrender1:amd64.

2024-09-05 23:53:45,770 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../25-libxrender1_1%3a0.9.10-1_amd64.deb ...

2024-09-05 23:53:45,835 - execution_module.docker_manager -raylib - INFO - Unpacking libxrender1:amd64 (1:0.9.10-1) ...

2024-09-05 23:53:46,362 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrandr2:amd64.

2024-09-05 23:53:46,394 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../26-libxrandr2_2%3a1.5.2-0ubuntu1_amd64.deb ...

2024-09-05 23:53:46,470 - execution_module.docker_manager -raylib - INFO - Unpacking libxrandr2:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-05 23:53:47,748 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-randr-dev.

2024-09-05 23:53:47,752 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../27-x11proto-randr-dev_2019.2-1ubuntu1_all.deb ...

2024-09-05 23:53:48,030 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-randr-dev (2019.2-1ubuntu1) ...

2024-09-05 23:53:49,301 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrender-dev:amd64.

2024-09-05 23:53:49,306 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../28-libxrender-dev_1%3a0.9.10-1_amd64.deb ...

2024-09-05 23:53:49,570 - execution_module.docker_manager -raylib - INFO - Unpacking libxrender-dev:amd64 (1:0.9.10-1) ...

2024-09-05 23:53:50,134 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrandr-dev:amd64.

2024-09-05 23:53:50,136 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../29-libxrandr-dev_2%3a1.5.2-0ubuntu1_amd64.deb ...

2024-09-05 23:53:50,203 - execution_module.docker_manager -raylib - INFO - Unpacking libxrandr-dev:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-05 23:53:50,590 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package pkg-config.

2024-09-05 23:53:50,593 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../30-pkg-config_0.29.1-0ubuntu4_amd64.deb ...

2024-09-05 23:53:50,641 - execution_module.docker_manager -raylib - INFO - Unpacking pkg-config (0.29.1-0ubuntu4) ...

2024-09-05 23:53:51,006 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-bin.

2024-09-05 23:53:51,022 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../31-libwayland-bin_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:53:51,100 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-05 23:53:51,667 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-dev:amd64.

2024-09-05 23:53:51,668 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../32-libwayland-dev_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-05 23:53:51,724 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:53:52,219 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package wayland-protocols.

2024-09-05 23:53:52,222 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../33-wayland-protocols_1.20-1_all.deb ...

2024-09-05 23:53:52,257 - execution_module.docker_manager -raylib - INFO - Unpacking wayland-protocols (1.20-1) ...

2024-09-05 23:53:52,515 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:53:52,625 - execution_module.docker_manager -raylib - INFO - Setting up libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:53:52,768 - execution_module.docker_manager -raylib - INFO - Setting up xkb-data (2.29-2) ...

2024-09-05 23:53:52,926 - execution_module.docker_manager -raylib - INFO - Setting up libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-05 23:53:53,123 - execution_module.docker_manager -raylib - INFO - Setting up xtrans-dev (1.4.0-1) ...

2024-09-05 23:53:53,294 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-05 23:53:55,388 - execution_module.docker_manager -raylib - INFO - Setting up libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:53:57,605 - execution_module.docker_manager -raylib - INFO - Setting up pkg-config (0.29.1-0ubuntu4) ...

2024-09-05 23:54:17,289 - execution_module.docker_manager -raylib - INFO - Setting up wayland-protocols (1.20-1) ...

2024-09-05 23:54:20,099 - execution_module.docker_manager -raylib - INFO - Setting up xorg-sgml-doctools (1:1.11-1) ...

2024-09-05 23:54:21,963 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:54:24,116 - execution_module.docker_manager -raylib - INFO - Setting up libbsd0:amd64 (0.10.0-1) ...

2024-09-05 23:54:26,618 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-05 23:54:29,614 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:54:31,141 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-dev (2019.2-1ubuntu1) ...

2024-09-05 23:54:33,753 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:54:37,534 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1:amd64 (1.14-2) ...

2024-09-05 23:54:39,913 - execution_module.docker_manager -raylib - INFO - Setting up libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-05 23:54:41,823 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-randr-dev (2019.2-1ubuntu1) ...

2024-09-05 23:54:43,886 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-05 23:54:45,912 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-05 23:54:46,643 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-05 23:54:47,883 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-xext-dev (2019.2-1ubuntu1) ...

2024-09-05 23:54:49,501 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:54:51,500 - execution_module.docker_manager -raylib - INFO - Setting up libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:54:52,842 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1-dev:amd64 (1.14-2) ...

2024-09-05 23:54:53,719 - execution_module.docker_manager -raylib - INFO - Setting up libxrender1:amd64 (1:0.9.10-1) ...

2024-09-05 23:54:54,597 - execution_module.docker_manager -raylib - INFO - Setting up libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-05 23:54:55,063 - execution_module.docker_manager -raylib - INFO - Setting up libxext6:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-05 23:54:55,700 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-05 23:54:56,354 - execution_module.docker_manager -raylib - INFO - Setting up libxrandr2:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-05 23:54:57,025 - execution_module.docker_manager -raylib - INFO - Setting up libxext-dev:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-05 23:54:57,773 - execution_module.docker_manager -raylib - INFO - Setting up libxrender-dev:amd64 (1:0.9.10-1) ...

2024-09-05 23:54:58,544 - execution_module.docker_manager -raylib - INFO - Setting up libxrandr-dev:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-05 23:54:59,149 - execution_module.docker_manager -raylib - INFO - Processing triggers for libc-bin (2.31-0ubuntu9.16) ...

2024-09-05 23:56:49,322 - execution_module.docker_manager -raylib - INFO -  ---> 73e44eb806ad

2024-09-05 23:56:49,322 - execution_module.docker_manager -raylib - INFO - Step 11/16 : RUN mkdir /tmp/raylib
2024-09-05 23:56:56,793 - execution_module.docker_manager -raylib - INFO -  ---> Running in 25d43cd5cabc

2024-09-05 23:57:03,072 - execution_module.docker_manager -raylib - INFO -  ---> 0902d4b7d8d8

2024-09-05 23:57:03,072 - execution_module.docker_manager -raylib - INFO - Step 12/16 : COPY ./raylib /tmp/raylib
2024-09-05 23:59:27,951 - execution_module.docker_manager -raylib - INFO -  ---> 108538c9808f

2024-09-05 23:59:27,951 - execution_module.docker_manager -raylib - INFO - Step 13/16 : WORKDIR /tmp/raylib
2024-09-05 23:59:29,206 - execution_module.docker_manager -raylib - INFO -  ---> Running in 589da2d422be

2024-09-05 23:59:30,141 - execution_module.docker_manager -raylib - INFO -  ---> bc8fb112e637

2024-09-05 23:59:30,142 - execution_module.docker_manager -raylib - INFO - Step 14/16 : RUN cmake -B build
2024-09-05 23:59:30,716 - execution_module.docker_manager -raylib - INFO -  ---> Running in ef32e466e96c

2024-09-05 23:59:35,572 - execution_module.docker_manager -raylib - INFO - -- The C compiler identification is GNU 9.4.0

2024-09-05 23:59:35,853 - execution_module.docker_manager -raylib - INFO - -- The CXX compiler identification is GNU 9.4.0

2024-09-05 23:59:35,861 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc

2024-09-05 23:59:36,012 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc -- works

2024-09-05 23:59:36,013 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info

2024-09-05 23:59:36,298 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info - done

2024-09-05 23:59:36,312 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features

2024-09-05 23:59:36,312 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features - done

2024-09-05 23:59:36,313 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++

2024-09-05 23:59:36,441 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++ -- works

2024-09-05 23:59:36,442 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info

2024-09-05 23:59:36,578 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info - done

2024-09-05 23:59:36,595 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features

2024-09-05 23:59:36,595 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features - done

2024-09-05 23:59:36,596 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES

2024-09-05 23:59:36,863 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES - Success

2024-09-05 23:59:36,863 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=pointer-arith can be used -- compiles

2024-09-05 23:59:36,864 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=implicit-function-declaration can be used -- compiles

2024-09-05 23:59:36,864 - execution_module.docker_manager -raylib - INFO - -- Testing if -fno-strict-aliasing can be used -- compiles

2024-09-05 23:59:36,878 - execution_module.docker_manager -raylib - INFO - -- Setting build type to 'Debug' as none was specified.

2024-09-05 23:59:36,879 - execution_module.docker_manager -raylib - INFO - -- Using raylib's GLFW

2024-09-05 23:59:36,884 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h

2024-09-05 23:59:37,272 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h - found

2024-09-05 23:59:37,272 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD

2024-09-05 23:59:37,355 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed

2024-09-05 23:59:37,355 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads

2024-09-05 23:59:37,569 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads - not found

2024-09-05 23:59:37,569 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread

2024-09-05 23:59:37,731 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread - found

2024-09-05 23:59:37,731 - execution_module.docker_manager -raylib - INFO - -- Found Threads: TRUE  

2024-09-05 23:59:37,731 - execution_module.docker_manager -raylib - INFO - -- Including Wayland support
-- Including X11 support

2024-09-05 23:59:37,734 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create

2024-09-05 23:59:37,935 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create - found

2024-09-05 23:59:38,101 - execution_module.docker_manager -raylib - INFO - -- Found PkgConfig: /usr/bin/pkg-config (found version "0.29.1") 

2024-09-05 23:59:38,102 - execution_module.docker_manager -raylib - INFO - -- Checking for modules 'wayland-client>=0.2.7;wayland-cursor>=0.2.7;wayland-egl>=0.2.7;xkbcommon>=0.5.0'

2024-09-05 23:59:38,138 - execution_module.docker_manager -raylib - INFO - --   Found wayland-client, version 1.18.0

2024-09-05 23:59:38,146 - execution_module.docker_manager -raylib - INFO - --   Found wayland-cursor, version 1.18.0

2024-09-05 23:59:38,160 - execution_module.docker_manager -raylib - INFO - --   Found wayland-egl, version 18.1.0

2024-09-05 23:59:38,174 - execution_module.docker_manager -raylib - INFO - --   Found xkbcommon, version 0.10.0

2024-09-05 23:59:38,322 - execution_module.docker_manager -raylib - INFO - -- Found X11: /usr/include   

2024-09-05 23:59:38,328 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so

2024-09-05 23:59:38,509 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so - found

2024-09-05 23:59:38,510 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname

2024-09-05 23:59:38,926 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname - found

2024-09-05 23:59:38,927 - execution_module.docker_manager -raylib - INFO - -- Looking for connect

2024-09-05 23:59:39,152 - execution_module.docker_manager -raylib - INFO - -- Looking for connect - found

2024-09-05 23:59:39,152 - execution_module.docker_manager -raylib - INFO - -- Looking for remove

2024-09-05 23:59:39,432 - execution_module.docker_manager -raylib - INFO - -- Looking for remove - found

2024-09-05 23:59:39,433 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat

2024-09-05 23:59:39,693 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat - found

2024-09-05 23:59:39,694 - execution_module.docker_manager -raylib - INFO - [91mCMake Error at src/external/glfw/src/CMakeLists.txt:192 (message):
  Xinerama headers not found; install libxinerama development package


[0m
2024-09-05 23:59:39,695 - execution_module.docker_manager -raylib - INFO - -- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".

2024-09-06 00:01:31,421 - cxxcrafter -raylib - INFO - Execution Module Finishes
2024-09-06 00:01:31,421 - cxxcrafter -raylib - ERROR - Execution failed with error: -- Looking for shmat
-- Looking for shmat - found
[91mCMake Error at src/external/glfw/src/CMakeLists.txt:192 (message):
  Xinerama headers not found; install libxinerama development package


[0m-- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".
The command '/bin/sh -c cmake -B build' returned a non-zero code: 1
2024-09-06 00:01:31,422 - cxxcrafter -raylib - INFO - Modifier Module Starts
2024-09-06 00:01:38,990 - cxxcrafter -raylib - INFO - Modifier Module Finishes
2024-09-06 00:01:39,311 - cxxcrafter -raylib - INFO - Execution Module Starts
2024-09-06 00:02:04,719 - execution_module.docker_manager -raylib - INFO - Step 1/16 : FROM ubuntu:20.04
2024-09-06 00:02:04,719 - execution_module.docker_manager -raylib - INFO -  ---> 5f5250218d28

2024-09-06 00:02:04,719 - execution_module.docker_manager -raylib - INFO - Step 2/16 : ENV DEBIAN_FRONTEND=noninteractive
2024-09-06 00:02:04,720 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:02:04,720 - execution_module.docker_manager -raylib - INFO -  ---> defc00a448c4

2024-09-06 00:02:04,720 - execution_module.docker_manager -raylib - INFO - Step 3/16 : RUN sed -i 's|http://archive.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-06 00:02:04,720 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:02:04,720 - execution_module.docker_manager -raylib - INFO -  ---> 7134b497e1a1

2024-09-06 00:02:04,720 - execution_module.docker_manager -raylib - INFO - Step 4/16 : RUN sed -i 's|http://security.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-06 00:02:04,720 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:02:04,721 - execution_module.docker_manager -raylib - INFO -  ---> ea57a31cfcfc

2024-09-06 00:02:04,721 - execution_module.docker_manager -raylib - INFO - Step 5/16 : RUN apt-get update
2024-09-06 00:02:04,721 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:02:04,721 - execution_module.docker_manager -raylib - INFO -  ---> 4041e8821132

2024-09-06 00:02:04,721 - execution_module.docker_manager -raylib - INFO - Step 6/16 : RUN apt-get upgrade -y
2024-09-06 00:02:04,721 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:02:04,721 - execution_module.docker_manager -raylib - INFO -  ---> afe371a706bb

2024-09-06 00:02:04,722 - execution_module.docker_manager -raylib - INFO - Step 7/16 : RUN apt-get install -y build-essential
2024-09-06 00:02:04,722 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:02:04,722 - execution_module.docker_manager -raylib - INFO -  ---> 29b06df4a18c

2024-09-06 00:02:04,722 - execution_module.docker_manager -raylib - INFO - Step 8/16 : RUN apt-get install -y software-properties-common
2024-09-06 00:02:04,722 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:02:04,722 - execution_module.docker_manager -raylib - INFO -  ---> 760c93dbc806

2024-09-06 00:02:04,722 - execution_module.docker_manager -raylib - INFO - Step 9/16 : RUN apt-get install -y cmake
2024-09-06 00:02:04,722 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:02:04,723 - execution_module.docker_manager -raylib - INFO -  ---> 0e6b9331b4ee

2024-09-06 00:02:04,723 - execution_module.docker_manager -raylib - INFO - Step 10/16 : RUN apt-get install -y libwayland-dev wayland-protocols pkg-config libx11-dev libxkbcommon-dev libxrandr-dev libxinerama-dev
2024-09-06 00:02:21,391 - execution_module.docker_manager -raylib - INFO -  ---> Running in b2cb94d1a312

2024-09-06 00:02:24,199 - execution_module.docker_manager -raylib - INFO - Reading package lists...
2024-09-06 00:02:24,953 - execution_module.docker_manager -raylib - INFO - Building dependency tree...
2024-09-06 00:02:25,061 - execution_module.docker_manager -raylib - INFO - 
Reading state information...
2024-09-06 00:02:25,162 - execution_module.docker_manager -raylib - INFO - The following additional packages will be installed:
  libbsd0 libpthread-stubs0-dev libwayland-bin libwayland-client0
  libwayland-cursor0 libwayland-egl1 libwayland-server0 libx11-6 libx11-data
  libxau-dev libxau6 libxcb1 libxcb1-dev libxdmcp-dev libxdmcp6 libxext-dev

2024-09-06 00:02:25,252 - execution_module.docker_manager -raylib - INFO -   libxext6 libxinerama1 libxkbcommon0 libxrandr2 libxrender-dev libxrender1
  x11proto-core-dev x11proto-dev x11proto-randr-dev x11proto-xext-dev
  x11proto-xinerama-dev xkb-data xorg-sgml-doctools xtrans-dev

2024-09-06 00:02:25,252 - execution_module.docker_manager -raylib - INFO - Suggested packages:
  libwayland-doc libx11-doc libxcb-doc libxext-doc

2024-09-06 00:02:25,295 - execution_module.docker_manager -raylib - INFO - The following NEW packages will be installed:

2024-09-06 00:02:25,423 - execution_module.docker_manager -raylib - INFO -   libbsd0 libpthread-stubs0-dev libwayland-bin libwayland-client0
  libwayland-cursor0 libwayland-dev libwayland-egl1 libwayland-server0
  libx11-6 libx11-data libx11-dev libxau-dev libxau6 libxcb1 libxcb1-dev
  libxdmcp-dev libxdmcp6 libxext-dev libxext6 libxinerama-dev libxinerama1
  libxkbcommon-dev libxkbcommon0 libxrandr-dev libxrandr2 libxrender-dev

2024-09-06 00:02:25,423 - execution_module.docker_manager -raylib - INFO -   libxrender1 pkg-config wayland-protocols x11proto-core-dev x11proto-dev
  x11proto-randr-dev x11proto-xext-dev x11proto-xinerama-dev xkb-data
  xorg-sgml-doctools xtrans-dev

2024-09-06 00:02:25,508 - execution_module.docker_manager -raylib - INFO - 0 upgraded, 37 newly installed, 0 to remove and 0 not upgraded.
Need to get 3220 kB of archives.
After this operation, 16.2 MB of additional disk space will be used.
Get:1 http://mirrors.aliyun.com/ubuntu focal/main amd64 libbsd0 amd64 0.10.0-1 [45.4 kB]

2024-09-06 00:02:25,610 - execution_module.docker_manager -raylib - INFO - Get:2 http://mirrors.aliyun.com/ubuntu focal/main amd64 xkb-data all 2.29-2 [349 kB]

2024-09-06 00:02:25,826 - execution_module.docker_manager -raylib - INFO - Get:3 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau6 amd64 1:1.0.9-0ubuntu1 [7488 B]

2024-09-06 00:02:25,919 - execution_module.docker_manager -raylib - INFO - Get:4 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp6 amd64 1:1.1.3-0ubuntu1 [10.6 kB]

2024-09-06 00:02:25,990 - execution_module.docker_manager -raylib - INFO - Get:5 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1 amd64 1.14-2 [44.7 kB]

2024-09-06 00:02:26,065 - execution_module.docker_manager -raylib - INFO - Get:6 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-data all 2:1.6.9-2ubuntu1.6 [114 kB]

2024-09-06 00:02:26,162 - execution_module.docker_manager -raylib - INFO - Get:7 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-6 amd64 2:1.6.9-2ubuntu1.6 [577 kB]

2024-09-06 00:02:26,316 - execution_module.docker_manager -raylib - INFO - Get:8 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxext6 amd64 2:1.3.4-0ubuntu1 [29.1 kB]

2024-09-06 00:02:26,373 - execution_module.docker_manager -raylib - INFO - Get:9 http://mirrors.aliyun.com/ubuntu focal/main amd64 libpthread-stubs0-dev amd64 0.4-1 [5384 B]

2024-09-06 00:02:26,437 - execution_module.docker_manager -raylib - INFO - Get:10 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-client0 amd64 1.18.0-1ubuntu0.1 [23.9 kB]

2024-09-06 00:02:26,484 - execution_module.docker_manager -raylib - INFO - Get:11 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-cursor0 amd64 1.18.0-1ubuntu0.1 [10.3 kB]

2024-09-06 00:02:26,530 - execution_module.docker_manager -raylib - INFO - Get:12 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-egl1 amd64 1.18.0-1ubuntu0.1 [5596 B]

2024-09-06 00:02:26,588 - execution_module.docker_manager -raylib - INFO - Get:13 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-server0 amd64 1.18.0-1ubuntu0.1 [31.3 kB]

2024-09-06 00:02:26,681 - execution_module.docker_manager -raylib - INFO - Get:14 http://mirrors.aliyun.com/ubuntu focal/main amd64 xorg-sgml-doctools all 1:1.11-1 [12.9 kB]

2024-09-06 00:02:26,753 - execution_module.docker_manager -raylib - INFO - Get:15 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-dev all 2019.2-1ubuntu1 [594 kB]

2024-09-06 00:02:26,907 - execution_module.docker_manager -raylib - INFO - Get:16 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-core-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-06 00:02:26,973 - execution_module.docker_manager -raylib - INFO - Get:17 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau-dev amd64 1:1.0.9-0ubuntu1 [9552 B]

2024-09-06 00:02:27,033 - execution_module.docker_manager -raylib - INFO - Get:18 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp-dev amd64 1:1.1.3-0ubuntu1 [25.3 kB]

2024-09-06 00:02:27,085 - execution_module.docker_manager -raylib - INFO - Get:19 http://mirrors.aliyun.com/ubuntu focal/main amd64 xtrans-dev all 1.4.0-1 [68.9 kB]

2024-09-06 00:02:27,155 - execution_module.docker_manager -raylib - INFO - Get:20 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1-dev amd64 1.14-2 [80.5 kB]

2024-09-06 00:02:27,233 - execution_module.docker_manager -raylib - INFO - Get:21 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-dev amd64 2:1.6.9-2ubuntu1.6 [648 kB]

2024-09-06 00:02:27,400 - execution_module.docker_manager -raylib - INFO - Get:22 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-xext-dev all 2019.2-1ubuntu1 [2616 B]

2024-09-06 00:02:27,578 - execution_module.docker_manager -raylib - INFO - Get:23 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxext-dev amd64 2:1.3.4-0ubuntu1 [82.2 kB]

2024-09-06 00:02:27,673 - execution_module.docker_manager -raylib - INFO - Get:24 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxinerama1 amd64 2:1.1.4-2 [6904 B]

2024-09-06 00:02:27,717 - execution_module.docker_manager -raylib - INFO - Get:25 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-xinerama-dev all 2019.2-1ubuntu1 [2628 B]

2024-09-06 00:02:27,769 - execution_module.docker_manager -raylib - INFO - Get:26 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxinerama-dev amd64 2:1.1.4-2 [7896 B]

2024-09-06 00:02:27,833 - execution_module.docker_manager -raylib - INFO - Get:27 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon0 amd64 0.10.0-1 [98.4 kB]

2024-09-06 00:02:27,908 - execution_module.docker_manager -raylib - INFO - Get:28 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon-dev amd64 0.10.0-1 [45.4 kB]

2024-09-06 00:02:27,975 - execution_module.docker_manager -raylib - INFO - Get:29 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrender1 amd64 1:0.9.10-1 [18.7 kB]

2024-09-06 00:02:28,033 - execution_module.docker_manager -raylib - INFO - Get:30 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrandr2 amd64 2:1.5.2-0ubuntu1 [18.5 kB]

2024-09-06 00:02:28,083 - execution_module.docker_manager -raylib - INFO - Get:31 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-randr-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-06 00:02:28,126 - execution_module.docker_manager -raylib - INFO - Get:32 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrender-dev amd64 1:0.9.10-1 [24.9 kB]

2024-09-06 00:02:28,171 - execution_module.docker_manager -raylib - INFO - Get:33 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrandr-dev amd64 2:1.5.2-0ubuntu1 [25.0 kB]

2024-09-06 00:02:28,241 - execution_module.docker_manager -raylib - INFO - Get:34 http://mirrors.aliyun.com/ubuntu focal/main amd64 pkg-config amd64 0.29.1-0ubuntu4 [45.5 kB]

2024-09-06 00:02:28,295 - execution_module.docker_manager -raylib - INFO - Get:35 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-bin amd64 1.18.0-1ubuntu0.1 [20.2 kB]

2024-09-06 00:02:28,360 - execution_module.docker_manager -raylib - INFO - Get:36 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-dev amd64 1.18.0-1ubuntu0.1 [64.6 kB]

2024-09-06 00:02:28,511 - execution_module.docker_manager -raylib - INFO - Get:37 http://mirrors.aliyun.com/ubuntu focal/main amd64 wayland-protocols all 1.20-1 [60.3 kB]

2024-09-06 00:02:29,297 - execution_module.docker_manager -raylib - INFO - [91mdebconf: delaying package configuration, since apt-utils is not installed
[0m
2024-09-06 00:02:29,512 - execution_module.docker_manager -raylib - INFO - Fetched 3220 kB in 3s (1044 kB/s)

2024-09-06 00:02:30,503 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libbsd0:amd64.
(Reading database ... 
2024-09-06 00:02:30,506 - execution_module.docker_manager -raylib - INFO - (Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
2024-09-06 00:02:30,533 - execution_module.docker_manager -raylib - INFO - (Reading database ... 65%
2024-09-06 00:02:30,538 - execution_module.docker_manager -raylib - INFO - (Reading database ... 70%
2024-09-06 00:02:30,618 - execution_module.docker_manager -raylib - INFO - (Reading database ... 75%
2024-09-06 00:02:30,898 - execution_module.docker_manager -raylib - INFO - (Reading database ... 80%
2024-09-06 00:02:31,335 - execution_module.docker_manager -raylib - INFO - (Reading database ... 85%
2024-09-06 00:02:31,566 - execution_module.docker_manager -raylib - INFO - (Reading database ... 90%
2024-09-06 00:02:31,791 - execution_module.docker_manager -raylib - INFO - (Reading database ... 95%
2024-09-06 00:02:31,824 - execution_module.docker_manager -raylib - INFO - (Reading database ... 100%
(Reading database ... 21508 files and directories currently installed.)

2024-09-06 00:02:31,826 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../00-libbsd0_0.10.0-1_amd64.deb ...

2024-09-06 00:02:32,318 - execution_module.docker_manager -raylib - INFO - Unpacking libbsd0:amd64 (0.10.0-1) ...

2024-09-06 00:02:34,160 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xkb-data.

2024-09-06 00:02:34,163 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../01-xkb-data_2.29-2_all.deb ...

2024-09-06 00:02:34,782 - execution_module.docker_manager -raylib - INFO - Unpacking xkb-data (2.29-2) ...

2024-09-06 00:02:38,198 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau6:amd64.

2024-09-06 00:02:38,200 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../02-libxau6_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-06 00:02:38,495 - execution_module.docker_manager -raylib - INFO - Unpacking libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:02:40,877 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp6:amd64.

2024-09-06 00:02:40,887 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../03-libxdmcp6_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-06 00:02:41,395 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:02:43,696 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1:amd64.

2024-09-06 00:02:43,699 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../04-libxcb1_1.14-2_amd64.deb ...

2024-09-06 00:02:44,097 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1:amd64 (1.14-2) ...

2024-09-06 00:02:45,348 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-data.

2024-09-06 00:02:45,351 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../05-libx11-data_2%3a1.6.9-2ubuntu1.6_all.deb ...

2024-09-06 00:02:45,400 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:02:46,328 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-6:amd64.

2024-09-06 00:02:46,330 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../06-libx11-6_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-06 00:02:46,558 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:02:47,591 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxext6:amd64.

2024-09-06 00:02:47,594 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../07-libxext6_2%3a1.3.4-0ubuntu1_amd64.deb ...

2024-09-06 00:02:47,730 - execution_module.docker_manager -raylib - INFO - Unpacking libxext6:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:02:48,532 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libpthread-stubs0-dev:amd64.

2024-09-06 00:02:48,533 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../08-libpthread-stubs0-dev_0.4-1_amd64.deb ...

2024-09-06 00:02:48,601 - execution_module.docker_manager -raylib - INFO - Unpacking libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-06 00:02:49,691 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-client0:amd64.

2024-09-06 00:02:49,693 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../09-libwayland-client0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:02:49,747 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:02:51,691 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-cursor0:amd64.

2024-09-06 00:02:51,704 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../10-libwayland-cursor0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:02:52,217 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:02:54,180 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-egl1:amd64.

2024-09-06 00:02:54,183 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../11-libwayland-egl1_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:02:54,426 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:02:55,009 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-server0:amd64.

2024-09-06 00:02:55,011 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../12-libwayland-server0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:02:55,130 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:02:56,127 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xorg-sgml-doctools.

2024-09-06 00:02:56,199 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../13-xorg-sgml-doctools_1%3a1.11-1_all.deb ...

2024-09-06 00:02:56,304 - execution_module.docker_manager -raylib - INFO - Unpacking xorg-sgml-doctools (1:1.11-1) ...

2024-09-06 00:02:56,716 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-dev.

2024-09-06 00:02:56,717 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../14-x11proto-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:02:56,747 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-dev (2019.2-1ubuntu1) ...

2024-09-06 00:02:57,025 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-core-dev.

2024-09-06 00:02:57,028 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../15-x11proto-core-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:02:57,079 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-06 00:02:58,143 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau-dev:amd64.

2024-09-06 00:02:58,144 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../16-libxau-dev_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-06 00:02:58,177 - execution_module.docker_manager -raylib - INFO - Unpacking libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:02:58,400 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp-dev:amd64.

2024-09-06 00:02:58,401 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../17-libxdmcp-dev_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-06 00:02:59,854 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:03:04,662 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xtrans-dev.

2024-09-06 00:03:04,664 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../18-xtrans-dev_1.4.0-1_all.deb ...

2024-09-06 00:03:05,952 - execution_module.docker_manager -raylib - INFO - Unpacking xtrans-dev (1.4.0-1) ...

2024-09-06 00:03:08,487 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1-dev:amd64.

2024-09-06 00:03:08,532 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../19-libxcb1-dev_1.14-2_amd64.deb ...

2024-09-06 00:03:08,712 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1-dev:amd64 (1.14-2) ...

2024-09-06 00:03:10,149 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-dev:amd64.

2024-09-06 00:03:10,152 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../20-libx11-dev_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-06 00:03:11,128 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:03:13,610 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-xext-dev.

2024-09-06 00:03:13,617 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../21-x11proto-xext-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:03:14,484 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-xext-dev (2019.2-1ubuntu1) ...

2024-09-06 00:03:15,899 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxext-dev:amd64.

2024-09-06 00:03:15,900 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../22-libxext-dev_2%3a1.3.4-0ubuntu1_amd64.deb ...

2024-09-06 00:03:15,970 - execution_module.docker_manager -raylib - INFO - Unpacking libxext-dev:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:03:16,615 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxinerama1:amd64.

2024-09-06 00:03:16,616 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../23-libxinerama1_2%3a1.1.4-2_amd64.deb ...

2024-09-06 00:03:16,661 - execution_module.docker_manager -raylib - INFO - Unpacking libxinerama1:amd64 (2:1.1.4-2) ...

2024-09-06 00:03:19,298 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-xinerama-dev.

2024-09-06 00:03:19,301 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../24-x11proto-xinerama-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:03:19,626 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-xinerama-dev (2019.2-1ubuntu1) ...

2024-09-06 00:03:20,987 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxinerama-dev:amd64.

2024-09-06 00:03:20,989 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../25-libxinerama-dev_2%3a1.1.4-2_amd64.deb ...

2024-09-06 00:03:21,427 - execution_module.docker_manager -raylib - INFO - Unpacking libxinerama-dev:amd64 (2:1.1.4-2) ...

2024-09-06 00:03:23,123 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon0:amd64.

2024-09-06 00:03:23,124 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../26-libxkbcommon0_0.10.0-1_amd64.deb ...

2024-09-06 00:03:23,287 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-06 00:03:25,488 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon-dev:amd64.

2024-09-06 00:03:25,494 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../27-libxkbcommon-dev_0.10.0-1_amd64.deb ...

2024-09-06 00:03:25,839 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-06 00:03:28,152 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrender1:amd64.

2024-09-06 00:03:28,174 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../28-libxrender1_1%3a0.9.10-1_amd64.deb ...

2024-09-06 00:03:28,404 - execution_module.docker_manager -raylib - INFO - Unpacking libxrender1:amd64 (1:0.9.10-1) ...

2024-09-06 00:03:29,393 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrandr2:amd64.

2024-09-06 00:03:29,394 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../29-libxrandr2_2%3a1.5.2-0ubuntu1_amd64.deb ...

2024-09-06 00:03:29,709 - execution_module.docker_manager -raylib - INFO - Unpacking libxrandr2:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:03:30,365 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-randr-dev.

2024-09-06 00:03:30,366 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../30-x11proto-randr-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:03:30,433 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-randr-dev (2019.2-1ubuntu1) ...

2024-09-06 00:03:31,005 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrender-dev:amd64.

2024-09-06 00:03:31,006 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../31-libxrender-dev_1%3a0.9.10-1_amd64.deb ...

2024-09-06 00:03:31,790 - execution_module.docker_manager -raylib - INFO - Unpacking libxrender-dev:amd64 (1:0.9.10-1) ...

2024-09-06 00:03:48,055 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrandr-dev:amd64.

2024-09-06 00:03:48,303 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../32-libxrandr-dev_2%3a1.5.2-0ubuntu1_amd64.deb ...

2024-09-06 00:03:53,127 - execution_module.docker_manager -raylib - INFO - Unpacking libxrandr-dev:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:04:03,467 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package pkg-config.

2024-09-06 00:04:03,513 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../33-pkg-config_0.29.1-0ubuntu4_amd64.deb ...

2024-09-06 00:04:04,596 - execution_module.docker_manager -raylib - INFO - Unpacking pkg-config (0.29.1-0ubuntu4) ...

2024-09-06 00:04:08,902 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-bin.

2024-09-06 00:04:08,931 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../34-libwayland-bin_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:04:09,704 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-06 00:04:13,559 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-dev:amd64.

2024-09-06 00:04:13,562 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../35-libwayland-dev_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:04:15,616 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:04:29,131 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package wayland-protocols.

2024-09-06 00:04:29,188 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../36-wayland-protocols_1.20-1_all.deb ...

2024-09-06 00:04:29,762 - execution_module.docker_manager -raylib - INFO - Unpacking wayland-protocols (1.20-1) ...

2024-09-06 00:04:31,614 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:04:32,588 - execution_module.docker_manager -raylib - INFO - Setting up libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:04:39,088 - execution_module.docker_manager -raylib - INFO - Setting up xkb-data (2.29-2) ...

2024-09-06 00:04:55,803 - execution_module.docker_manager -raylib - INFO - Setting up libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-06 00:04:57,744 - execution_module.docker_manager -raylib - INFO - Setting up xtrans-dev (1.4.0-1) ...

2024-09-06 00:04:58,239 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-06 00:04:59,480 - execution_module.docker_manager -raylib - INFO - Setting up libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:05:01,492 - execution_module.docker_manager -raylib - INFO - Setting up pkg-config (0.29.1-0ubuntu4) ...

2024-09-06 00:05:03,214 - execution_module.docker_manager -raylib - INFO - Setting up wayland-protocols (1.20-1) ...

2024-09-06 00:05:04,326 - execution_module.docker_manager -raylib - INFO - Setting up xorg-sgml-doctools (1:1.11-1) ...

2024-09-06 00:05:05,889 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:05:07,104 - execution_module.docker_manager -raylib - INFO - Setting up libbsd0:amd64 (0.10.0-1) ...

2024-09-06 00:05:08,115 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-06 00:05:08,770 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:05:09,433 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-dev (2019.2-1ubuntu1) ...

2024-09-06 00:05:10,041 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:05:10,715 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1:amd64 (1.14-2) ...

2024-09-06 00:05:11,227 - execution_module.docker_manager -raylib - INFO - Setting up libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:05:11,780 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-randr-dev (2019.2-1ubuntu1) ...

2024-09-06 00:05:12,392 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-06 00:05:12,895 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-xinerama-dev (2019.2-1ubuntu1) ...

2024-09-06 00:05:13,461 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:05:13,984 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-06 00:05:15,084 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-xext-dev (2019.2-1ubuntu1) ...

2024-09-06 00:05:15,676 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:05:16,253 - execution_module.docker_manager -raylib - INFO - Setting up libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:05:16,903 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1-dev:amd64 (1.14-2) ...

2024-09-06 00:05:17,256 - execution_module.docker_manager -raylib - INFO - Setting up libxrender1:amd64 (1:0.9.10-1) ...

2024-09-06 00:05:17,572 - execution_module.docker_manager -raylib - INFO - Setting up libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:05:17,911 - execution_module.docker_manager -raylib - INFO - Setting up libxext6:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:05:18,219 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:05:18,344 - execution_module.docker_manager -raylib - INFO - Setting up libxinerama1:amd64 (2:1.1.4-2) ...

2024-09-06 00:05:18,470 - execution_module.docker_manager -raylib - INFO - Setting up libxrandr2:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:05:18,582 - execution_module.docker_manager -raylib - INFO - Setting up libxext-dev:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:05:18,714 - execution_module.docker_manager -raylib - INFO - Setting up libxrender-dev:amd64 (1:0.9.10-1) ...

2024-09-06 00:05:19,417 - execution_module.docker_manager -raylib - INFO - Setting up libxrandr-dev:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:05:19,925 - execution_module.docker_manager -raylib - INFO - Setting up libxinerama-dev:amd64 (2:1.1.4-2) ...

2024-09-06 00:05:20,082 - execution_module.docker_manager -raylib - INFO - Processing triggers for libc-bin (2.31-0ubuntu9.16) ...

2024-09-06 00:07:37,699 - execution_module.docker_manager -raylib - INFO -  ---> 6cb475a3c0ba

2024-09-06 00:07:37,699 - execution_module.docker_manager -raylib - INFO - Step 11/16 : RUN mkdir /tmp/raylib
2024-09-06 00:07:38,395 - execution_module.docker_manager -raylib - INFO -  ---> Running in 947a68645fbb

2024-09-06 00:07:40,500 - execution_module.docker_manager -raylib - INFO -  ---> 8869d8945ffd

2024-09-06 00:07:40,500 - execution_module.docker_manager -raylib - INFO - Step 12/16 : COPY ./raylib /tmp/raylib
2024-09-06 00:08:45,464 - execution_module.docker_manager -raylib - INFO -  ---> de339f0a4ee7

2024-09-06 00:08:45,465 - execution_module.docker_manager -raylib - INFO - Step 13/16 : WORKDIR /tmp/raylib
2024-09-06 00:09:09,240 - execution_module.docker_manager -raylib - INFO -  ---> Running in 0da09d215ff1

2024-09-06 00:10:45,084 - execution_module.docker_manager -raylib - INFO -  ---> 1c94cc36ef9b

2024-09-06 00:10:45,084 - execution_module.docker_manager -raylib - INFO - Step 14/16 : RUN cmake -B build
2024-09-06 00:10:56,880 - execution_module.docker_manager -raylib - INFO -  ---> Running in 7cd81deab00a

2024-09-06 00:10:59,569 - execution_module.docker_manager -raylib - INFO - -- The C compiler identification is GNU 9.4.0

2024-09-06 00:10:59,670 - execution_module.docker_manager -raylib - INFO - -- The CXX compiler identification is GNU 9.4.0

2024-09-06 00:10:59,674 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc

2024-09-06 00:10:59,826 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc -- works

2024-09-06 00:10:59,827 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info

2024-09-06 00:10:59,999 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info - done

2024-09-06 00:11:00,009 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features

2024-09-06 00:11:00,010 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features - done

2024-09-06 00:11:00,040 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++

2024-09-06 00:11:00,309 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++ -- works

2024-09-06 00:11:00,311 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info

2024-09-06 00:11:00,565 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info - done

2024-09-06 00:11:00,580 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features

2024-09-06 00:11:00,581 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features - done

2024-09-06 00:11:00,583 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES

2024-09-06 00:11:00,907 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES - Success

2024-09-06 00:11:00,908 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=pointer-arith can be used -- compiles

2024-09-06 00:11:00,908 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=implicit-function-declaration can be used -- compiles

2024-09-06 00:11:00,908 - execution_module.docker_manager -raylib - INFO - -- Testing if -fno-strict-aliasing can be used -- compiles

2024-09-06 00:11:00,912 - execution_module.docker_manager -raylib - INFO - -- Setting build type to 'Debug' as none was specified.

2024-09-06 00:11:00,912 - execution_module.docker_manager -raylib - INFO - -- Using raylib's GLFW

2024-09-06 00:11:00,915 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h

2024-09-06 00:11:01,628 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h - found

2024-09-06 00:11:01,628 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD

2024-09-06 00:11:01,856 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed

2024-09-06 00:11:01,857 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads

2024-09-06 00:11:02,040 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads - not found

2024-09-06 00:11:02,041 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread

2024-09-06 00:11:02,302 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread - found

2024-09-06 00:11:02,303 - execution_module.docker_manager -raylib - INFO - -- Found Threads: TRUE  

2024-09-06 00:11:02,303 - execution_module.docker_manager -raylib - INFO - -- Including Wayland support
-- Including X11 support

2024-09-06 00:11:02,305 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create

2024-09-06 00:11:02,714 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create - found

2024-09-06 00:11:02,998 - execution_module.docker_manager -raylib - INFO - -- Found PkgConfig: /usr/bin/pkg-config (found version "0.29.1") 

2024-09-06 00:11:02,999 - execution_module.docker_manager -raylib - INFO - -- Checking for modules 'wayland-client>=0.2.7;wayland-cursor>=0.2.7;wayland-egl>=0.2.7;xkbcommon>=0.5.0'

2024-09-06 00:11:03,065 - execution_module.docker_manager -raylib - INFO - --   Found wayland-client, version 1.18.0

2024-09-06 00:11:03,120 - execution_module.docker_manager -raylib - INFO - --   Found wayland-cursor, version 1.18.0

2024-09-06 00:11:03,120 - execution_module.docker_manager -raylib - INFO - --   Found wayland-egl, version 18.1.0

2024-09-06 00:11:03,120 - execution_module.docker_manager -raylib - INFO - --   Found xkbcommon, version 0.10.0

2024-09-06 00:11:03,240 - execution_module.docker_manager -raylib - INFO - -- Found X11: /usr/include   

2024-09-06 00:11:03,282 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so

2024-09-06 00:11:03,692 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so - found

2024-09-06 00:11:03,693 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname

2024-09-06 00:11:03,852 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname - found

2024-09-06 00:11:03,853 - execution_module.docker_manager -raylib - INFO - -- Looking for connect

2024-09-06 00:11:04,042 - execution_module.docker_manager -raylib - INFO - -- Looking for connect - found

2024-09-06 00:11:04,043 - execution_module.docker_manager -raylib - INFO - -- Looking for remove

2024-09-06 00:11:04,567 - execution_module.docker_manager -raylib - INFO - -- Looking for remove - found

2024-09-06 00:11:04,567 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat

2024-09-06 00:11:04,957 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat - found

2024-09-06 00:11:04,958 - execution_module.docker_manager -raylib - INFO - [91mCMake Error at src/external/glfw/src/CMakeLists.txt:204 (message):
  Xcursor headers not found; install libxcursor development package


[0m
2024-09-06 00:11:04,979 - execution_module.docker_manager -raylib - INFO - -- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".

2024-09-06 00:11:09,614 - cxxcrafter -raylib - INFO - Execution Module Finishes
2024-09-06 00:11:09,614 - cxxcrafter -raylib - ERROR - Execution failed with error: -- Looking for shmat
-- Looking for shmat - found
[91mCMake Error at src/external/glfw/src/CMakeLists.txt:204 (message):
  Xcursor headers not found; install libxcursor development package


[0m-- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".
The command '/bin/sh -c cmake -B build' returned a non-zero code: 1
2024-09-06 00:11:09,614 - cxxcrafter -raylib - INFO - Modifier Module Starts
2024-09-06 00:11:14,182 - cxxcrafter -raylib - INFO - Modifier Module Finishes
2024-09-06 00:11:14,182 - cxxcrafter -raylib - INFO - Execution Module Starts
2024-09-06 00:11:32,380 - execution_module.docker_manager -raylib - INFO - Step 1/16 : FROM ubuntu:20.04
2024-09-06 00:11:32,380 - execution_module.docker_manager -raylib - INFO -  ---> 5f5250218d28

2024-09-06 00:11:32,380 - execution_module.docker_manager -raylib - INFO - Step 2/16 : ENV DEBIAN_FRONTEND=noninteractive
2024-09-06 00:11:32,380 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:11:32,380 - execution_module.docker_manager -raylib - INFO -  ---> defc00a448c4

2024-09-06 00:11:32,380 - execution_module.docker_manager -raylib - INFO - Step 3/16 : RUN sed -i 's|http://archive.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-06 00:11:32,380 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:11:32,380 - execution_module.docker_manager -raylib - INFO -  ---> 7134b497e1a1

2024-09-06 00:11:32,380 - execution_module.docker_manager -raylib - INFO - Step 4/16 : RUN sed -i 's|http://security.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-06 00:11:32,380 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:11:32,380 - execution_module.docker_manager -raylib - INFO -  ---> ea57a31cfcfc

2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO - Step 5/16 : RUN apt-get update
2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO -  ---> 4041e8821132

2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO - Step 6/16 : RUN apt-get upgrade -y
2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO -  ---> afe371a706bb

2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO - Step 7/16 : RUN apt-get install -y build-essential
2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO -  ---> 29b06df4a18c

2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO - Step 8/16 : RUN apt-get install -y software-properties-common
2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO -  ---> 760c93dbc806

2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO - Step 9/16 : RUN apt-get install -y cmake
2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO -  ---> 0e6b9331b4ee

2024-09-06 00:11:32,381 - execution_module.docker_manager -raylib - INFO - Step 10/16 : RUN apt-get install -y libwayland-dev wayland-protocols pkg-config libx11-dev libxkbcommon-dev libxrandr-dev libxinerama-dev libxcursor-dev
2024-09-06 00:12:02,947 - execution_module.docker_manager -raylib - INFO -  ---> Running in 2605f15d016c

2024-09-06 00:12:03,434 - execution_module.docker_manager -raylib - INFO - Reading package lists...
2024-09-06 00:12:04,236 - execution_module.docker_manager -raylib - INFO - Building dependency tree...
2024-09-06 00:12:04,420 - execution_module.docker_manager -raylib - INFO - 
Reading state information...

2024-09-06 00:12:04,467 - execution_module.docker_manager -raylib - INFO - The following additional packages will be installed:

2024-09-06 00:12:04,467 - execution_module.docker_manager -raylib - INFO -   libbsd0 libpthread-stubs0-dev libwayland-bin libwayland-client0
  libwayland-cursor0 libwayland-egl1 libwayland-server0 libx11-6 libx11-data
  libxau-dev libxau6 libxcb1 libxcb1-dev libxcursor1 libxdmcp-dev libxdmcp6
  libxext-dev libxext6 libxfixes-dev libxfixes3 libxinerama1 libxkbcommon0

2024-09-06 00:12:04,467 - execution_module.docker_manager -raylib - INFO -   libxrandr2 libxrender-dev libxrender1 x11proto-core-dev x11proto-dev
  x11proto-randr-dev x11proto-xext-dev x11proto-xinerama-dev xkb-data
  xorg-sgml-doctools xtrans-dev

2024-09-06 00:12:04,468 - execution_module.docker_manager -raylib - INFO - Suggested packages:
  libwayland-doc libx11-doc libxcb-doc libxext-doc

2024-09-06 00:12:04,509 - execution_module.docker_manager -raylib - INFO - The following NEW packages will be installed:

2024-09-06 00:12:04,621 - execution_module.docker_manager -raylib - INFO -   libbsd0 libpthread-stubs0-dev libwayland-bin libwayland-client0
  libwayland-cursor0 libwayland-dev libwayland-egl1 libwayland-server0
  libx11-6 libx11-data libx11-dev libxau-dev libxau6 libxcb1 libxcb1-dev
  libxcursor-dev libxcursor1 libxdmcp-dev libxdmcp6 libxext-dev libxext6
  libxfixes-dev libxfixes3 libxinerama-dev libxinerama1 libxkbcommon-dev

2024-09-06 00:12:04,621 - execution_module.docker_manager -raylib - INFO -   libxkbcommon0 libxrandr-dev libxrandr2 libxrender-dev libxrender1 pkg-config
  wayland-protocols x11proto-core-dev x11proto-dev x11proto-randr-dev
  x11proto-xext-dev x11proto-xinerama-dev xkb-data xorg-sgml-doctools
  xtrans-dev

2024-09-06 00:12:04,680 - execution_module.docker_manager -raylib - INFO - 0 upgraded, 41 newly installed, 0 to remove and 0 not upgraded.
Need to get 3289 kB of archives.
After this operation, 16.5 MB of additional disk space will be used.
Get:1 http://mirrors.aliyun.com/ubuntu focal/main amd64 libbsd0 amd64 0.10.0-1 [45.4 kB]

2024-09-06 00:12:05,060 - execution_module.docker_manager -raylib - INFO - Get:2 http://mirrors.aliyun.com/ubuntu focal/main amd64 xkb-data all 2.29-2 [349 kB]

2024-09-06 00:12:05,444 - execution_module.docker_manager -raylib - INFO - Get:3 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau6 amd64 1:1.0.9-0ubuntu1 [7488 B]

2024-09-06 00:12:05,492 - execution_module.docker_manager -raylib - INFO - Get:4 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp6 amd64 1:1.1.3-0ubuntu1 [10.6 kB]

2024-09-06 00:12:05,710 - execution_module.docker_manager -raylib - INFO - Get:5 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1 amd64 1.14-2 [44.7 kB]

2024-09-06 00:12:05,782 - execution_module.docker_manager -raylib - INFO - Get:6 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-data all 2:1.6.9-2ubuntu1.6 [114 kB]

2024-09-06 00:12:05,994 - execution_module.docker_manager -raylib - INFO - Get:7 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-6 amd64 2:1.6.9-2ubuntu1.6 [577 kB]

2024-09-06 00:12:06,445 - execution_module.docker_manager -raylib - INFO - Get:8 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxext6 amd64 2:1.3.4-0ubuntu1 [29.1 kB]

2024-09-06 00:12:06,550 - execution_module.docker_manager -raylib - INFO - Get:9 http://mirrors.aliyun.com/ubuntu focal/main amd64 libpthread-stubs0-dev amd64 0.4-1 [5384 B]

2024-09-06 00:12:06,609 - execution_module.docker_manager -raylib - INFO - Get:10 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-client0 amd64 1.18.0-1ubuntu0.1 [23.9 kB]

2024-09-06 00:12:06,757 - execution_module.docker_manager -raylib - INFO - Get:11 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-cursor0 amd64 1.18.0-1ubuntu0.1 [10.3 kB]

2024-09-06 00:12:06,810 - execution_module.docker_manager -raylib - INFO - Get:12 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-egl1 amd64 1.18.0-1ubuntu0.1 [5596 B]

2024-09-06 00:12:06,867 - execution_module.docker_manager -raylib - INFO - Get:13 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-server0 amd64 1.18.0-1ubuntu0.1 [31.3 kB]

2024-09-06 00:12:06,923 - execution_module.docker_manager -raylib - INFO - Get:14 http://mirrors.aliyun.com/ubuntu focal/main amd64 xorg-sgml-doctools all 1:1.11-1 [12.9 kB]

2024-09-06 00:12:06,972 - execution_module.docker_manager -raylib - INFO - Get:15 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-dev all 2019.2-1ubuntu1 [594 kB]

2024-09-06 00:12:07,557 - execution_module.docker_manager -raylib - INFO - Get:16 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-core-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-06 00:12:07,681 - execution_module.docker_manager -raylib - INFO - Get:17 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau-dev amd64 1:1.0.9-0ubuntu1 [9552 B]

2024-09-06 00:12:07,787 - execution_module.docker_manager -raylib - INFO - Get:18 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp-dev amd64 1:1.1.3-0ubuntu1 [25.3 kB]

2024-09-06 00:12:07,949 - execution_module.docker_manager -raylib - INFO - Get:19 http://mirrors.aliyun.com/ubuntu focal/main amd64 xtrans-dev all 1.4.0-1 [68.9 kB]

2024-09-06 00:12:08,087 - execution_module.docker_manager -raylib - INFO - Get:20 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1-dev amd64 1.14-2 [80.5 kB]

2024-09-06 00:12:08,351 - execution_module.docker_manager -raylib - INFO - Get:21 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-dev amd64 2:1.6.9-2ubuntu1.6 [648 kB]

2024-09-06 00:12:08,861 - execution_module.docker_manager -raylib - INFO - Get:22 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxfixes3 amd64 1:5.0.3-2 [10.9 kB]

2024-09-06 00:12:09,134 - execution_module.docker_manager -raylib - INFO - Get:23 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrender1 amd64 1:0.9.10-1 [18.7 kB]

2024-09-06 00:12:09,412 - execution_module.docker_manager -raylib - INFO - Get:24 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcursor1 amd64 1:1.2.0-2 [20.1 kB]

2024-09-06 00:12:09,829 - execution_module.docker_manager -raylib - INFO - Get:25 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrender-dev amd64 1:0.9.10-1 [24.9 kB]

2024-09-06 00:12:10,239 - execution_module.docker_manager -raylib - INFO - Get:26 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxfixes-dev amd64 1:5.0.3-2 [11.4 kB]

2024-09-06 00:12:10,406 - execution_module.docker_manager -raylib - INFO - Get:27 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcursor-dev amd64 1:1.2.0-2 [26.5 kB]

2024-09-06 00:12:10,747 - execution_module.docker_manager -raylib - INFO - Get:28 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-xext-dev all 2019.2-1ubuntu1 [2616 B]

2024-09-06 00:12:10,812 - execution_module.docker_manager -raylib - INFO - Get:29 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxext-dev amd64 2:1.3.4-0ubuntu1 [82.2 kB]

2024-09-06 00:12:11,436 - execution_module.docker_manager -raylib - INFO - Get:30 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxinerama1 amd64 2:1.1.4-2 [6904 B]

2024-09-06 00:12:11,623 - execution_module.docker_manager -raylib - INFO - Get:31 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-xinerama-dev all 2019.2-1ubuntu1 [2628 B]

2024-09-06 00:12:12,046 - execution_module.docker_manager -raylib - INFO - Get:32 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxinerama-dev amd64 2:1.1.4-2 [7896 B]

2024-09-06 00:12:12,119 - execution_module.docker_manager -raylib - INFO - Get:33 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon0 amd64 0.10.0-1 [98.4 kB]

2024-09-06 00:12:12,297 - execution_module.docker_manager -raylib - INFO - Get:34 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon-dev amd64 0.10.0-1 [45.4 kB]

2024-09-06 00:12:12,729 - execution_module.docker_manager -raylib - INFO - Get:35 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrandr2 amd64 2:1.5.2-0ubuntu1 [18.5 kB]

2024-09-06 00:12:13,008 - execution_module.docker_manager -raylib - INFO - Get:36 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-randr-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-06 00:12:13,599 - execution_module.docker_manager -raylib - INFO - Get:37 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrandr-dev amd64 2:1.5.2-0ubuntu1 [25.0 kB]

2024-09-06 00:12:13,754 - execution_module.docker_manager -raylib - INFO - Get:38 http://mirrors.aliyun.com/ubuntu focal/main amd64 pkg-config amd64 0.29.1-0ubuntu4 [45.5 kB]

2024-09-06 00:12:14,336 - execution_module.docker_manager -raylib - INFO - Get:39 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-bin amd64 1.18.0-1ubuntu0.1 [20.2 kB]

2024-09-06 00:12:14,599 - execution_module.docker_manager -raylib - INFO - Get:40 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-dev amd64 1.18.0-1ubuntu0.1 [64.6 kB]

2024-09-06 00:12:15,001 - execution_module.docker_manager -raylib - INFO - Get:41 http://mirrors.aliyun.com/ubuntu focal/main amd64 wayland-protocols all 1.20-1 [60.3 kB]

2024-09-06 00:12:37,303 - execution_module.docker_manager -raylib - INFO - [91mdebconf: delaying package configuration, since apt-utils is not installed
[0m
2024-09-06 00:12:37,990 - execution_module.docker_manager -raylib - INFO - Fetched 3289 kB in 11s (299 kB/s)

2024-09-06 00:12:40,675 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libbsd0:amd64.
(Reading database ... 
2024-09-06 00:12:40,735 - execution_module.docker_manager -raylib - INFO - (Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
2024-09-06 00:12:40,735 - execution_module.docker_manager -raylib - INFO - (Reading database ... 65%
2024-09-06 00:12:40,735 - execution_module.docker_manager -raylib - INFO - (Reading database ... 70%
2024-09-06 00:12:40,736 - execution_module.docker_manager -raylib - INFO - (Reading database ... 75%
2024-09-06 00:12:40,736 - execution_module.docker_manager -raylib - INFO - (Reading database ... 80%
2024-09-06 00:12:40,736 - execution_module.docker_manager -raylib - INFO - (Reading database ... 85%
2024-09-06 00:12:40,736 - execution_module.docker_manager -raylib - INFO - (Reading database ... 90%
2024-09-06 00:12:40,736 - execution_module.docker_manager -raylib - INFO - (Reading database ... 95%
2024-09-06 00:12:40,736 - execution_module.docker_manager -raylib - INFO - (Reading database ... 100%
(Reading database ... 21508 files and directories currently installed.)

2024-09-06 00:12:40,736 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../00-libbsd0_0.10.0-1_amd64.deb ...

2024-09-06 00:12:43,947 - execution_module.docker_manager -raylib - INFO - Unpacking libbsd0:amd64 (0.10.0-1) ...

2024-09-06 00:12:49,954 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xkb-data.

2024-09-06 00:12:50,782 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../01-xkb-data_2.29-2_all.deb ...

2024-09-06 00:12:53,622 - execution_module.docker_manager -raylib - INFO - Unpacking xkb-data (2.29-2) ...

2024-09-06 00:12:56,560 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau6:amd64.

2024-09-06 00:12:56,562 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../02-libxau6_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-06 00:12:57,294 - execution_module.docker_manager -raylib - INFO - Unpacking libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:13:00,261 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp6:amd64.

2024-09-06 00:13:00,263 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../03-libxdmcp6_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-06 00:13:00,467 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:13:02,889 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1:amd64.

2024-09-06 00:13:02,892 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../04-libxcb1_1.14-2_amd64.deb ...

2024-09-06 00:13:03,035 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1:amd64 (1.14-2) ...

2024-09-06 00:13:05,999 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-data.

2024-09-06 00:13:06,002 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../05-libx11-data_2%3a1.6.9-2ubuntu1.6_all.deb ...

2024-09-06 00:13:06,606 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:13:08,811 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-6:amd64.

2024-09-06 00:13:08,815 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../06-libx11-6_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-06 00:13:09,147 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:13:10,782 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxext6:amd64.

2024-09-06 00:13:10,935 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../07-libxext6_2%3a1.3.4-0ubuntu1_amd64.deb ...

2024-09-06 00:13:11,487 - execution_module.docker_manager -raylib - INFO - Unpacking libxext6:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:13:14,207 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libpthread-stubs0-dev:amd64.

2024-09-06 00:13:14,227 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../08-libpthread-stubs0-dev_0.4-1_amd64.deb ...

2024-09-06 00:13:14,430 - execution_module.docker_manager -raylib - INFO - Unpacking libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-06 00:13:16,538 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-client0:amd64.

2024-09-06 00:13:16,541 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../09-libwayland-client0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:13:16,688 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:13:20,515 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-cursor0:amd64.

2024-09-06 00:13:20,517 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../10-libwayland-cursor0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:13:21,159 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:13:24,154 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-egl1:amd64.

2024-09-06 00:13:24,223 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../11-libwayland-egl1_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:13:24,384 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:13:26,929 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-server0:amd64.

2024-09-06 00:13:26,931 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../12-libwayland-server0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:13:27,571 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:13:29,950 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xorg-sgml-doctools.

2024-09-06 00:13:29,962 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../13-xorg-sgml-doctools_1%3a1.11-1_all.deb ...

2024-09-06 00:13:30,249 - execution_module.docker_manager -raylib - INFO - Unpacking xorg-sgml-doctools (1:1.11-1) ...

2024-09-06 00:13:31,456 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-dev.

2024-09-06 00:13:31,463 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../14-x11proto-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:13:31,769 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-dev (2019.2-1ubuntu1) ...

2024-09-06 00:13:33,770 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-core-dev.

2024-09-06 00:13:33,772 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../15-x11proto-core-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:13:33,980 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-06 00:13:35,620 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau-dev:amd64.

2024-09-06 00:13:35,623 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../16-libxau-dev_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-06 00:13:35,867 - execution_module.docker_manager -raylib - INFO - Unpacking libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:13:36,977 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp-dev:amd64.

2024-09-06 00:13:36,979 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../17-libxdmcp-dev_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-06 00:13:37,158 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:13:38,261 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xtrans-dev.

2024-09-06 00:13:38,264 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../18-xtrans-dev_1.4.0-1_all.deb ...

2024-09-06 00:13:38,802 - execution_module.docker_manager -raylib - INFO - Unpacking xtrans-dev (1.4.0-1) ...

2024-09-06 00:13:40,250 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1-dev:amd64.

2024-09-06 00:13:40,253 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../19-libxcb1-dev_1.14-2_amd64.deb ...

2024-09-06 00:13:40,474 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1-dev:amd64 (1.14-2) ...

2024-09-06 00:13:43,485 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-dev:amd64.

2024-09-06 00:13:43,507 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../20-libx11-dev_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-06 00:13:44,574 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:13:49,904 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxfixes3:amd64.

2024-09-06 00:13:49,907 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../21-libxfixes3_1%3a5.0.3-2_amd64.deb ...

2024-09-06 00:13:56,408 - execution_module.docker_manager -raylib - INFO - Unpacking libxfixes3:amd64 (1:5.0.3-2) ...

2024-09-06 00:14:08,326 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrender1:amd64.

2024-09-06 00:14:08,329 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../22-libxrender1_1%3a0.9.10-1_amd64.deb ...

2024-09-06 00:14:08,670 - execution_module.docker_manager -raylib - INFO - Unpacking libxrender1:amd64 (1:0.9.10-1) ...

2024-09-06 00:14:10,103 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcursor1:amd64.

2024-09-06 00:14:10,106 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../23-libxcursor1_1%3a1.2.0-2_amd64.deb ...

2024-09-06 00:14:10,444 - execution_module.docker_manager -raylib - INFO - Unpacking libxcursor1:amd64 (1:1.2.0-2) ...

2024-09-06 00:14:11,261 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrender-dev:amd64.

2024-09-06 00:14:11,264 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../24-libxrender-dev_1%3a0.9.10-1_amd64.deb ...

2024-09-06 00:14:11,554 - execution_module.docker_manager -raylib - INFO - Unpacking libxrender-dev:amd64 (1:0.9.10-1) ...

2024-09-06 00:14:12,288 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxfixes-dev:amd64.

2024-09-06 00:14:12,306 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../25-libxfixes-dev_1%3a5.0.3-2_amd64.deb ...

2024-09-06 00:14:12,451 - execution_module.docker_manager -raylib - INFO - Unpacking libxfixes-dev:amd64 (1:5.0.3-2) ...

2024-09-06 00:14:13,176 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcursor-dev:amd64.

2024-09-06 00:14:13,179 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../26-libxcursor-dev_1%3a1.2.0-2_amd64.deb ...

2024-09-06 00:14:13,296 - execution_module.docker_manager -raylib - INFO - Unpacking libxcursor-dev:amd64 (1:1.2.0-2) ...

2024-09-06 00:14:14,168 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-xext-dev.

2024-09-06 00:14:14,169 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../27-x11proto-xext-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:14:14,258 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-xext-dev (2019.2-1ubuntu1) ...

2024-09-06 00:14:16,131 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxext-dev:amd64.

2024-09-06 00:14:16,133 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../28-libxext-dev_2%3a1.3.4-0ubuntu1_amd64.deb ...

2024-09-06 00:14:16,778 - execution_module.docker_manager -raylib - INFO - Unpacking libxext-dev:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:14:19,357 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxinerama1:amd64.

2024-09-06 00:14:19,359 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../29-libxinerama1_2%3a1.1.4-2_amd64.deb ...

2024-09-06 00:14:19,583 - execution_module.docker_manager -raylib - INFO - Unpacking libxinerama1:amd64 (2:1.1.4-2) ...

2024-09-06 00:14:24,451 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-xinerama-dev.

2024-09-06 00:14:24,454 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../30-x11proto-xinerama-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:14:24,702 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-xinerama-dev (2019.2-1ubuntu1) ...

2024-09-06 00:14:28,870 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxinerama-dev:amd64.

2024-09-06 00:14:28,873 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../31-libxinerama-dev_2%3a1.1.4-2_amd64.deb ...

2024-09-06 00:14:29,805 - execution_module.docker_manager -raylib - INFO - Unpacking libxinerama-dev:amd64 (2:1.1.4-2) ...

2024-09-06 00:14:33,770 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon0:amd64.

2024-09-06 00:14:33,893 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../32-libxkbcommon0_0.10.0-1_amd64.deb ...

2024-09-06 00:14:35,193 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-06 00:14:36,677 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon-dev:amd64.

2024-09-06 00:14:36,679 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../33-libxkbcommon-dev_0.10.0-1_amd64.deb ...

2024-09-06 00:14:36,871 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-06 00:14:42,424 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrandr2:amd64.

2024-09-06 00:14:42,426 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../34-libxrandr2_2%3a1.5.2-0ubuntu1_amd64.deb ...

2024-09-06 00:14:42,878 - execution_module.docker_manager -raylib - INFO - Unpacking libxrandr2:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:14:45,625 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-randr-dev.

2024-09-06 00:14:45,628 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../35-x11proto-randr-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:14:45,802 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-randr-dev (2019.2-1ubuntu1) ...

2024-09-06 00:14:48,459 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrandr-dev:amd64.

2024-09-06 00:14:48,461 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../36-libxrandr-dev_2%3a1.5.2-0ubuntu1_amd64.deb ...

2024-09-06 00:14:49,232 - execution_module.docker_manager -raylib - INFO - Unpacking libxrandr-dev:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:14:53,756 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package pkg-config.

2024-09-06 00:14:53,759 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../37-pkg-config_0.29.1-0ubuntu4_amd64.deb ...

2024-09-06 00:14:55,223 - execution_module.docker_manager -raylib - INFO - Unpacking pkg-config (0.29.1-0ubuntu4) ...

2024-09-06 00:15:07,552 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-bin.

2024-09-06 00:15:07,596 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../38-libwayland-bin_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:15:11,063 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-06 00:15:16,632 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-dev:amd64.

2024-09-06 00:15:16,634 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../39-libwayland-dev_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:15:17,920 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:15:23,313 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package wayland-protocols.

2024-09-06 00:15:23,346 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../40-wayland-protocols_1.20-1_all.deb ...

2024-09-06 00:15:24,356 - execution_module.docker_manager -raylib - INFO - Unpacking wayland-protocols (1.20-1) ...

2024-09-06 00:15:33,090 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:15:49,889 - execution_module.docker_manager -raylib - INFO - Setting up libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:15:53,669 - execution_module.docker_manager -raylib - INFO - Setting up xkb-data (2.29-2) ...

2024-09-06 00:15:56,590 - execution_module.docker_manager -raylib - INFO - Setting up libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-06 00:15:58,541 - execution_module.docker_manager -raylib - INFO - Setting up xtrans-dev (1.4.0-1) ...

2024-09-06 00:15:59,944 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-06 00:16:00,932 - execution_module.docker_manager -raylib - INFO - Setting up libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:16:02,176 - execution_module.docker_manager -raylib - INFO - Setting up pkg-config (0.29.1-0ubuntu4) ...

2024-09-06 00:16:03,294 - execution_module.docker_manager -raylib - INFO - Setting up wayland-protocols (1.20-1) ...

2024-09-06 00:16:03,868 - execution_module.docker_manager -raylib - INFO - Setting up xorg-sgml-doctools (1:1.11-1) ...

2024-09-06 00:16:04,562 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:16:06,059 - execution_module.docker_manager -raylib - INFO - Setting up libbsd0:amd64 (0.10.0-1) ...

2024-09-06 00:16:06,840 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-06 00:16:07,494 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:16:09,223 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-dev (2019.2-1ubuntu1) ...

2024-09-06 00:16:10,922 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:16:12,934 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1:amd64 (1.14-2) ...

2024-09-06 00:16:14,830 - execution_module.docker_manager -raylib - INFO - Setting up libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:16:19,553 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-randr-dev (2019.2-1ubuntu1) ...

2024-09-06 00:16:22,630 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-06 00:16:25,181 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-xinerama-dev (2019.2-1ubuntu1) ...

2024-09-06 00:16:26,679 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:16:28,526 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-06 00:16:30,526 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-xext-dev (2019.2-1ubuntu1) ...

2024-09-06 00:16:31,961 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:16:32,807 - execution_module.docker_manager -raylib - INFO - Setting up libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:16:33,824 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1-dev:amd64 (1.14-2) ...

2024-09-06 00:16:34,511 - execution_module.docker_manager -raylib - INFO - Setting up libxrender1:amd64 (1:0.9.10-1) ...

2024-09-06 00:16:35,244 - execution_module.docker_manager -raylib - INFO - Setting up libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:16:35,950 - execution_module.docker_manager -raylib - INFO - Setting up libxext6:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:16:36,670 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:16:37,553 - execution_module.docker_manager -raylib - INFO - Setting up libxfixes3:amd64 (1:5.0.3-2) ...

2024-09-06 00:16:38,137 - execution_module.docker_manager -raylib - INFO - Setting up libxinerama1:amd64 (2:1.1.4-2) ...

2024-09-06 00:16:38,516 - execution_module.docker_manager -raylib - INFO - Setting up libxrandr2:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:16:38,955 - execution_module.docker_manager -raylib - INFO - Setting up libxext-dev:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:16:40,583 - execution_module.docker_manager -raylib - INFO - Setting up libxrender-dev:amd64 (1:0.9.10-1) ...

2024-09-06 00:16:41,207 - execution_module.docker_manager -raylib - INFO - Setting up libxcursor1:amd64 (1:1.2.0-2) ...

2024-09-06 00:16:41,731 - execution_module.docker_manager -raylib - INFO - Setting up libxfixes-dev:amd64 (1:5.0.3-2) ...

2024-09-06 00:16:42,131 - execution_module.docker_manager -raylib - INFO - Setting up libxrandr-dev:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:16:42,299 - execution_module.docker_manager -raylib - INFO - Setting up libxinerama-dev:amd64 (2:1.1.4-2) ...

2024-09-06 00:16:42,467 - execution_module.docker_manager -raylib - INFO - Setting up libxcursor-dev:amd64 (1:1.2.0-2) ...

2024-09-06 00:16:43,171 - execution_module.docker_manager -raylib - INFO - Processing triggers for libc-bin (2.31-0ubuntu9.16) ...

2024-09-06 00:17:48,497 - execution_module.docker_manager -raylib - INFO -  ---> e1f42e9ff6d4

2024-09-06 00:17:48,498 - execution_module.docker_manager -raylib - INFO - Step 11/16 : RUN mkdir /tmp/raylib
2024-09-06 00:18:08,400 - execution_module.docker_manager -raylib - INFO -  ---> Running in 08dfd24025c8

2024-09-06 00:20:16,335 - execution_module.docker_manager -raylib - INFO -  ---> 51bb13acccc8

2024-09-06 00:20:16,336 - execution_module.docker_manager -raylib - INFO - Step 12/16 : COPY ./raylib /tmp/raylib
2024-09-06 00:21:13,392 - execution_module.docker_manager -raylib - INFO -  ---> 9798f62f8696

2024-09-06 00:21:13,392 - execution_module.docker_manager -raylib - INFO - Step 13/16 : WORKDIR /tmp/raylib
2024-09-06 00:21:13,956 - execution_module.docker_manager -raylib - INFO -  ---> Running in 88fb426d6f49

2024-09-06 00:21:37,335 - execution_module.docker_manager -raylib - INFO -  ---> 0edb8c21c8e6

2024-09-06 00:21:37,335 - execution_module.docker_manager -raylib - INFO - Step 14/16 : RUN cmake -B build
2024-09-06 00:21:38,814 - execution_module.docker_manager -raylib - INFO -  ---> Running in 40e54b63970f

2024-09-06 00:21:40,323 - execution_module.docker_manager -raylib - INFO - -- The C compiler identification is GNU 9.4.0

2024-09-06 00:21:40,509 - execution_module.docker_manager -raylib - INFO - -- The CXX compiler identification is GNU 9.4.0

2024-09-06 00:21:40,512 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc

2024-09-06 00:21:40,621 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc -- works

2024-09-06 00:21:40,623 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info

2024-09-06 00:21:40,748 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info - done

2024-09-06 00:21:40,761 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features

2024-09-06 00:21:40,761 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features - done

2024-09-06 00:21:40,763 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++

2024-09-06 00:21:40,961 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++ -- works

2024-09-06 00:21:40,962 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info

2024-09-06 00:21:41,145 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info - done

2024-09-06 00:21:41,194 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features

2024-09-06 00:21:41,195 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features - done

2024-09-06 00:21:41,202 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES

2024-09-06 00:21:41,320 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES - Success

2024-09-06 00:21:41,320 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=pointer-arith can be used -- compiles

2024-09-06 00:21:41,320 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=implicit-function-declaration can be used -- compiles

2024-09-06 00:21:41,320 - execution_module.docker_manager -raylib - INFO - -- Testing if -fno-strict-aliasing can be used -- compiles

2024-09-06 00:21:41,329 - execution_module.docker_manager -raylib - INFO - -- Setting build type to 'Debug' as none was specified.

2024-09-06 00:21:41,332 - execution_module.docker_manager -raylib - INFO - -- Using raylib's GLFW

2024-09-06 00:21:41,338 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h

2024-09-06 00:21:41,792 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h - found

2024-09-06 00:21:41,793 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD

2024-09-06 00:21:42,085 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed

2024-09-06 00:21:42,085 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads

2024-09-06 00:21:42,378 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads - not found

2024-09-06 00:21:42,378 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread

2024-09-06 00:21:42,623 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread - found

2024-09-06 00:21:42,624 - execution_module.docker_manager -raylib - INFO - -- Found Threads: TRUE  

2024-09-06 00:21:42,624 - execution_module.docker_manager -raylib - INFO - -- Including Wayland support
-- Including X11 support

2024-09-06 00:21:42,633 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create

2024-09-06 00:21:42,763 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create - found

2024-09-06 00:21:42,828 - execution_module.docker_manager -raylib - INFO - -- Found PkgConfig: /usr/bin/pkg-config (found version "0.29.1") 

2024-09-06 00:21:42,912 - execution_module.docker_manager -raylib - INFO - -- Checking for modules 'wayland-client>=0.2.7;wayland-cursor>=0.2.7;wayland-egl>=0.2.7;xkbcommon>=0.5.0'

2024-09-06 00:21:42,912 - execution_module.docker_manager -raylib - INFO - --   Found wayland-client, version 1.18.0

2024-09-06 00:21:42,912 - execution_module.docker_manager -raylib - INFO - --   Found wayland-cursor, version 1.18.0

2024-09-06 00:21:42,912 - execution_module.docker_manager -raylib - INFO - --   Found wayland-egl, version 18.1.0

2024-09-06 00:21:42,912 - execution_module.docker_manager -raylib - INFO - --   Found xkbcommon, version 0.10.0

2024-09-06 00:21:42,999 - execution_module.docker_manager -raylib - INFO - -- Found X11: /usr/include   

2024-09-06 00:21:43,080 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so

2024-09-06 00:21:43,468 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so - found

2024-09-06 00:21:43,468 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname

2024-09-06 00:21:43,566 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname - found

2024-09-06 00:21:43,566 - execution_module.docker_manager -raylib - INFO - -- Looking for connect

2024-09-06 00:21:43,710 - execution_module.docker_manager -raylib - INFO - -- Looking for connect - found

2024-09-06 00:21:43,711 - execution_module.docker_manager -raylib - INFO - -- Looking for remove

2024-09-06 00:21:43,816 - execution_module.docker_manager -raylib - INFO - -- Looking for remove - found

2024-09-06 00:21:43,816 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat

2024-09-06 00:21:44,020 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat - found

2024-09-06 00:21:44,021 - execution_module.docker_manager -raylib - INFO - [91mCMake Error at src/external/glfw/src/CMakeLists.txt:210 (message):
  XInput headers not found; install libxi development package


[0m
2024-09-06 00:21:44,022 - execution_module.docker_manager -raylib - INFO - -- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".

2024-09-06 00:21:44,879 - cxxcrafter -raylib - INFO - Execution Module Finishes
2024-09-06 00:21:44,879 - cxxcrafter -raylib - ERROR - Execution failed with error: -- Looking for shmat
-- Looking for shmat - found
[91mCMake Error at src/external/glfw/src/CMakeLists.txt:210 (message):
  XInput headers not found; install libxi development package


[0m-- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".
The command '/bin/sh -c cmake -B build' returned a non-zero code: 1
2024-09-06 00:21:44,880 - cxxcrafter -raylib - INFO - Modifier Module Starts
2024-09-06 00:21:49,786 - cxxcrafter -raylib - INFO - Modifier Module Finishes
2024-09-06 00:21:49,901 - cxxcrafter -raylib - INFO - Execution Module Starts
2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO - Step 1/16 : FROM ubuntu:20.04
2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO -  ---> 5f5250218d28

2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO - Step 2/16 : ENV DEBIAN_FRONTEND=noninteractive
2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO -  ---> defc00a448c4

2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO - Step 3/16 : RUN sed -i 's|http://archive.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO -  ---> 7134b497e1a1

2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO - Step 4/16 : RUN sed -i 's|http://security.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO -  ---> ea57a31cfcfc

2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO - Step 5/16 : RUN apt-get update
2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:21:51,257 - execution_module.docker_manager -raylib - INFO -  ---> 4041e8821132

2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO - Step 6/16 : RUN apt-get upgrade -y
2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO -  ---> afe371a706bb

2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO - Step 7/16 : RUN apt-get install -y build-essential
2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO -  ---> 29b06df4a18c

2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO - Step 8/16 : RUN apt-get install -y software-properties-common
2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO -  ---> 760c93dbc806

2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO - Step 9/16 : RUN apt-get install -y cmake
2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO -  ---> 0e6b9331b4ee

2024-09-06 00:21:51,258 - execution_module.docker_manager -raylib - INFO - Step 10/16 : RUN apt-get install -y libwayland-dev wayland-protocols pkg-config libx11-dev libxkbcommon-dev libxrandr-dev libxinerama-dev libxcursor-dev libxi-dev
2024-09-06 00:22:59,372 - execution_module.docker_manager -raylib - INFO -  ---> Running in 5dab746b71f2

2024-09-06 00:22:59,853 - execution_module.docker_manager -raylib - INFO - Reading package lists...
2024-09-06 00:23:00,717 - execution_module.docker_manager -raylib - INFO - Building dependency tree...
2024-09-06 00:23:00,836 - execution_module.docker_manager -raylib - INFO - 
Reading state information...
2024-09-06 00:23:00,951 - execution_module.docker_manager -raylib - INFO - The following additional packages will be installed:

2024-09-06 00:23:00,951 - execution_module.docker_manager -raylib - INFO -   libbsd0 libpthread-stubs0-dev libwayland-bin libwayland-client0
  libwayland-cursor0 libwayland-egl1 libwayland-server0 libx11-6 libx11-data
  libxau-dev libxau6 libxcb1 libxcb1-dev libxcursor1 libxdmcp-dev libxdmcp6
  libxext-dev libxext6 libxfixes-dev libxfixes3 libxi6 libxinerama1

2024-09-06 00:23:00,951 - execution_module.docker_manager -raylib - INFO -   libxkbcommon0 libxrandr2 libxrender-dev libxrender1 x11proto-core-dev
  x11proto-dev x11proto-input-dev x11proto-randr-dev x11proto-xext-dev
  x11proto-xinerama-dev xkb-data xorg-sgml-doctools xtrans-dev

2024-09-06 00:23:00,951 - execution_module.docker_manager -raylib - INFO - Suggested packages:
  libwayland-doc libx11-doc libxcb-doc libxext-doc

2024-09-06 00:23:01,005 - execution_module.docker_manager -raylib - INFO - The following NEW packages will be installed:

2024-09-06 00:23:01,005 - execution_module.docker_manager -raylib - INFO -   libbsd0 libpthread-stubs0-dev libwayland-bin libwayland-client0
  libwayland-cursor0 libwayland-dev libwayland-egl1 libwayland-server0
  libx11-6 libx11-data libx11-dev libxau-dev libxau6 libxcb1 libxcb1-dev
  libxcursor-dev libxcursor1 libxdmcp-dev libxdmcp6 libxext-dev libxext6
  libxfixes-dev libxfixes3 libxi-dev libxi6 libxinerama-dev libxinerama1
  libxkbcommon-dev libxkbcommon0 libxrandr-dev libxrandr2 libxrender-dev

2024-09-06 00:23:01,005 - execution_module.docker_manager -raylib - INFO -   libxrender1 pkg-config wayland-protocols x11proto-core-dev x11proto-dev
  x11proto-input-dev x11proto-randr-dev x11proto-xext-dev

2024-09-06 00:23:01,005 - execution_module.docker_manager -raylib - INFO -   x11proto-xinerama-dev xkb-data xorg-sgml-doctools xtrans-dev

2024-09-06 00:23:01,216 - execution_module.docker_manager -raylib - INFO - 0 upgraded, 44 newly installed, 0 to remove and 0 not upgraded.
Need to get 3508 kB of archives.
After this operation, 17.4 MB of additional disk space will be used.
Get:1 http://mirrors.aliyun.com/ubuntu focal/main amd64 libbsd0 amd64 0.10.0-1 [45.4 kB]

2024-09-06 00:23:01,274 - execution_module.docker_manager -raylib - INFO - Get:2 http://mirrors.aliyun.com/ubuntu focal/main amd64 xkb-data all 2.29-2 [349 kB]

2024-09-06 00:23:01,541 - execution_module.docker_manager -raylib - INFO - Get:3 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau6 amd64 1:1.0.9-0ubuntu1 [7488 B]

2024-09-06 00:23:01,723 - execution_module.docker_manager -raylib - INFO - Get:4 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp6 amd64 1:1.1.3-0ubuntu1 [10.6 kB]

2024-09-06 00:23:01,812 - execution_module.docker_manager -raylib - INFO - Get:5 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1 amd64 1.14-2 [44.7 kB]

2024-09-06 00:23:02,110 - execution_module.docker_manager -raylib - INFO - Get:6 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-data all 2:1.6.9-2ubuntu1.6 [114 kB]

2024-09-06 00:23:02,187 - execution_module.docker_manager -raylib - INFO - Get:7 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-6 amd64 2:1.6.9-2ubuntu1.6 [577 kB]

2024-09-06 00:23:02,663 - execution_module.docker_manager -raylib - INFO - Get:8 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxext6 amd64 2:1.3.4-0ubuntu1 [29.1 kB]

2024-09-06 00:23:02,738 - execution_module.docker_manager -raylib - INFO - Get:9 http://mirrors.aliyun.com/ubuntu focal/main amd64 libpthread-stubs0-dev amd64 0.4-1 [5384 B]

2024-09-06 00:23:02,912 - execution_module.docker_manager -raylib - INFO - Get:10 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-client0 amd64 1.18.0-1ubuntu0.1 [23.9 kB]

2024-09-06 00:23:03,137 - execution_module.docker_manager -raylib - INFO - Get:11 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-cursor0 amd64 1.18.0-1ubuntu0.1 [10.3 kB]

2024-09-06 00:23:03,210 - execution_module.docker_manager -raylib - INFO - Get:12 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-egl1 amd64 1.18.0-1ubuntu0.1 [5596 B]

2024-09-06 00:23:03,373 - execution_module.docker_manager -raylib - INFO - Get:13 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-server0 amd64 1.18.0-1ubuntu0.1 [31.3 kB]

2024-09-06 00:23:03,462 - execution_module.docker_manager -raylib - INFO - Get:14 http://mirrors.aliyun.com/ubuntu focal/main amd64 xorg-sgml-doctools all 1:1.11-1 [12.9 kB]

2024-09-06 00:23:03,546 - execution_module.docker_manager -raylib - INFO - Get:15 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-dev all 2019.2-1ubuntu1 [594 kB]

2024-09-06 00:23:03,808 - execution_module.docker_manager -raylib - INFO - Get:16 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-core-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-06 00:23:03,846 - execution_module.docker_manager -raylib - INFO - Get:17 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau-dev amd64 1:1.0.9-0ubuntu1 [9552 B]

2024-09-06 00:23:03,888 - execution_module.docker_manager -raylib - INFO - Get:18 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp-dev amd64 1:1.1.3-0ubuntu1 [25.3 kB]

2024-09-06 00:23:03,936 - execution_module.docker_manager -raylib - INFO - Get:19 http://mirrors.aliyun.com/ubuntu focal/main amd64 xtrans-dev all 1.4.0-1 [68.9 kB]

2024-09-06 00:23:04,167 - execution_module.docker_manager -raylib - INFO - Get:20 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1-dev amd64 1.14-2 [80.5 kB]

2024-09-06 00:23:04,317 - execution_module.docker_manager -raylib - INFO - Get:21 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-dev amd64 2:1.6.9-2ubuntu1.6 [648 kB]

2024-09-06 00:23:04,618 - execution_module.docker_manager -raylib - INFO - Get:22 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxfixes3 amd64 1:5.0.3-2 [10.9 kB]

2024-09-06 00:23:04,784 - execution_module.docker_manager -raylib - INFO - Get:23 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrender1 amd64 1:0.9.10-1 [18.7 kB]

2024-09-06 00:23:04,832 - execution_module.docker_manager -raylib - INFO - Get:24 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcursor1 amd64 1:1.2.0-2 [20.1 kB]

2024-09-06 00:23:04,879 - execution_module.docker_manager -raylib - INFO - Get:25 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrender-dev amd64 1:0.9.10-1 [24.9 kB]

2024-09-06 00:23:04,956 - execution_module.docker_manager -raylib - INFO - Get:26 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxfixes-dev amd64 1:5.0.3-2 [11.4 kB]

2024-09-06 00:23:05,000 - execution_module.docker_manager -raylib - INFO - Get:27 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcursor-dev amd64 1:1.2.0-2 [26.5 kB]

2024-09-06 00:23:05,044 - execution_module.docker_manager -raylib - INFO - Get:28 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-xext-dev all 2019.2-1ubuntu1 [2616 B]

2024-09-06 00:23:05,262 - execution_module.docker_manager -raylib - INFO - Get:29 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxext-dev amd64 2:1.3.4-0ubuntu1 [82.2 kB]

2024-09-06 00:23:05,302 - execution_module.docker_manager -raylib - INFO - Get:30 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxi6 amd64 2:1.7.10-0ubuntu1 [29.9 kB]

2024-09-06 00:23:05,449 - execution_module.docker_manager -raylib - INFO - Get:31 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-input-dev all 2019.2-1ubuntu1 [2628 B]

2024-09-06 00:23:05,592 - execution_module.docker_manager -raylib - INFO - Get:32 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxi-dev amd64 2:1.7.10-0ubuntu1 [187 kB]

2024-09-06 00:23:05,656 - execution_module.docker_manager -raylib - INFO - Get:33 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxinerama1 amd64 2:1.1.4-2 [6904 B]

2024-09-06 00:23:05,701 - execution_module.docker_manager -raylib - INFO - Get:34 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-xinerama-dev all 2019.2-1ubuntu1 [2628 B]

2024-09-06 00:23:05,743 - execution_module.docker_manager -raylib - INFO - Get:35 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxinerama-dev amd64 2:1.1.4-2 [7896 B]

2024-09-06 00:23:05,790 - execution_module.docker_manager -raylib - INFO - Get:36 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon0 amd64 0.10.0-1 [98.4 kB]

2024-09-06 00:23:05,898 - execution_module.docker_manager -raylib - INFO - Get:37 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon-dev amd64 0.10.0-1 [45.4 kB]

2024-09-06 00:23:05,953 - execution_module.docker_manager -raylib - INFO - Get:38 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrandr2 amd64 2:1.5.2-0ubuntu1 [18.5 kB]

2024-09-06 00:23:05,996 - execution_module.docker_manager -raylib - INFO - Get:39 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-randr-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-06 00:23:06,040 - execution_module.docker_manager -raylib - INFO - Get:40 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrandr-dev amd64 2:1.5.2-0ubuntu1 [25.0 kB]

2024-09-06 00:23:06,088 - execution_module.docker_manager -raylib - INFO - Get:41 http://mirrors.aliyun.com/ubuntu focal/main amd64 pkg-config amd64 0.29.1-0ubuntu4 [45.5 kB]

2024-09-06 00:23:06,146 - execution_module.docker_manager -raylib - INFO - Get:42 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-bin amd64 1.18.0-1ubuntu0.1 [20.2 kB]

2024-09-06 00:23:06,191 - execution_module.docker_manager -raylib - INFO - Get:43 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-dev amd64 1.18.0-1ubuntu0.1 [64.6 kB]

2024-09-06 00:23:06,340 - execution_module.docker_manager -raylib - INFO - Get:44 http://mirrors.aliyun.com/ubuntu focal/main amd64 wayland-protocols all 1.20-1 [60.3 kB]

2024-09-06 00:23:06,680 - execution_module.docker_manager -raylib - INFO - [91mdebconf: delaying package configuration, since apt-utils is not installed
[0m
2024-09-06 00:23:06,833 - execution_module.docker_manager -raylib - INFO - Fetched 3508 kB in 5s (656 kB/s)

2024-09-06 00:23:07,015 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libbsd0:amd64.
(Reading database ... 
2024-09-06 00:23:07,019 - execution_module.docker_manager -raylib - INFO - (Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
2024-09-06 00:23:07,020 - execution_module.docker_manager -raylib - INFO - (Reading database ... 65%
2024-09-06 00:23:07,021 - execution_module.docker_manager -raylib - INFO - (Reading database ... 70%
2024-09-06 00:23:07,022 - execution_module.docker_manager -raylib - INFO - (Reading database ... 75%
2024-09-06 00:23:07,024 - execution_module.docker_manager -raylib - INFO - (Reading database ... 80%
2024-09-06 00:23:07,025 - execution_module.docker_manager -raylib - INFO - (Reading database ... 85%
2024-09-06 00:23:07,025 - execution_module.docker_manager -raylib - INFO - (Reading database ... 90%
2024-09-06 00:23:07,027 - execution_module.docker_manager -raylib - INFO - (Reading database ... 95%
2024-09-06 00:23:07,028 - execution_module.docker_manager -raylib - INFO - (Reading database ... 100%
(Reading database ... 21508 files and directories currently installed.)

2024-09-06 00:23:07,028 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../00-libbsd0_0.10.0-1_amd64.deb ...

2024-09-06 00:23:07,149 - execution_module.docker_manager -raylib - INFO - Unpacking libbsd0:amd64 (0.10.0-1) ...

2024-09-06 00:23:07,365 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xkb-data.

2024-09-06 00:23:07,368 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../01-xkb-data_2.29-2_all.deb ...

2024-09-06 00:23:07,408 - execution_module.docker_manager -raylib - INFO - Unpacking xkb-data (2.29-2) ...

2024-09-06 00:23:08,493 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau6:amd64.

2024-09-06 00:23:08,495 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../02-libxau6_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-06 00:23:08,614 - execution_module.docker_manager -raylib - INFO - Unpacking libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:23:11,955 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp6:amd64.

2024-09-06 00:23:11,958 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../03-libxdmcp6_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-06 00:23:12,235 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:23:14,813 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1:amd64.

2024-09-06 00:23:14,815 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../04-libxcb1_1.14-2_amd64.deb ...

2024-09-06 00:23:14,909 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1:amd64 (1.14-2) ...

2024-09-06 00:23:15,317 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-data.

2024-09-06 00:23:15,318 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../05-libx11-data_2%3a1.6.9-2ubuntu1.6_all.deb ...

2024-09-06 00:23:15,378 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:23:15,787 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-6:amd64.

2024-09-06 00:23:15,788 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../06-libx11-6_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-06 00:23:15,830 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:23:16,194 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxext6:amd64.

2024-09-06 00:23:16,196 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../07-libxext6_2%3a1.3.4-0ubuntu1_amd64.deb ...

2024-09-06 00:23:16,247 - execution_module.docker_manager -raylib - INFO - Unpacking libxext6:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:23:16,485 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libpthread-stubs0-dev:amd64.

2024-09-06 00:23:16,486 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../08-libpthread-stubs0-dev_0.4-1_amd64.deb ...

2024-09-06 00:23:16,527 - execution_module.docker_manager -raylib - INFO - Unpacking libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-06 00:23:18,741 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-client0:amd64.

2024-09-06 00:23:18,742 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../09-libwayland-client0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:23:19,179 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:23:22,283 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-cursor0:amd64.

2024-09-06 00:23:22,289 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../10-libwayland-cursor0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:23:22,575 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:23:25,437 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-egl1:amd64.

2024-09-06 00:23:25,440 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../11-libwayland-egl1_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:23:26,224 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:23:29,798 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-server0:amd64.

2024-09-06 00:23:29,806 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../12-libwayland-server0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:23:30,282 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:23:34,034 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xorg-sgml-doctools.

2024-09-06 00:23:34,053 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../13-xorg-sgml-doctools_1%3a1.11-1_all.deb ...

2024-09-06 00:23:34,953 - execution_module.docker_manager -raylib - INFO - Unpacking xorg-sgml-doctools (1:1.11-1) ...

2024-09-06 00:23:50,488 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-dev.

2024-09-06 00:23:50,668 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../14-x11proto-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:23:51,837 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-dev (2019.2-1ubuntu1) ...

2024-09-06 00:23:53,428 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-core-dev.

2024-09-06 00:23:53,442 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../15-x11proto-core-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:23:53,656 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-06 00:23:54,732 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau-dev:amd64.

2024-09-06 00:23:54,735 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../16-libxau-dev_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-06 00:23:55,142 - execution_module.docker_manager -raylib - INFO - Unpacking libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:23:56,598 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp-dev:amd64.

2024-09-06 00:23:56,600 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../17-libxdmcp-dev_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-06 00:23:56,716 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:23:58,209 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xtrans-dev.

2024-09-06 00:23:58,212 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../18-xtrans-dev_1.4.0-1_all.deb ...

2024-09-06 00:23:58,414 - execution_module.docker_manager -raylib - INFO - Unpacking xtrans-dev (1.4.0-1) ...

2024-09-06 00:23:59,869 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1-dev:amd64.

2024-09-06 00:23:59,871 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../19-libxcb1-dev_1.14-2_amd64.deb ...

2024-09-06 00:24:00,193 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1-dev:amd64 (1.14-2) ...

2024-09-06 00:24:01,255 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-dev:amd64.

2024-09-06 00:24:01,256 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../20-libx11-dev_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-06 00:24:01,770 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:24:06,318 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxfixes3:amd64.

2024-09-06 00:24:06,321 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../21-libxfixes3_1%3a5.0.3-2_amd64.deb ...

2024-09-06 00:24:06,844 - execution_module.docker_manager -raylib - INFO - Unpacking libxfixes3:amd64 (1:5.0.3-2) ...

2024-09-06 00:24:10,485 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrender1:amd64.

2024-09-06 00:24:10,488 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../22-libxrender1_1%3a0.9.10-1_amd64.deb ...

2024-09-06 00:24:11,973 - execution_module.docker_manager -raylib - INFO - Unpacking libxrender1:amd64 (1:0.9.10-1) ...

2024-09-06 00:24:13,788 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcursor1:amd64.

2024-09-06 00:24:13,790 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../23-libxcursor1_1%3a1.2.0-2_amd64.deb ...

2024-09-06 00:24:14,113 - execution_module.docker_manager -raylib - INFO - Unpacking libxcursor1:amd64 (1:1.2.0-2) ...

2024-09-06 00:24:15,024 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrender-dev:amd64.

2024-09-06 00:24:15,027 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../24-libxrender-dev_1%3a0.9.10-1_amd64.deb ...

2024-09-06 00:24:15,110 - execution_module.docker_manager -raylib - INFO - Unpacking libxrender-dev:amd64 (1:0.9.10-1) ...

2024-09-06 00:24:16,790 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxfixes-dev:amd64.

2024-09-06 00:24:16,791 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../25-libxfixes-dev_1%3a5.0.3-2_amd64.deb ...

2024-09-06 00:24:17,418 - execution_module.docker_manager -raylib - INFO - Unpacking libxfixes-dev:amd64 (1:5.0.3-2) ...

2024-09-06 00:24:19,775 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcursor-dev:amd64.

2024-09-06 00:24:19,777 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../26-libxcursor-dev_1%3a1.2.0-2_amd64.deb ...

2024-09-06 00:24:20,453 - execution_module.docker_manager -raylib - INFO - Unpacking libxcursor-dev:amd64 (1:1.2.0-2) ...

2024-09-06 00:24:22,946 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-xext-dev.

2024-09-06 00:24:22,966 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../27-x11proto-xext-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:24:23,393 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-xext-dev (2019.2-1ubuntu1) ...

2024-09-06 00:24:25,568 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxext-dev:amd64.

2024-09-06 00:24:25,585 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../28-libxext-dev_2%3a1.3.4-0ubuntu1_amd64.deb ...

2024-09-06 00:24:25,955 - execution_module.docker_manager -raylib - INFO - Unpacking libxext-dev:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:24:30,895 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxi6:amd64.

2024-09-06 00:24:31,008 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../29-libxi6_2%3a1.7.10-0ubuntu1_amd64.deb ...

2024-09-06 00:24:33,302 - execution_module.docker_manager -raylib - INFO - Unpacking libxi6:amd64 (2:1.7.10-0ubuntu1) ...

2024-09-06 00:24:36,055 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-input-dev.

2024-09-06 00:24:36,057 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../30-x11proto-input-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:24:36,655 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-input-dev (2019.2-1ubuntu1) ...

2024-09-06 00:24:37,818 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxi-dev:amd64.

2024-09-06 00:24:37,822 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../31-libxi-dev_2%3a1.7.10-0ubuntu1_amd64.deb ...

2024-09-06 00:24:37,939 - execution_module.docker_manager -raylib - INFO - Unpacking libxi-dev:amd64 (2:1.7.10-0ubuntu1) ...

2024-09-06 00:24:40,476 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxinerama1:amd64.

2024-09-06 00:24:40,479 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../32-libxinerama1_2%3a1.1.4-2_amd64.deb ...

2024-09-06 00:24:41,367 - execution_module.docker_manager -raylib - INFO - Unpacking libxinerama1:amd64 (2:1.1.4-2) ...

2024-09-06 00:24:44,428 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-xinerama-dev.

2024-09-06 00:24:44,459 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../33-x11proto-xinerama-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:24:44,654 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-xinerama-dev (2019.2-1ubuntu1) ...

2024-09-06 00:24:46,917 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxinerama-dev:amd64.

2024-09-06 00:24:46,920 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../34-libxinerama-dev_2%3a1.1.4-2_amd64.deb ...

2024-09-06 00:24:47,155 - execution_module.docker_manager -raylib - INFO - Unpacking libxinerama-dev:amd64 (2:1.1.4-2) ...

2024-09-06 00:24:49,317 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon0:amd64.

2024-09-06 00:24:49,320 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../35-libxkbcommon0_0.10.0-1_amd64.deb ...

2024-09-06 00:24:49,383 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-06 00:24:50,410 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon-dev:amd64.

2024-09-06 00:24:50,413 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../36-libxkbcommon-dev_0.10.0-1_amd64.deb ...

2024-09-06 00:24:50,670 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-06 00:24:51,014 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrandr2:amd64.

2024-09-06 00:24:51,015 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../37-libxrandr2_2%3a1.5.2-0ubuntu1_amd64.deb ...

2024-09-06 00:24:51,063 - execution_module.docker_manager -raylib - INFO - Unpacking libxrandr2:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:24:52,166 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-randr-dev.

2024-09-06 00:24:52,169 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../38-x11proto-randr-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:24:52,751 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-randr-dev (2019.2-1ubuntu1) ...

2024-09-06 00:24:54,599 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrandr-dev:amd64.

2024-09-06 00:24:54,622 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../39-libxrandr-dev_2%3a1.5.2-0ubuntu1_amd64.deb ...

2024-09-06 00:24:54,786 - execution_module.docker_manager -raylib - INFO - Unpacking libxrandr-dev:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:24:56,348 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package pkg-config.

2024-09-06 00:24:56,351 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../40-pkg-config_0.29.1-0ubuntu4_amd64.deb ...

2024-09-06 00:24:56,953 - execution_module.docker_manager -raylib - INFO - Unpacking pkg-config (0.29.1-0ubuntu4) ...

2024-09-06 00:24:59,298 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-bin.

2024-09-06 00:24:59,301 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../41-libwayland-bin_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:24:59,506 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-06 00:25:01,971 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-dev:amd64.

2024-09-06 00:25:01,981 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../42-libwayland-dev_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:25:02,943 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:25:06,610 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package wayland-protocols.

2024-09-06 00:25:06,611 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../43-wayland-protocols_1.20-1_all.deb ...

2024-09-06 00:25:08,162 - execution_module.docker_manager -raylib - INFO - Unpacking wayland-protocols (1.20-1) ...

2024-09-06 00:25:17,976 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:25:21,073 - execution_module.docker_manager -raylib - INFO - Setting up libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:25:23,364 - execution_module.docker_manager -raylib - INFO - Setting up xkb-data (2.29-2) ...

2024-09-06 00:25:25,485 - execution_module.docker_manager -raylib - INFO - Setting up libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-06 00:25:26,908 - execution_module.docker_manager -raylib - INFO - Setting up xtrans-dev (1.4.0-1) ...

2024-09-06 00:25:27,567 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-06 00:25:28,149 - execution_module.docker_manager -raylib - INFO - Setting up libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:25:28,770 - execution_module.docker_manager -raylib - INFO - Setting up pkg-config (0.29.1-0ubuntu4) ...

2024-09-06 00:25:30,154 - execution_module.docker_manager -raylib - INFO - Setting up wayland-protocols (1.20-1) ...

2024-09-06 00:25:30,887 - execution_module.docker_manager -raylib - INFO - Setting up xorg-sgml-doctools (1:1.11-1) ...

2024-09-06 00:25:31,903 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:25:32,682 - execution_module.docker_manager -raylib - INFO - Setting up libbsd0:amd64 (0.10.0-1) ...

2024-09-06 00:25:33,420 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-06 00:25:34,126 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:25:34,727 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-dev (2019.2-1ubuntu1) ...

2024-09-06 00:25:35,234 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:25:38,649 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1:amd64 (1.14-2) ...

2024-09-06 00:25:45,135 - execution_module.docker_manager -raylib - INFO - Setting up libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:25:58,032 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-randr-dev (2019.2-1ubuntu1) ...

2024-09-06 00:25:59,417 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-06 00:25:59,794 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-xinerama-dev (2019.2-1ubuntu1) ...

2024-09-06 00:26:00,373 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:26:01,065 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-06 00:26:01,545 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-input-dev (2019.2-1ubuntu1) ...

2024-09-06 00:26:01,953 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-xext-dev (2019.2-1ubuntu1) ...

2024-09-06 00:26:02,418 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:26:02,688 - execution_module.docker_manager -raylib - INFO - Setting up libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:26:02,808 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1-dev:amd64 (1.14-2) ...

2024-09-06 00:26:03,443 - execution_module.docker_manager -raylib - INFO - Setting up libxrender1:amd64 (1:0.9.10-1) ...

2024-09-06 00:26:03,696 - execution_module.docker_manager -raylib - INFO - Setting up libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:26:04,088 - execution_module.docker_manager -raylib - INFO - Setting up libxext6:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:26:04,232 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:26:04,404 - execution_module.docker_manager -raylib - INFO - Setting up libxfixes3:amd64 (1:5.0.3-2) ...

2024-09-06 00:26:04,905 - execution_module.docker_manager -raylib - INFO - Setting up libxinerama1:amd64 (2:1.1.4-2) ...

2024-09-06 00:26:05,264 - execution_module.docker_manager -raylib - INFO - Setting up libxrandr2:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:26:05,760 - execution_module.docker_manager -raylib - INFO - Setting up libxext-dev:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:26:05,915 - execution_module.docker_manager -raylib - INFO - Setting up libxrender-dev:amd64 (1:0.9.10-1) ...

2024-09-06 00:26:06,595 - execution_module.docker_manager -raylib - INFO - Setting up libxi6:amd64 (2:1.7.10-0ubuntu1) ...

2024-09-06 00:26:07,064 - execution_module.docker_manager -raylib - INFO - Setting up libxcursor1:amd64 (1:1.2.0-2) ...

2024-09-06 00:26:07,532 - execution_module.docker_manager -raylib - INFO - Setting up libxfixes-dev:amd64 (1:5.0.3-2) ...

2024-09-06 00:26:08,251 - execution_module.docker_manager -raylib - INFO - Setting up libxrandr-dev:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:26:08,771 - execution_module.docker_manager -raylib - INFO - Setting up libxinerama-dev:amd64 (2:1.1.4-2) ...

2024-09-06 00:26:09,350 - execution_module.docker_manager -raylib - INFO - Setting up libxi-dev:amd64 (2:1.7.10-0ubuntu1) ...

2024-09-06 00:26:09,680 - execution_module.docker_manager -raylib - INFO - Setting up libxcursor-dev:amd64 (1:1.2.0-2) ...

2024-09-06 00:26:09,813 - execution_module.docker_manager -raylib - INFO - Processing triggers for libc-bin (2.31-0ubuntu9.16) ...

2024-09-06 00:26:14,290 - execution_module.docker_manager -raylib - INFO -  ---> bb5b964275e4

2024-09-06 00:26:14,290 - execution_module.docker_manager -raylib - INFO - Step 11/16 : RUN mkdir /tmp/raylib
2024-09-06 00:26:15,370 - execution_module.docker_manager -raylib - INFO -  ---> Running in 989e222e8b9a

2024-09-06 00:27:10,110 - execution_module.docker_manager -raylib - INFO -  ---> 3a9ccd7167a7

2024-09-06 00:27:10,110 - execution_module.docker_manager -raylib - INFO - Step 12/16 : COPY ./raylib /tmp/raylib
2024-09-06 00:27:51,191 - execution_module.docker_manager -raylib - INFO -  ---> 1eca69d2ddc4

2024-09-06 00:27:51,191 - execution_module.docker_manager -raylib - INFO - Step 13/16 : WORKDIR /tmp/raylib
2024-09-06 00:28:16,037 - execution_module.docker_manager -raylib - INFO -  ---> Running in 48f528256109

2024-09-06 00:28:24,676 - execution_module.docker_manager -raylib - INFO -  ---> 169d7ffeb4f6

2024-09-06 00:28:24,676 - execution_module.docker_manager -raylib - INFO - Step 14/16 : RUN cmake -B build
2024-09-06 00:28:25,595 - execution_module.docker_manager -raylib - INFO -  ---> Running in dbf0efce4a8d

2024-09-06 00:28:26,639 - execution_module.docker_manager -raylib - INFO - -- The C compiler identification is GNU 9.4.0

2024-09-06 00:28:26,735 - execution_module.docker_manager -raylib - INFO - -- The CXX compiler identification is GNU 9.4.0

2024-09-06 00:28:26,739 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc

2024-09-06 00:28:26,845 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc -- works

2024-09-06 00:28:26,847 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info

2024-09-06 00:28:26,947 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info - done

2024-09-06 00:28:26,955 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features

2024-09-06 00:28:26,955 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features - done

2024-09-06 00:28:26,958 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++

2024-09-06 00:28:27,066 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++ -- works

2024-09-06 00:28:27,068 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info

2024-09-06 00:28:27,168 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info - done

2024-09-06 00:28:27,176 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features

2024-09-06 00:28:27,176 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features - done

2024-09-06 00:28:27,178 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES

2024-09-06 00:28:27,233 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES - Success

2024-09-06 00:28:27,233 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=pointer-arith can be used -- compiles

2024-09-06 00:28:27,233 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=implicit-function-declaration can be used -- compiles

2024-09-06 00:28:27,233 - execution_module.docker_manager -raylib - INFO - -- Testing if -fno-strict-aliasing can be used -- compiles

2024-09-06 00:28:27,239 - execution_module.docker_manager -raylib - INFO - -- Setting build type to 'Debug' as none was specified.

2024-09-06 00:28:27,239 - execution_module.docker_manager -raylib - INFO - -- Using raylib's GLFW

2024-09-06 00:28:27,252 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h

2024-09-06 00:28:27,395 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h - found

2024-09-06 00:28:27,395 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD

2024-09-06 00:28:27,587 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed

2024-09-06 00:28:27,587 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads

2024-09-06 00:28:27,713 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads - not found

2024-09-06 00:28:27,713 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread

2024-09-06 00:28:27,937 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread - found

2024-09-06 00:28:27,938 - execution_module.docker_manager -raylib - INFO - -- Found Threads: TRUE  

2024-09-06 00:28:27,938 - execution_module.docker_manager -raylib - INFO - -- Including Wayland support
-- Including X11 support

2024-09-06 00:28:27,940 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create

2024-09-06 00:28:28,241 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create - found

2024-09-06 00:28:28,263 - execution_module.docker_manager -raylib - INFO - -- Found PkgConfig: /usr/bin/pkg-config (found version "0.29.1") 

2024-09-06 00:28:28,263 - execution_module.docker_manager -raylib - INFO - -- Checking for modules 'wayland-client>=0.2.7;wayland-cursor>=0.2.7;wayland-egl>=0.2.7;xkbcommon>=0.5.0'

2024-09-06 00:28:28,276 - execution_module.docker_manager -raylib - INFO - --   Found wayland-client, version 1.18.0

2024-09-06 00:28:28,283 - execution_module.docker_manager -raylib - INFO - --   Found wayland-cursor, version 1.18.0

2024-09-06 00:28:28,290 - execution_module.docker_manager -raylib - INFO - --   Found wayland-egl, version 18.1.0

2024-09-06 00:28:28,300 - execution_module.docker_manager -raylib - INFO - --   Found xkbcommon, version 0.10.0

2024-09-06 00:28:28,453 - execution_module.docker_manager -raylib - INFO - -- Found X11: /usr/include   

2024-09-06 00:28:28,454 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so

2024-09-06 00:28:28,780 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so - found

2024-09-06 00:28:28,780 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname

2024-09-06 00:28:28,972 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname - found

2024-09-06 00:28:28,972 - execution_module.docker_manager -raylib - INFO - -- Looking for connect

2024-09-06 00:28:29,148 - execution_module.docker_manager -raylib - INFO - -- Looking for connect - found

2024-09-06 00:28:29,148 - execution_module.docker_manager -raylib - INFO - -- Looking for remove

2024-09-06 00:28:29,318 - execution_module.docker_manager -raylib - INFO - -- Looking for remove - found

2024-09-06 00:28:29,318 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat

2024-09-06 00:28:29,422 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat - found

2024-09-06 00:28:29,455 - execution_module.docker_manager -raylib - INFO - -- Audio Backend: miniaudio

2024-09-06 00:28:29,462 - execution_module.docker_manager -raylib - INFO - -- Building raylib static library

2024-09-06 00:28:29,475 - execution_module.docker_manager -raylib - INFO - -- Generated build type: Debug
-- Compiling with the flags:
--   PLATFORM=PLATFORM_DESKTOP
--   GRAPHICS=GRAPHICS_API_OPENGL_33

2024-09-06 00:28:29,790 - execution_module.docker_manager -raylib - INFO - -- Building examples is enabled

2024-09-06 00:28:29,931 - execution_module.docker_manager -raylib - INFO - -- Looking for CLOCK_MONOTONIC

2024-09-06 00:28:30,003 - execution_module.docker_manager -raylib - INFO - -- Looking for CLOCK_MONOTONIC - found

2024-09-06 00:28:30,004 - execution_module.docker_manager -raylib - INFO - -- Looking for QueryPerformanceCounter

2024-09-06 00:28:30,065 - execution_module.docker_manager -raylib - INFO - -- Looking for QueryPerformanceCounter - not found

2024-09-06 00:28:30,065 - execution_module.docker_manager -raylib - INFO - -- Looking for stdatomic.h

2024-09-06 00:28:30,171 - execution_module.docker_manager -raylib - INFO - -- Looking for stdatomic.h - found

2024-09-06 00:28:30,173 - execution_module.docker_manager -raylib - INFO - -- Testing if -std=c11 can be used -- compiles

2024-09-06 00:28:30,233 - execution_module.docker_manager -raylib - INFO - [91mCMake Error: The following variables are used in this project, but they are set to NOTFOUND.
Please set them or make sure they are set and tested correctly in the CMake files:
/tmp/raylib/src/OPENGL_INCLUDE_DIR
   used as include directory in directory /tmp/raylib/src

[0m
2024-09-06 00:28:30,233 - execution_module.docker_manager -raylib - INFO - -- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".

2024-09-06 00:30:52,905 - cxxcrafter -raylib - INFO - Execution Module Finishes
2024-09-06 00:30:52,905 - cxxcrafter -raylib - ERROR - Execution failed with error: -- Looking for stdatomic.h - found
-- Testing if -std=c11 can be used -- compiles
[91mCMake Error: The following variables are used in this project, but they are set to NOTFOUND.
Please set them or make sure they are set and tested correctly in the CMake files:
/tmp/raylib/src/OPENGL_INCLUDE_DIR
   used as include directory in directory /tmp/raylib/src

[0m-- Configuring incomplete, errors occurred!
See also "/tmp/raylib/build/CMakeFiles/CMakeOutput.log".
See also "/tmp/raylib/build/CMakeFiles/CMakeError.log".
The command '/bin/sh -c cmake -B build' returned a non-zero code: 1
2024-09-06 00:30:52,905 - cxxcrafter -raylib - INFO - Modifier Module Starts
2024-09-06 00:30:57,831 - cxxcrafter -raylib - INFO - Modifier Module Finishes
2024-09-06 00:30:57,831 - cxxcrafter -raylib - INFO - Execution Module Starts
2024-09-06 00:31:22,418 - execution_module.docker_manager -raylib - INFO - Step 1/16 : FROM ubuntu:20.04
2024-09-06 00:31:22,419 - execution_module.docker_manager -raylib - INFO -  ---> 5f5250218d28

2024-09-06 00:31:22,419 - execution_module.docker_manager -raylib - INFO - Step 2/16 : ENV DEBIAN_FRONTEND=noninteractive
2024-09-06 00:31:22,419 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:31:22,419 - execution_module.docker_manager -raylib - INFO -  ---> defc00a448c4

2024-09-06 00:31:22,420 - execution_module.docker_manager -raylib - INFO - Step 3/16 : RUN sed -i 's|http://archive.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-06 00:31:22,420 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:31:22,420 - execution_module.docker_manager -raylib - INFO -  ---> 7134b497e1a1

2024-09-06 00:31:22,420 - execution_module.docker_manager -raylib - INFO - Step 4/16 : RUN sed -i 's|http://security.ubuntu.com/ubuntu/|http://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list
2024-09-06 00:31:22,420 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:31:22,420 - execution_module.docker_manager -raylib - INFO -  ---> ea57a31cfcfc

2024-09-06 00:31:22,420 - execution_module.docker_manager -raylib - INFO - Step 5/16 : RUN apt-get update
2024-09-06 00:31:22,420 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:31:22,421 - execution_module.docker_manager -raylib - INFO -  ---> 4041e8821132

2024-09-06 00:31:22,421 - execution_module.docker_manager -raylib - INFO - Step 6/16 : RUN apt-get upgrade -y
2024-09-06 00:31:22,421 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:31:22,421 - execution_module.docker_manager -raylib - INFO -  ---> afe371a706bb

2024-09-06 00:31:22,421 - execution_module.docker_manager -raylib - INFO - Step 7/16 : RUN apt-get install -y build-essential
2024-09-06 00:31:22,421 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:31:22,421 - execution_module.docker_manager -raylib - INFO -  ---> 29b06df4a18c

2024-09-06 00:31:22,421 - execution_module.docker_manager -raylib - INFO - Step 8/16 : RUN apt-get install -y software-properties-common
2024-09-06 00:31:22,422 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:31:22,422 - execution_module.docker_manager -raylib - INFO -  ---> 760c93dbc806

2024-09-06 00:31:22,422 - execution_module.docker_manager -raylib - INFO - Step 9/16 : RUN apt-get install -y cmake
2024-09-06 00:31:22,422 - execution_module.docker_manager -raylib - INFO -  ---> Using cache

2024-09-06 00:31:22,422 - execution_module.docker_manager -raylib - INFO -  ---> 0e6b9331b4ee

2024-09-06 00:31:22,422 - execution_module.docker_manager -raylib - INFO - Step 10/16 : RUN apt-get install -y libgl1-mesa-dev libglu1-mesa-dev     libwayland-dev wayland-protocols pkg-config libx11-dev libxkbcommon-dev     libxrandr-dev libxinerama-dev libxcursor-dev libxi-dev
2024-09-06 00:32:13,760 - execution_module.docker_manager -raylib - INFO -  ---> Running in 0fe20c7b6fcf

2024-09-06 00:32:16,204 - execution_module.docker_manager -raylib - INFO - Reading package lists...
2024-09-06 00:32:16,938 - execution_module.docker_manager -raylib - INFO - Building dependency tree...
2024-09-06 00:32:17,040 - execution_module.docker_manager -raylib - INFO - 
Reading state information...
2024-09-06 00:32:17,140 - execution_module.docker_manager -raylib - INFO - The following additional packages will be installed:
  libbsd0 libdrm-amdgpu1 libdrm-common libdrm-intel1 libdrm-nouveau2
  libdrm-radeon1 libdrm2 libedit2 libegl-dev libegl-mesa0 libegl1 libgbm1
  libgl-dev libgl1 libgl1-mesa-dri libglapi-mesa libgles-dev libgles1 libgles2
  libglu1-mesa libglvnd-dev libglvnd0 libglx-dev libglx-mesa0 libglx0
  libllvm12 libopengl-dev libopengl0 libpciaccess0 libpthread-stubs0-dev

2024-09-06 00:32:17,140 - execution_module.docker_manager -raylib - INFO -   libsensors-config libsensors5 libvulkan1 libwayland-bin libwayland-client0
  libwayland-cursor0 libwayland-egl1 libwayland-server0 libx11-6 libx11-data
  libx11-xcb1 libxau-dev libxau6 libxcb-dri2-0 libxcb-dri3-0 libxcb-glx0
  libxcb-present0 libxcb-randr0 libxcb-shm0 libxcb-sync1 libxcb-xfixes0
  libxcb1 libxcb1-dev libxcursor1 libxdmcp-dev libxdmcp6 libxext-dev libxext6
  libxfixes-dev libxfixes3 libxi6 libxinerama1 libxkbcommon0 libxrandr2

2024-09-06 00:32:17,140 - execution_module.docker_manager -raylib - INFO -   libxrender-dev libxrender1 libxshmfence1 libxxf86vm1 mesa-vulkan-drivers
  x11proto-core-dev x11proto-dev x11proto-input-dev x11proto-randr-dev
  x11proto-xext-dev x11proto-xinerama-dev xkb-data xorg-sgml-doctools
  xtrans-dev

2024-09-06 00:32:17,140 - execution_module.docker_manager -raylib - INFO - Suggested packages:
  pciutils lm-sensors libwayland-doc libx11-doc libxcb-doc libxext-doc

2024-09-06 00:32:17,227 - execution_module.docker_manager -raylib - INFO - The following NEW packages will be installed:
  libbsd0 libdrm-amdgpu1 libdrm-common libdrm-intel1 libdrm-nouveau2
  libdrm-radeon1 libdrm2 libedit2 libegl-dev libegl-mesa0 libegl1 libgbm1
  libgl-dev libgl1 libgl1-mesa-dev libgl1-mesa-dri libglapi-mesa libgles-dev
  libgles1 libgles2 libglu1-mesa libglu1-mesa-dev libglvnd-dev libglvnd0
  libglx-dev libglx-mesa0 libglx0 libllvm12 libopengl-dev libopengl0

2024-09-06 00:32:17,227 - execution_module.docker_manager -raylib - INFO -   libpciaccess0 libpthread-stubs0-dev libsensors-config libsensors5 libvulkan1
  libwayland-bin libwayland-client0 libwayland-cursor0 libwayland-dev
  libwayland-egl1 libwayland-server0 libx11-6 libx11-data libx11-dev
  libx11-xcb1 libxau-dev libxau6 libxcb-dri2-0 libxcb-dri3-0 libxcb-glx0
  libxcb-present0 libxcb-randr0 libxcb-shm0 libxcb-sync1 libxcb-xfixes0
  libxcb1 libxcb1-dev libxcursor-dev libxcursor1 libxdmcp-dev libxdmcp6
  libxext-dev libxext6 libxfixes-dev libxfixes3 libxi-dev libxi6
  libxinerama-dev libxinerama1 libxkbcommon-dev libxkbcommon0 libxrandr-dev

2024-09-06 00:32:17,227 - execution_module.docker_manager -raylib - INFO -   libxrandr2 libxrender-dev libxrender1 libxshmfence1 libxxf86vm1

2024-09-06 00:32:17,227 - execution_module.docker_manager -raylib - INFO -   mesa-vulkan-drivers pkg-config wayland-protocols x11proto-core-dev
  x11proto-dev x11proto-input-dev x11proto-randr-dev x11proto-xext-dev
  x11proto-xinerama-dev xkb-data xorg-sgml-doctools xtrans-dev

2024-09-06 00:32:17,307 - execution_module.docker_manager -raylib - INFO - 0 upgraded, 89 newly installed, 0 to remove and 0 not upgraded.
Need to get 40.7 MB of archives.
After this operation, 547 MB of additional disk space will be used.
Get:1 http://mirrors.aliyun.com/ubuntu focal/main amd64 libbsd0 amd64 0.10.0-1 [45.4 kB]

2024-09-06 00:32:17,381 - execution_module.docker_manager -raylib - INFO - Get:2 http://mirrors.aliyun.com/ubuntu focal/main amd64 xkb-data all 2.29-2 [349 kB]

2024-09-06 00:32:17,842 - execution_module.docker_manager -raylib - INFO - Get:3 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libdrm-common all 2.4.107-8ubuntu1~20.04.2 [5396 B]

2024-09-06 00:32:17,964 - execution_module.docker_manager -raylib - INFO - Get:4 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libdrm2 amd64 2.4.107-8ubuntu1~20.04.2 [34.1 kB]

2024-09-06 00:32:18,145 - execution_module.docker_manager -raylib - INFO - Get:5 http://mirrors.aliyun.com/ubuntu focal/main amd64 libedit2 amd64 3.1-20191231-1 [87.0 kB]

2024-09-06 00:32:18,318 - execution_module.docker_manager -raylib - INFO - Get:6 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau6 amd64 1:1.0.9-0ubuntu1 [7488 B]

2024-09-06 00:32:18,488 - execution_module.docker_manager -raylib - INFO - Get:7 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp6 amd64 1:1.1.3-0ubuntu1 [10.6 kB]

2024-09-06 00:32:18,665 - execution_module.docker_manager -raylib - INFO - Get:8 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1 amd64 1.14-2 [44.7 kB]

2024-09-06 00:32:18,718 - execution_module.docker_manager -raylib - INFO - Get:9 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-data all 2:1.6.9-2ubuntu1.6 [114 kB]

2024-09-06 00:32:19,053 - execution_module.docker_manager -raylib - INFO - Get:10 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-6 amd64 2:1.6.9-2ubuntu1.6 [577 kB]

2024-09-06 00:32:19,359 - execution_module.docker_manager -raylib - INFO - Get:11 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxext6 amd64 2:1.3.4-0ubuntu1 [29.1 kB]

2024-09-06 00:32:19,424 - execution_module.docker_manager -raylib - INFO - Get:12 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libdrm-amdgpu1 amd64 2.4.107-8ubuntu1~20.04.2 [18.6 kB]

2024-09-06 00:32:19,476 - execution_module.docker_manager -raylib - INFO - Get:13 http://mirrors.aliyun.com/ubuntu focal/main amd64 libpciaccess0 amd64 0.16-0ubuntu1 [17.9 kB]

2024-09-06 00:32:19,523 - execution_module.docker_manager -raylib - INFO - Get:14 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libdrm-intel1 amd64 2.4.107-8ubuntu1~20.04.2 [60.3 kB]

2024-09-06 00:32:19,617 - execution_module.docker_manager -raylib - INFO - Get:15 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libdrm-nouveau2 amd64 2.4.107-8ubuntu1~20.04.2 [16.6 kB]

2024-09-06 00:32:19,725 - execution_module.docker_manager -raylib - INFO - Get:16 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libdrm-radeon1 amd64 2.4.107-8ubuntu1~20.04.2 [19.7 kB]

2024-09-06 00:32:19,911 - execution_module.docker_manager -raylib - INFO - Get:17 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libglvnd0 amd64 1.3.2-1~ubuntu0.20.04.2 [48.1 kB]

2024-09-06 00:32:20,082 - execution_module.docker_manager -raylib - INFO - Get:18 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-server0 amd64 1.18.0-1ubuntu0.1 [31.3 kB]

2024-09-06 00:32:20,235 - execution_module.docker_manager -raylib - INFO - Get:19 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libgbm1 amd64 21.2.6-0ubuntu0.1~20.04.2 [29.2 kB]

2024-09-06 00:32:20,290 - execution_module.docker_manager -raylib - INFO - Get:20 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libglapi-mesa amd64 21.2.6-0ubuntu0.1~20.04.2 [27.4 kB]

2024-09-06 00:32:20,518 - execution_module.docker_manager -raylib - INFO - Get:21 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-client0 amd64 1.18.0-1ubuntu0.1 [23.9 kB]

2024-09-06 00:32:20,583 - execution_module.docker_manager -raylib - INFO - Get:22 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-xcb1 amd64 2:1.6.9-2ubuntu1.6 [9448 B]

2024-09-06 00:32:20,636 - execution_module.docker_manager -raylib - INFO - Get:23 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb-dri2-0 amd64 1.14-2 [6920 B]

2024-09-06 00:32:20,686 - execution_module.docker_manager -raylib - INFO - Get:24 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb-dri3-0 amd64 1.14-2 [6552 B]

2024-09-06 00:32:20,739 - execution_module.docker_manager -raylib - INFO - Get:25 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb-present0 amd64 1.14-2 [5560 B]

2024-09-06 00:32:20,781 - execution_module.docker_manager -raylib - INFO - Get:26 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb-sync1 amd64 1.14-2 [8884 B]

2024-09-06 00:32:20,849 - execution_module.docker_manager -raylib - INFO - Get:27 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb-xfixes0 amd64 1.14-2 [9296 B]

2024-09-06 00:32:21,050 - execution_module.docker_manager -raylib - INFO - Get:28 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxshmfence1 amd64 1.3-1 [5028 B]

2024-09-06 00:32:21,420 - execution_module.docker_manager -raylib - INFO - Get:29 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libegl-mesa0 amd64 21.2.6-0ubuntu0.1~20.04.2 [96.3 kB]

2024-09-06 00:32:21,477 - execution_module.docker_manager -raylib - INFO - Get:30 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libegl1 amd64 1.3.2-1~ubuntu0.20.04.2 [31.9 kB]

2024-09-06 00:32:21,913 - execution_module.docker_manager -raylib - INFO - Get:31 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb-glx0 amd64 1.14-2 [22.1 kB]

2024-09-06 00:32:22,278 - execution_module.docker_manager -raylib - INFO - Get:32 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb-shm0 amd64 1.14-2 [5584 B]

2024-09-06 00:32:22,831 - execution_module.docker_manager -raylib - INFO - Get:33 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxfixes3 amd64 1:5.0.3-2 [10.9 kB]

2024-09-06 00:32:23,424 - execution_module.docker_manager -raylib - INFO - Get:34 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxxf86vm1 amd64 1:1.1.4-1build1 [10.2 kB]

2024-09-06 00:32:23,466 - execution_module.docker_manager -raylib - INFO - Get:35 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libllvm12 amd64 1:12.0.0-3ubuntu1~20.04.5 [18.8 MB]

2024-09-06 00:32:42,256 - execution_module.docker_manager -raylib - INFO - Get:36 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libsensors-config all 1:3.6.0-2ubuntu1.1 [6052 B]

2024-09-06 00:32:42,389 - execution_module.docker_manager -raylib - INFO - Get:37 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libsensors5 amd64 1:3.6.0-2ubuntu1.1 [27.2 kB]

2024-09-06 00:32:42,710 - execution_module.docker_manager -raylib - INFO - Get:38 http://mirrors.aliyun.com/ubuntu focal/main amd64 libvulkan1 amd64 1.2.131.2-1 [93.3 kB]

2024-09-06 00:32:42,841 - execution_module.docker_manager -raylib - INFO - Get:39 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libgl1-mesa-dri amd64 21.2.6-0ubuntu0.1~20.04.2 [11.0 MB]

2024-09-06 00:32:49,798 - execution_module.docker_manager -raylib - INFO - Get:40 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libglx-mesa0 amd64 21.2.6-0ubuntu0.1~20.04.2 [137 kB]

2024-09-06 00:32:50,217 - execution_module.docker_manager -raylib - INFO - Get:41 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libglx0 amd64 1.3.2-1~ubuntu0.20.04.2 [32.5 kB]

2024-09-06 00:32:50,287 - execution_module.docker_manager -raylib - INFO - Get:42 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libgl1 amd64 1.3.2-1~ubuntu0.20.04.2 [85.8 kB]

2024-09-06 00:32:50,575 - execution_module.docker_manager -raylib - INFO - Get:43 http://mirrors.aliyun.com/ubuntu focal/main amd64 xorg-sgml-doctools all 1:1.11-1 [12.9 kB]

2024-09-06 00:32:50,824 - execution_module.docker_manager -raylib - INFO - Get:44 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-dev all 2019.2-1ubuntu1 [594 kB]

2024-09-06 00:32:51,073 - execution_module.docker_manager -raylib - INFO - Get:45 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-core-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-06 00:32:51,129 - execution_module.docker_manager -raylib - INFO - Get:46 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxau-dev amd64 1:1.0.9-0ubuntu1 [9552 B]

2024-09-06 00:32:51,190 - execution_module.docker_manager -raylib - INFO - Get:47 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxdmcp-dev amd64 1:1.1.3-0ubuntu1 [25.3 kB]

2024-09-06 00:32:51,355 - execution_module.docker_manager -raylib - INFO - Get:48 http://mirrors.aliyun.com/ubuntu focal/main amd64 xtrans-dev all 1.4.0-1 [68.9 kB]

2024-09-06 00:32:51,463 - execution_module.docker_manager -raylib - INFO - Get:49 http://mirrors.aliyun.com/ubuntu focal/main amd64 libpthread-stubs0-dev amd64 0.4-1 [5384 B]

2024-09-06 00:32:51,526 - execution_module.docker_manager -raylib - INFO - Get:50 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb1-dev amd64 1.14-2 [80.5 kB]

2024-09-06 00:32:51,620 - execution_module.docker_manager -raylib - INFO - Get:51 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libx11-dev amd64 2:1.6.9-2ubuntu1.6 [648 kB]

2024-09-06 00:32:52,102 - execution_module.docker_manager -raylib - INFO - Get:52 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libglx-dev amd64 1.3.2-1~ubuntu0.20.04.2 [14.0 kB]

2024-09-06 00:32:52,171 - execution_module.docker_manager -raylib - INFO - Get:53 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libgl-dev amd64 1.3.2-1~ubuntu0.20.04.2 [97.8 kB]

2024-09-06 00:32:52,308 - execution_module.docker_manager -raylib - INFO - Get:54 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libegl-dev amd64 1.3.2-1~ubuntu0.20.04.2 [17.2 kB]

2024-09-06 00:32:52,364 - execution_module.docker_manager -raylib - INFO - Get:55 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libgles1 amd64 1.3.2-1~ubuntu0.20.04.2 [10.3 kB]

2024-09-06 00:32:52,413 - execution_module.docker_manager -raylib - INFO - Get:56 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libgles2 amd64 1.3.2-1~ubuntu0.20.04.2 [15.6 kB]

2024-09-06 00:32:52,477 - execution_module.docker_manager -raylib - INFO - Get:57 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libgles-dev amd64 1.3.2-1~ubuntu0.20.04.2 [47.9 kB]

2024-09-06 00:32:52,554 - execution_module.docker_manager -raylib - INFO - Get:58 http://mirrors.aliyun.com/ubuntu focal/main amd64 libglu1-mesa amd64 9.0.1-1build1 [168 kB]

2024-09-06 00:32:52,715 - execution_module.docker_manager -raylib - INFO - Get:59 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libopengl0 amd64 1.3.2-1~ubuntu0.20.04.2 [29.2 kB]

2024-09-06 00:32:52,817 - execution_module.docker_manager -raylib - INFO - Get:60 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libopengl-dev amd64 1.3.2-1~ubuntu0.20.04.2 [3584 B]

2024-09-06 00:32:52,876 - execution_module.docker_manager -raylib - INFO - Get:61 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libglvnd-dev amd64 1.3.2-1~ubuntu0.20.04.2 [11.6 kB]

2024-09-06 00:32:52,966 - execution_module.docker_manager -raylib - INFO - Get:62 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libgl1-mesa-dev amd64 21.2.6-0ubuntu0.1~20.04.2 [6420 B]

2024-09-06 00:32:53,017 - execution_module.docker_manager -raylib - INFO - Get:63 http://mirrors.aliyun.com/ubuntu focal/main amd64 libglu1-mesa-dev amd64 9.0.1-1build1 [207 kB]

2024-09-06 00:32:53,210 - execution_module.docker_manager -raylib - INFO - Get:64 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-cursor0 amd64 1.18.0-1ubuntu0.1 [10.3 kB]

2024-09-06 00:32:53,331 - execution_module.docker_manager -raylib - INFO - Get:65 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-egl1 amd64 1.18.0-1ubuntu0.1 [5596 B]

2024-09-06 00:32:53,405 - execution_module.docker_manager -raylib - INFO - Get:66 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcb-randr0 amd64 1.14-2 [16.3 kB]

2024-09-06 00:32:53,455 - execution_module.docker_manager -raylib - INFO - Get:67 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrender1 amd64 1:0.9.10-1 [18.7 kB]

2024-09-06 00:32:53,524 - execution_module.docker_manager -raylib - INFO - Get:68 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcursor1 amd64 1:1.2.0-2 [20.1 kB]

2024-09-06 00:32:53,581 - execution_module.docker_manager -raylib - INFO - Get:69 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrender-dev amd64 1:0.9.10-1 [24.9 kB]

2024-09-06 00:32:53,658 - execution_module.docker_manager -raylib - INFO - Get:70 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxfixes-dev amd64 1:5.0.3-2 [11.4 kB]

2024-09-06 00:32:53,710 - execution_module.docker_manager -raylib - INFO - Get:71 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxcursor-dev amd64 1:1.2.0-2 [26.5 kB]

2024-09-06 00:32:53,774 - execution_module.docker_manager -raylib - INFO - Get:72 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-xext-dev all 2019.2-1ubuntu1 [2616 B]

2024-09-06 00:32:53,834 - execution_module.docker_manager -raylib - INFO - Get:73 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxext-dev amd64 2:1.3.4-0ubuntu1 [82.2 kB]

2024-09-06 00:32:54,322 - execution_module.docker_manager -raylib - INFO - Get:74 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxi6 amd64 2:1.7.10-0ubuntu1 [29.9 kB]

2024-09-06 00:32:54,424 - execution_module.docker_manager -raylib - INFO - Get:75 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-input-dev all 2019.2-1ubuntu1 [2628 B]

2024-09-06 00:32:54,479 - execution_module.docker_manager -raylib - INFO - Get:76 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxi-dev amd64 2:1.7.10-0ubuntu1 [187 kB]

2024-09-06 00:32:55,044 - execution_module.docker_manager -raylib - INFO - Get:77 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxinerama1 amd64 2:1.1.4-2 [6904 B]

2024-09-06 00:32:55,124 - execution_module.docker_manager -raylib - INFO - Get:78 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-xinerama-dev all 2019.2-1ubuntu1 [2628 B]

2024-09-06 00:32:55,266 - execution_module.docker_manager -raylib - INFO - Get:79 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxinerama-dev amd64 2:1.1.4-2 [7896 B]

2024-09-06 00:32:55,366 - execution_module.docker_manager -raylib - INFO - Get:80 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon0 amd64 0.10.0-1 [98.4 kB]

2024-09-06 00:32:55,647 - execution_module.docker_manager -raylib - INFO - Get:81 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxkbcommon-dev amd64 0.10.0-1 [45.4 kB]

2024-09-06 00:32:55,714 - execution_module.docker_manager -raylib - INFO - Get:82 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrandr2 amd64 2:1.5.2-0ubuntu1 [18.5 kB]

2024-09-06 00:32:55,787 - execution_module.docker_manager -raylib - INFO - Get:83 http://mirrors.aliyun.com/ubuntu focal/main amd64 x11proto-randr-dev all 2019.2-1ubuntu1 [2620 B]

2024-09-06 00:32:55,862 - execution_module.docker_manager -raylib - INFO - Get:84 http://mirrors.aliyun.com/ubuntu focal/main amd64 libxrandr-dev amd64 2:1.5.2-0ubuntu1 [25.0 kB]

2024-09-06 00:32:56,000 - execution_module.docker_manager -raylib - INFO - Get:85 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 mesa-vulkan-drivers amd64 21.2.6-0ubuntu0.1~20.04.2 [5788 kB]

2024-09-06 00:32:59,787 - execution_module.docker_manager -raylib - INFO - Get:86 http://mirrors.aliyun.com/ubuntu focal/main amd64 pkg-config amd64 0.29.1-0ubuntu4 [45.5 kB]

2024-09-06 00:32:59,967 - execution_module.docker_manager -raylib - INFO - Get:87 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-bin amd64 1.18.0-1ubuntu0.1 [20.2 kB]

2024-09-06 00:33:00,088 - execution_module.docker_manager -raylib - INFO - Get:88 http://mirrors.aliyun.com/ubuntu focal-updates/main amd64 libwayland-dev amd64 1.18.0-1ubuntu0.1 [64.6 kB]

2024-09-06 00:33:00,158 - execution_module.docker_manager -raylib - INFO - Get:89 http://mirrors.aliyun.com/ubuntu focal/main amd64 wayland-protocols all 1.20-1 [60.3 kB]

2024-09-06 00:33:00,598 - execution_module.docker_manager -raylib - INFO - [91mdebconf: delaying package configuration, since apt-utils is not installed
[0m
2024-09-06 00:33:00,809 - execution_module.docker_manager -raylib - INFO - Fetched 40.7 MB in 43s (947 kB/s)

2024-09-06 00:33:13,345 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libbsd0:amd64.
(Reading database ... 
2024-09-06 00:33:13,358 - execution_module.docker_manager -raylib - INFO - (Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
2024-09-06 00:33:13,358 - execution_module.docker_manager -raylib - INFO - (Reading database ... 65%
2024-09-06 00:33:13,359 - execution_module.docker_manager -raylib - INFO - (Reading database ... 70%
2024-09-06 00:33:13,359 - execution_module.docker_manager -raylib - INFO - (Reading database ... 75%
2024-09-06 00:33:13,359 - execution_module.docker_manager -raylib - INFO - (Reading database ... 80%
2024-09-06 00:33:13,359 - execution_module.docker_manager -raylib - INFO - (Reading database ... 85%
2024-09-06 00:33:13,359 - execution_module.docker_manager -raylib - INFO - (Reading database ... 90%
2024-09-06 00:33:13,361 - execution_module.docker_manager -raylib - INFO - (Reading database ... 95%
2024-09-06 00:33:13,362 - execution_module.docker_manager -raylib - INFO - (Reading database ... 100%
(Reading database ... 21508 files and directories currently installed.)

2024-09-06 00:33:13,363 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../00-libbsd0_0.10.0-1_amd64.deb ...

2024-09-06 00:33:14,690 - execution_module.docker_manager -raylib - INFO - Unpacking libbsd0:amd64 (0.10.0-1) ...

2024-09-06 00:33:19,790 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xkb-data.

2024-09-06 00:33:19,793 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../01-xkb-data_2.29-2_all.deb ...

2024-09-06 00:33:20,061 - execution_module.docker_manager -raylib - INFO - Unpacking xkb-data (2.29-2) ...

2024-09-06 00:33:21,139 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libdrm-common.

2024-09-06 00:33:21,145 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../02-libdrm-common_2.4.107-8ubuntu1~20.04.2_all.deb ...

2024-09-06 00:33:21,321 - execution_module.docker_manager -raylib - INFO - Unpacking libdrm-common (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:33:22,458 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libdrm2:amd64.

2024-09-06 00:33:22,461 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../03-libdrm2_2.4.107-8ubuntu1~20.04.2_amd64.deb ...

2024-09-06 00:33:22,637 - execution_module.docker_manager -raylib - INFO - Unpacking libdrm2:amd64 (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:33:23,756 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libedit2:amd64.

2024-09-06 00:33:23,757 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../04-libedit2_3.1-20191231-1_amd64.deb ...

2024-09-06 00:33:23,901 - execution_module.docker_manager -raylib - INFO - Unpacking libedit2:amd64 (3.1-20191231-1) ...

2024-09-06 00:33:25,241 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau6:amd64.

2024-09-06 00:33:25,251 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../05-libxau6_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-06 00:33:25,513 - execution_module.docker_manager -raylib - INFO - Unpacking libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:33:26,903 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp6:amd64.

2024-09-06 00:33:26,904 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../06-libxdmcp6_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-06 00:33:27,068 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:33:28,866 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1:amd64.

2024-09-06 00:33:28,869 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../07-libxcb1_1.14-2_amd64.deb ...

2024-09-06 00:33:29,128 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1:amd64 (1.14-2) ...

2024-09-06 00:33:29,991 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-data.

2024-09-06 00:33:29,993 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../08-libx11-data_2%3a1.6.9-2ubuntu1.6_all.deb ...

2024-09-06 00:33:30,107 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:33:33,065 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-6:amd64.

2024-09-06 00:33:33,069 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../09-libx11-6_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-06 00:33:33,439 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:33:36,838 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxext6:amd64.

2024-09-06 00:33:36,841 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../10-libxext6_2%3a1.3.4-0ubuntu1_amd64.deb ...

2024-09-06 00:33:37,050 - execution_module.docker_manager -raylib - INFO - Unpacking libxext6:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:33:38,066 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libdrm-amdgpu1:amd64.

2024-09-06 00:33:38,069 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../11-libdrm-amdgpu1_2.4.107-8ubuntu1~20.04.2_amd64.deb ...

2024-09-06 00:33:38,312 - execution_module.docker_manager -raylib - INFO - Unpacking libdrm-amdgpu1:amd64 (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:33:39,761 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libpciaccess0:amd64.

2024-09-06 00:33:39,769 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../12-libpciaccess0_0.16-0ubuntu1_amd64.deb ...

2024-09-06 00:33:40,719 - execution_module.docker_manager -raylib - INFO - Unpacking libpciaccess0:amd64 (0.16-0ubuntu1) ...

2024-09-06 00:33:44,738 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libdrm-intel1:amd64.

2024-09-06 00:33:44,744 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../13-libdrm-intel1_2.4.107-8ubuntu1~20.04.2_amd64.deb ...

2024-09-06 00:33:45,215 - execution_module.docker_manager -raylib - INFO - Unpacking libdrm-intel1:amd64 (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:33:56,904 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libdrm-nouveau2:amd64.

2024-09-06 00:33:56,942 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../14-libdrm-nouveau2_2.4.107-8ubuntu1~20.04.2_amd64.deb ...

2024-09-06 00:33:57,624 - execution_module.docker_manager -raylib - INFO - Unpacking libdrm-nouveau2:amd64 (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:34:00,513 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libdrm-radeon1:amd64.

2024-09-06 00:34:00,535 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../15-libdrm-radeon1_2.4.107-8ubuntu1~20.04.2_amd64.deb ...

2024-09-06 00:34:00,846 - execution_module.docker_manager -raylib - INFO - Unpacking libdrm-radeon1:amd64 (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:34:02,947 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libglvnd0:amd64.

2024-09-06 00:34:02,972 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../16-libglvnd0_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:34:03,357 - execution_module.docker_manager -raylib - INFO - Unpacking libglvnd0:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:34:05,913 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-server0:amd64.

2024-09-06 00:34:05,916 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../17-libwayland-server0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:34:06,678 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:34:11,068 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libgbm1:amd64.

2024-09-06 00:34:11,071 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../18-libgbm1_21.2.6-0ubuntu0.1~20.04.2_amd64.deb ...

2024-09-06 00:34:11,669 - execution_module.docker_manager -raylib - INFO - Unpacking libgbm1:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:34:15,256 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libglapi-mesa:amd64.

2024-09-06 00:34:15,260 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../19-libglapi-mesa_21.2.6-0ubuntu0.1~20.04.2_amd64.deb ...

2024-09-06 00:34:16,399 - execution_module.docker_manager -raylib - INFO - Unpacking libglapi-mesa:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:34:20,895 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-client0:amd64.

2024-09-06 00:34:20,898 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../20-libwayland-client0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:34:21,629 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:34:25,237 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-xcb1:amd64.

2024-09-06 00:34:25,240 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../21-libx11-xcb1_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-06 00:34:25,579 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-xcb1:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:34:28,187 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb-dri2-0:amd64.

2024-09-06 00:34:28,189 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../22-libxcb-dri2-0_1.14-2_amd64.deb ...

2024-09-06 00:34:28,705 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb-dri2-0:amd64 (1.14-2) ...

2024-09-06 00:34:30,986 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb-dri3-0:amd64.

2024-09-06 00:34:30,988 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../23-libxcb-dri3-0_1.14-2_amd64.deb ...

2024-09-06 00:34:31,631 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb-dri3-0:amd64 (1.14-2) ...

2024-09-06 00:34:35,329 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb-present0:amd64.

2024-09-06 00:34:35,330 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../24-libxcb-present0_1.14-2_amd64.deb ...

2024-09-06 00:34:35,533 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb-present0:amd64 (1.14-2) ...

2024-09-06 00:34:38,165 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb-sync1:amd64.

2024-09-06 00:34:38,180 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../25-libxcb-sync1_1.14-2_amd64.deb ...

2024-09-06 00:34:38,315 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb-sync1:amd64 (1.14-2) ...

2024-09-06 00:34:40,114 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb-xfixes0:amd64.

2024-09-06 00:34:40,117 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../26-libxcb-xfixes0_1.14-2_amd64.deb ...

2024-09-06 00:34:40,351 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb-xfixes0:amd64 (1.14-2) ...

2024-09-06 00:34:41,614 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxshmfence1:amd64.

2024-09-06 00:34:41,617 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../27-libxshmfence1_1.3-1_amd64.deb ...

2024-09-06 00:34:41,803 - execution_module.docker_manager -raylib - INFO - Unpacking libxshmfence1:amd64 (1.3-1) ...

2024-09-06 00:34:42,934 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libegl-mesa0:amd64.

2024-09-06 00:34:42,979 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../28-libegl-mesa0_21.2.6-0ubuntu0.1~20.04.2_amd64.deb ...

2024-09-06 00:34:43,229 - execution_module.docker_manager -raylib - INFO - Unpacking libegl-mesa0:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:34:44,399 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libegl1:amd64.

2024-09-06 00:34:44,402 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../29-libegl1_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:34:44,561 - execution_module.docker_manager -raylib - INFO - Unpacking libegl1:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:34:45,895 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb-glx0:amd64.

2024-09-06 00:34:45,900 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../30-libxcb-glx0_1.14-2_amd64.deb ...

2024-09-06 00:34:46,090 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb-glx0:amd64 (1.14-2) ...

2024-09-06 00:34:47,800 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb-shm0:amd64.

2024-09-06 00:34:47,820 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../31-libxcb-shm0_1.14-2_amd64.deb ...

2024-09-06 00:34:48,114 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb-shm0:amd64 (1.14-2) ...

2024-09-06 00:34:50,116 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxfixes3:amd64.

2024-09-06 00:34:50,119 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../32-libxfixes3_1%3a5.0.3-2_amd64.deb ...

2024-09-06 00:34:51,098 - execution_module.docker_manager -raylib - INFO - Unpacking libxfixes3:amd64 (1:5.0.3-2) ...

2024-09-06 00:34:54,170 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxxf86vm1:amd64.

2024-09-06 00:34:54,173 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../33-libxxf86vm1_1%3a1.1.4-1build1_amd64.deb ...

2024-09-06 00:34:54,711 - execution_module.docker_manager -raylib - INFO - Unpacking libxxf86vm1:amd64 (1:1.1.4-1build1) ...

2024-09-06 00:34:57,523 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libllvm12:amd64.

2024-09-06 00:34:57,526 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../34-libllvm12_1%3a12.0.0-3ubuntu1~20.04.5_amd64.deb ...

2024-09-06 00:34:58,396 - execution_module.docker_manager -raylib - INFO - Unpacking libllvm12:amd64 (1:12.0.0-3ubuntu1~20.04.5) ...

2024-09-06 00:35:02,943 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libsensors-config.

2024-09-06 00:35:02,946 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../35-libsensors-config_1%3a3.6.0-2ubuntu1.1_all.deb ...

2024-09-06 00:35:03,215 - execution_module.docker_manager -raylib - INFO - Unpacking libsensors-config (1:3.6.0-2ubuntu1.1) ...

2024-09-06 00:35:04,770 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libsensors5:amd64.

2024-09-06 00:35:04,772 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../36-libsensors5_1%3a3.6.0-2ubuntu1.1_amd64.deb ...

2024-09-06 00:35:05,680 - execution_module.docker_manager -raylib - INFO - Unpacking libsensors5:amd64 (1:3.6.0-2ubuntu1.1) ...

2024-09-06 00:35:07,006 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libvulkan1:amd64.

2024-09-06 00:35:07,008 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../37-libvulkan1_1.2.131.2-1_amd64.deb ...

2024-09-06 00:35:07,666 - execution_module.docker_manager -raylib - INFO - Unpacking libvulkan1:amd64 (1.2.131.2-1) ...

2024-09-06 00:35:10,729 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libgl1-mesa-dri:amd64.

2024-09-06 00:35:10,753 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../38-libgl1-mesa-dri_21.2.6-0ubuntu0.1~20.04.2_amd64.deb ...

2024-09-06 00:35:11,403 - execution_module.docker_manager -raylib - INFO - Unpacking libgl1-mesa-dri:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:35:19,263 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libglx-mesa0:amd64.

2024-09-06 00:35:19,574 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../39-libglx-mesa0_21.2.6-0ubuntu0.1~20.04.2_amd64.deb ...

2024-09-06 00:35:20,575 - execution_module.docker_manager -raylib - INFO - Unpacking libglx-mesa0:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:35:26,675 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libglx0:amd64.

2024-09-06 00:35:26,679 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../40-libglx0_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:35:27,225 - execution_module.docker_manager -raylib - INFO - Unpacking libglx0:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:35:31,880 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libgl1:amd64.

2024-09-06 00:35:31,899 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../41-libgl1_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:35:32,078 - execution_module.docker_manager -raylib - INFO - Unpacking libgl1:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:35:33,647 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xorg-sgml-doctools.

2024-09-06 00:35:33,650 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../42-xorg-sgml-doctools_1%3a1.11-1_all.deb ...

2024-09-06 00:35:33,866 - execution_module.docker_manager -raylib - INFO - Unpacking xorg-sgml-doctools (1:1.11-1) ...

2024-09-06 00:35:35,291 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-dev.

2024-09-06 00:35:35,294 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../43-x11proto-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:35:35,493 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-dev (2019.2-1ubuntu1) ...

2024-09-06 00:35:36,424 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-core-dev.

2024-09-06 00:35:36,427 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../44-x11proto-core-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:35:36,585 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-06 00:35:39,188 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxau-dev:amd64.

2024-09-06 00:35:39,206 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../45-libxau-dev_1%3a1.0.9-0ubuntu1_amd64.deb ...

2024-09-06 00:35:39,656 - execution_module.docker_manager -raylib - INFO - Unpacking libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:35:41,835 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxdmcp-dev:amd64.

2024-09-06 00:35:41,838 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../46-libxdmcp-dev_1%3a1.1.3-0ubuntu1_amd64.deb ...

2024-09-06 00:35:42,565 - execution_module.docker_manager -raylib - INFO - Unpacking libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:35:44,988 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package xtrans-dev.

2024-09-06 00:35:45,009 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../47-xtrans-dev_1.4.0-1_all.deb ...

2024-09-06 00:35:45,277 - execution_module.docker_manager -raylib - INFO - Unpacking xtrans-dev (1.4.0-1) ...

2024-09-06 00:35:46,841 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libpthread-stubs0-dev:amd64.

2024-09-06 00:35:46,843 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../48-libpthread-stubs0-dev_0.4-1_amd64.deb ...

2024-09-06 00:35:47,001 - execution_module.docker_manager -raylib - INFO - Unpacking libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-06 00:35:48,035 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb1-dev:amd64.

2024-09-06 00:35:48,050 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../49-libxcb1-dev_1.14-2_amd64.deb ...

2024-09-06 00:35:48,334 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb1-dev:amd64 (1.14-2) ...

2024-09-06 00:35:49,771 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libx11-dev:amd64.

2024-09-06 00:35:49,773 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../50-libx11-dev_2%3a1.6.9-2ubuntu1.6_amd64.deb ...

2024-09-06 00:35:50,398 - execution_module.docker_manager -raylib - INFO - Unpacking libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:35:51,510 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libglx-dev:amd64.

2024-09-06 00:35:51,515 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../51-libglx-dev_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:35:51,885 - execution_module.docker_manager -raylib - INFO - Unpacking libglx-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:35:52,688 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libgl-dev:amd64.

2024-09-06 00:35:52,689 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../52-libgl-dev_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:35:53,006 - execution_module.docker_manager -raylib - INFO - Unpacking libgl-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:35:54,726 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libegl-dev:amd64.

2024-09-06 00:35:54,729 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../53-libegl-dev_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:35:54,854 - execution_module.docker_manager -raylib - INFO - Unpacking libegl-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:35:55,665 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libgles1:amd64.

2024-09-06 00:35:55,667 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../54-libgles1_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:35:55,734 - execution_module.docker_manager -raylib - INFO - Unpacking libgles1:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:35:56,064 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libgles2:amd64.

2024-09-06 00:35:56,066 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../55-libgles2_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:35:56,112 - execution_module.docker_manager -raylib - INFO - Unpacking libgles2:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:35:57,696 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libgles-dev:amd64.

2024-09-06 00:35:57,725 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../56-libgles-dev_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:35:58,129 - execution_module.docker_manager -raylib - INFO - Unpacking libgles-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:35:59,849 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libglu1-mesa:amd64.

2024-09-06 00:35:59,850 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../57-libglu1-mesa_9.0.1-1build1_amd64.deb ...

2024-09-06 00:35:59,952 - execution_module.docker_manager -raylib - INFO - Unpacking libglu1-mesa:amd64 (9.0.1-1build1) ...

2024-09-06 00:36:02,093 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libopengl0:amd64.

2024-09-06 00:36:02,111 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../58-libopengl0_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:36:02,487 - execution_module.docker_manager -raylib - INFO - Unpacking libopengl0:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:36:04,977 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libopengl-dev:amd64.

2024-09-06 00:36:04,980 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../59-libopengl-dev_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:36:05,665 - execution_module.docker_manager -raylib - INFO - Unpacking libopengl-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:36:06,393 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libglvnd-dev:amd64.

2024-09-06 00:36:06,395 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../60-libglvnd-dev_1.3.2-1~ubuntu0.20.04.2_amd64.deb ...

2024-09-06 00:36:06,431 - execution_module.docker_manager -raylib - INFO - Unpacking libglvnd-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:36:06,655 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libgl1-mesa-dev:amd64.

2024-09-06 00:36:06,656 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../61-libgl1-mesa-dev_21.2.6-0ubuntu0.1~20.04.2_amd64.deb ...

2024-09-06 00:36:06,689 - execution_module.docker_manager -raylib - INFO - Unpacking libgl1-mesa-dev:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:36:06,887 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libglu1-mesa-dev:amd64.

2024-09-06 00:36:06,888 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../62-libglu1-mesa-dev_9.0.1-1build1_amd64.deb ...

2024-09-06 00:36:06,932 - execution_module.docker_manager -raylib - INFO - Unpacking libglu1-mesa-dev:amd64 (9.0.1-1build1) ...

2024-09-06 00:36:07,209 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-cursor0:amd64.

2024-09-06 00:36:07,210 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../63-libwayland-cursor0_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:36:07,256 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:36:07,570 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-egl1:amd64.

2024-09-06 00:36:07,572 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../64-libwayland-egl1_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:36:07,609 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:36:16,249 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcb-randr0:amd64.

2024-09-06 00:36:16,313 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../65-libxcb-randr0_1.14-2_amd64.deb ...

2024-09-06 00:36:16,966 - execution_module.docker_manager -raylib - INFO - Unpacking libxcb-randr0:amd64 (1.14-2) ...

2024-09-06 00:36:21,037 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrender1:amd64.

2024-09-06 00:36:21,038 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../66-libxrender1_1%3a0.9.10-1_amd64.deb ...

2024-09-06 00:36:22,133 - execution_module.docker_manager -raylib - INFO - Unpacking libxrender1:amd64 (1:0.9.10-1) ...

2024-09-06 00:36:26,472 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcursor1:amd64.

2024-09-06 00:36:26,474 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../67-libxcursor1_1%3a1.2.0-2_amd64.deb ...

2024-09-06 00:36:26,824 - execution_module.docker_manager -raylib - INFO - Unpacking libxcursor1:amd64 (1:1.2.0-2) ...

2024-09-06 00:36:30,325 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrender-dev:amd64.

2024-09-06 00:36:30,327 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../68-libxrender-dev_1%3a0.9.10-1_amd64.deb ...

2024-09-06 00:36:30,881 - execution_module.docker_manager -raylib - INFO - Unpacking libxrender-dev:amd64 (1:0.9.10-1) ...

2024-09-06 00:36:32,371 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxfixes-dev:amd64.

2024-09-06 00:36:32,372 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../69-libxfixes-dev_1%3a5.0.3-2_amd64.deb ...

2024-09-06 00:36:32,518 - execution_module.docker_manager -raylib - INFO - Unpacking libxfixes-dev:amd64 (1:5.0.3-2) ...

2024-09-06 00:36:34,923 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxcursor-dev:amd64.

2024-09-06 00:36:34,926 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../70-libxcursor-dev_1%3a1.2.0-2_amd64.deb ...

2024-09-06 00:36:35,266 - execution_module.docker_manager -raylib - INFO - Unpacking libxcursor-dev:amd64 (1:1.2.0-2) ...

2024-09-06 00:36:37,104 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-xext-dev.

2024-09-06 00:36:37,106 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../71-x11proto-xext-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:36:37,348 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-xext-dev (2019.2-1ubuntu1) ...

2024-09-06 00:36:39,341 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxext-dev:amd64.

2024-09-06 00:36:39,371 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../72-libxext-dev_2%3a1.3.4-0ubuntu1_amd64.deb ...

2024-09-06 00:36:39,547 - execution_module.docker_manager -raylib - INFO - Unpacking libxext-dev:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:36:40,789 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxi6:amd64.

2024-09-06 00:36:40,792 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../73-libxi6_2%3a1.7.10-0ubuntu1_amd64.deb ...

2024-09-06 00:36:40,834 - execution_module.docker_manager -raylib - INFO - Unpacking libxi6:amd64 (2:1.7.10-0ubuntu1) ...

2024-09-06 00:36:41,112 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-input-dev.

2024-09-06 00:36:41,114 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../74-x11proto-input-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:36:41,465 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-input-dev (2019.2-1ubuntu1) ...

2024-09-06 00:36:43,378 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxi-dev:amd64.

2024-09-06 00:36:43,379 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../75-libxi-dev_2%3a1.7.10-0ubuntu1_amd64.deb ...

2024-09-06 00:36:43,521 - execution_module.docker_manager -raylib - INFO - Unpacking libxi-dev:amd64 (2:1.7.10-0ubuntu1) ...

2024-09-06 00:36:44,761 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxinerama1:amd64.

2024-09-06 00:36:44,762 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../76-libxinerama1_2%3a1.1.4-2_amd64.deb ...

2024-09-06 00:36:44,827 - execution_module.docker_manager -raylib - INFO - Unpacking libxinerama1:amd64 (2:1.1.4-2) ...

2024-09-06 00:36:45,079 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-xinerama-dev.

2024-09-06 00:36:45,081 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../77-x11proto-xinerama-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:36:45,119 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-xinerama-dev (2019.2-1ubuntu1) ...

2024-09-06 00:36:46,739 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxinerama-dev:amd64.

2024-09-06 00:36:46,740 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../78-libxinerama-dev_2%3a1.1.4-2_amd64.deb ...

2024-09-06 00:36:47,372 - execution_module.docker_manager -raylib - INFO - Unpacking libxinerama-dev:amd64 (2:1.1.4-2) ...

2024-09-06 00:36:48,563 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon0:amd64.

2024-09-06 00:36:48,570 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../79-libxkbcommon0_0.10.0-1_amd64.deb ...

2024-09-06 00:36:48,627 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-06 00:36:50,125 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxkbcommon-dev:amd64.

2024-09-06 00:36:50,133 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../80-libxkbcommon-dev_0.10.0-1_amd64.deb ...

2024-09-06 00:36:50,262 - execution_module.docker_manager -raylib - INFO - Unpacking libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-06 00:36:51,304 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrandr2:amd64.

2024-09-06 00:36:51,306 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../81-libxrandr2_2%3a1.5.2-0ubuntu1_amd64.deb ...

2024-09-06 00:36:51,716 - execution_module.docker_manager -raylib - INFO - Unpacking libxrandr2:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:36:53,395 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package x11proto-randr-dev.

2024-09-06 00:36:53,410 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../82-x11proto-randr-dev_2019.2-1ubuntu1_all.deb ...

2024-09-06 00:36:53,777 - execution_module.docker_manager -raylib - INFO - Unpacking x11proto-randr-dev (2019.2-1ubuntu1) ...

2024-09-06 00:36:55,620 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libxrandr-dev:amd64.

2024-09-06 00:36:55,622 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../83-libxrandr-dev_2%3a1.5.2-0ubuntu1_amd64.deb ...

2024-09-06 00:36:56,531 - execution_module.docker_manager -raylib - INFO - Unpacking libxrandr-dev:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:36:56,861 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package mesa-vulkan-drivers:amd64.

2024-09-06 00:36:56,863 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../84-mesa-vulkan-drivers_21.2.6-0ubuntu0.1~20.04.2_amd64.deb ...

2024-09-06 00:36:56,908 - execution_module.docker_manager -raylib - INFO - Unpacking mesa-vulkan-drivers:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:36:58,152 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package pkg-config.

2024-09-06 00:36:58,155 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../85-pkg-config_0.29.1-0ubuntu4_amd64.deb ...

2024-09-06 00:36:58,186 - execution_module.docker_manager -raylib - INFO - Unpacking pkg-config (0.29.1-0ubuntu4) ...

2024-09-06 00:36:58,436 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-bin.

2024-09-06 00:36:58,438 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../86-libwayland-bin_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:36:58,487 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-06 00:36:58,709 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package libwayland-dev:amd64.

2024-09-06 00:36:58,711 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../87-libwayland-dev_1.18.0-1ubuntu0.1_amd64.deb ...

2024-09-06 00:36:58,762 - execution_module.docker_manager -raylib - INFO - Unpacking libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:36:59,014 - execution_module.docker_manager -raylib - INFO - Selecting previously unselected package wayland-protocols.

2024-09-06 00:36:59,016 - execution_module.docker_manager -raylib - INFO - Preparing to unpack .../88-wayland-protocols_1.20-1_all.deb ...

2024-09-06 00:36:59,065 - execution_module.docker_manager -raylib - INFO - Unpacking wayland-protocols (1.20-1) ...

2024-09-06 00:37:04,304 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-server0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:37:05,492 - execution_module.docker_manager -raylib - INFO - Setting up libx11-xcb1:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:37:07,346 - execution_module.docker_manager -raylib - INFO - Setting up libpciaccess0:amd64 (0.16-0ubuntu1) ...

2024-09-06 00:37:09,299 - execution_module.docker_manager -raylib - INFO - Setting up libxau6:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:37:11,103 - execution_module.docker_manager -raylib - INFO - Setting up libglvnd0:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:37:14,862 - execution_module.docker_manager -raylib - INFO - Setting up libsensors-config (1:3.6.0-2ubuntu1.1) ...

2024-09-06 00:37:19,677 - execution_module.docker_manager -raylib - INFO - Setting up xkb-data (2.29-2) ...

2024-09-06 00:37:27,570 - execution_module.docker_manager -raylib - INFO - Setting up libpthread-stubs0-dev:amd64 (0.4-1) ...

2024-09-06 00:37:34,739 - execution_module.docker_manager -raylib - INFO - Setting up libopengl0:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:37:43,313 - execution_module.docker_manager -raylib - INFO - Setting up xtrans-dev (1.4.0-1) ...

2024-09-06 00:37:47,488 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-bin (1.18.0-1ubuntu0.1) ...

2024-09-06 00:37:49,195 - execution_module.docker_manager -raylib - INFO - Setting up libgles2:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:37:50,902 - execution_module.docker_manager -raylib - INFO - Setting up libx11-data (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:37:51,298 - execution_module.docker_manager -raylib - INFO - Setting up libgles1:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:37:52,128 - execution_module.docker_manager -raylib - INFO - Setting up pkg-config (0.29.1-0ubuntu4) ...

2024-09-06 00:37:53,923 - execution_module.docker_manager -raylib - INFO - Setting up libsensors5:amd64 (1:3.6.0-2ubuntu1.1) ...

2024-09-06 00:37:54,904 - execution_module.docker_manager -raylib - INFO - Setting up libglapi-mesa:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:37:55,412 - execution_module.docker_manager -raylib - INFO - Setting up libvulkan1:amd64 (1.2.131.2-1) ...

2024-09-06 00:37:55,587 - execution_module.docker_manager -raylib - INFO - Setting up wayland-protocols (1.20-1) ...

2024-09-06 00:37:55,760 - execution_module.docker_manager -raylib - INFO - Setting up libxshmfence1:amd64 (1.3-1) ...

2024-09-06 00:37:55,893 - execution_module.docker_manager -raylib - INFO - Setting up xorg-sgml-doctools (1:1.11-1) ...

2024-09-06 00:37:56,039 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-egl1:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:37:56,175 - execution_module.docker_manager -raylib - INFO - Setting up libopengl-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:37:56,350 - execution_module.docker_manager -raylib - INFO - Setting up libbsd0:amd64 (0.10.0-1) ...

2024-09-06 00:37:56,502 - execution_module.docker_manager -raylib - INFO - Setting up libdrm-common (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:37:58,059 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon0:amd64 (0.10.0-1) ...

2024-09-06 00:37:58,863 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-client0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:38:00,282 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-dev (2019.2-1ubuntu1) ...

2024-09-06 00:38:00,445 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp6:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:38:00,602 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1:amd64 (1.14-2) ...

2024-09-06 00:38:01,448 - execution_module.docker_manager -raylib - INFO - Setting up libxcb-xfixes0:amd64 (1.14-2) ...

2024-09-06 00:38:02,320 - execution_module.docker_manager -raylib - INFO - Setting up libxau-dev:amd64 (1:1.0.9-0ubuntu1) ...

2024-09-06 00:38:03,625 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-randr-dev (2019.2-1ubuntu1) ...

2024-09-06 00:38:04,022 - execution_module.docker_manager -raylib - INFO - Setting up libxcb-glx0:amd64 (1.14-2) ...

2024-09-06 00:38:05,196 - execution_module.docker_manager -raylib - INFO - Setting up libxkbcommon-dev:amd64 (0.10.0-1) ...

2024-09-06 00:38:05,359 - execution_module.docker_manager -raylib - INFO - Setting up libedit2:amd64 (3.1-20191231-1) ...

2024-09-06 00:38:06,104 - execution_module.docker_manager -raylib - INFO - Setting up libxcb-shm0:amd64 (1.14-2) ...

2024-09-06 00:38:07,126 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-xinerama-dev (2019.2-1ubuntu1) ...

2024-09-06 00:38:08,728 - execution_module.docker_manager -raylib - INFO - Setting up libxcb-present0:amd64 (1.14-2) ...

2024-09-06 00:38:09,796 - execution_module.docker_manager -raylib - INFO - Setting up libxdmcp-dev:amd64 (1:1.1.3-0ubuntu1) ...

2024-09-06 00:38:12,817 - execution_module.docker_manager -raylib - INFO - Setting up libxcb-sync1:amd64 (1.14-2) ...

2024-09-06 00:38:17,353 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-core-dev (2019.2-1ubuntu1) ...

2024-09-06 00:38:19,051 - execution_module.docker_manager -raylib - INFO - Setting up libllvm12:amd64 (1:12.0.0-3ubuntu1~20.04.5) ...

2024-09-06 00:38:26,227 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-input-dev (2019.2-1ubuntu1) ...

2024-09-06 00:38:28,269 - execution_module.docker_manager -raylib - INFO - Setting up libxcb-dri2-0:amd64 (1.14-2) ...

2024-09-06 00:38:29,066 - execution_module.docker_manager -raylib - INFO - Setting up x11proto-xext-dev (2019.2-1ubuntu1) ...

2024-09-06 00:38:29,993 - execution_module.docker_manager -raylib - INFO - Setting up libdrm2:amd64 (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:38:30,580 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-cursor0:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:38:31,525 - execution_module.docker_manager -raylib - INFO - Setting up libxcb-randr0:amd64 (1.14-2) ...

2024-09-06 00:38:32,828 - execution_module.docker_manager -raylib - INFO - Setting up libx11-6:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:38:33,526 - execution_module.docker_manager -raylib - INFO - Setting up libdrm-amdgpu1:amd64 (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:38:34,326 - execution_module.docker_manager -raylib - INFO - Setting up libxcb-dri3-0:amd64 (1.14-2) ...

2024-09-06 00:38:35,110 - execution_module.docker_manager -raylib - INFO - Setting up mesa-vulkan-drivers:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:38:35,846 - execution_module.docker_manager -raylib - INFO - Setting up libdrm-nouveau2:amd64 (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:38:36,257 - execution_module.docker_manager -raylib - INFO - Setting up libxcb1-dev:amd64 (1.14-2) ...

2024-09-06 00:38:37,257 - execution_module.docker_manager -raylib - INFO - Setting up libxrender1:amd64 (1:0.9.10-1) ...

2024-09-06 00:38:38,431 - execution_module.docker_manager -raylib - INFO - Setting up libgbm1:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:38:38,646 - execution_module.docker_manager -raylib - INFO - Setting up libdrm-radeon1:amd64 (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:38:39,540 - execution_module.docker_manager -raylib - INFO - Setting up libdrm-intel1:amd64 (2.4.107-8ubuntu1~20.04.2) ...

2024-09-06 00:38:40,866 - execution_module.docker_manager -raylib - INFO - Setting up libgl1-mesa-dri:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:38:42,021 - execution_module.docker_manager -raylib - INFO - Setting up libx11-dev:amd64 (2:1.6.9-2ubuntu1.6) ...

2024-09-06 00:38:43,192 - execution_module.docker_manager -raylib - INFO - Setting up libxext6:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:38:43,978 - execution_module.docker_manager -raylib - INFO - Setting up libwayland-dev:amd64 (1.18.0-1ubuntu0.1) ...

2024-09-06 00:38:45,757 - execution_module.docker_manager -raylib - INFO - Setting up libxxf86vm1:amd64 (1:1.1.4-1build1) ...

2024-09-06 00:38:46,862 - execution_module.docker_manager -raylib - INFO - Setting up libegl-mesa0:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:38:47,374 - execution_module.docker_manager -raylib - INFO - Setting up libxfixes3:amd64 (1:5.0.3-2) ...

2024-09-06 00:38:48,464 - execution_module.docker_manager -raylib - INFO - Setting up libxinerama1:amd64 (2:1.1.4-2) ...

2024-09-06 00:38:49,376 - execution_module.docker_manager -raylib - INFO - Setting up libxrandr2:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:38:50,569 - execution_module.docker_manager -raylib - INFO - Setting up libxext-dev:amd64 (2:1.3.4-0ubuntu1) ...

2024-09-06 00:38:52,017 - execution_module.docker_manager -raylib - INFO - Setting up libegl1:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:38:53,239 - execution_module.docker_manager -raylib - INFO - Setting up libxrender-dev:amd64 (1:0.9.10-1) ...

2024-09-06 00:38:54,113 - execution_module.docker_manager -raylib - INFO - Setting up libglx-mesa0:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:38:54,863 - execution_module.docker_manager -raylib - INFO - Setting up libxi6:amd64 (2:1.7.10-0ubuntu1) ...

2024-09-06 00:38:55,854 - execution_module.docker_manager -raylib - INFO - Setting up libglx0:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:38:56,726 - execution_module.docker_manager -raylib - INFO - Setting up libxcursor1:amd64 (1:1.2.0-2) ...

2024-09-06 00:38:57,797 - execution_module.docker_manager -raylib - INFO - Setting up libxfixes-dev:amd64 (1:5.0.3-2) ...

2024-09-06 00:38:58,071 - execution_module.docker_manager -raylib - INFO - Setting up libxrandr-dev:amd64 (2:1.5.2-0ubuntu1) ...

2024-09-06 00:38:58,809 - execution_module.docker_manager -raylib - INFO - Setting up libgl1:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:38:59,941 - execution_module.docker_manager -raylib - INFO - Setting up libxinerama-dev:amd64 (2:1.1.4-2) ...

2024-09-06 00:39:00,322 - execution_module.docker_manager -raylib - INFO - Setting up libglx-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:39:01,080 - execution_module.docker_manager -raylib - INFO - Setting up libglu1-mesa:amd64 (9.0.1-1build1) ...

2024-09-06 00:39:01,889 - execution_module.docker_manager -raylib - INFO - Setting up libxi-dev:amd64 (2:1.7.10-0ubuntu1) ...

2024-09-06 00:39:03,633 - execution_module.docker_manager -raylib - INFO - Setting up libgl-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:39:19,575 - execution_module.docker_manager -raylib - INFO - Setting up libegl-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:39:22,697 - execution_module.docker_manager -raylib - INFO - Setting up libxcursor-dev:amd64 (1:1.2.0-2) ...

2024-09-06 00:39:26,454 - execution_module.docker_manager -raylib - INFO - Setting up libglu1-mesa-dev:amd64 (9.0.1-1build1) ...

2024-09-06 00:39:29,459 - execution_module.docker_manager -raylib - INFO - Setting up libgles-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:39:31,280 - execution_module.docker_manager -raylib - INFO - Setting up libglvnd-dev:amd64 (1.3.2-1~ubuntu0.20.04.2) ...

2024-09-06 00:39:32,559 - execution_module.docker_manager -raylib - INFO - Setting up libgl1-mesa-dev:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...

2024-09-06 00:39:33,617 - execution_module.docker_manager -raylib - INFO - Processing triggers for libc-bin (2.31-0ubuntu9.16) ...

2024-09-06 00:40:43,440 - execution_module.docker_manager -raylib - INFO -  ---> 6645966af532

2024-09-06 00:40:43,460 - execution_module.docker_manager -raylib - INFO - Step 11/16 : RUN mkdir /tmp/raylib
2024-09-06 00:40:45,009 - execution_module.docker_manager -raylib - INFO -  ---> Running in fc1db62d8d91

2024-09-06 00:40:50,034 - execution_module.docker_manager -raylib - INFO -  ---> 3e69fe9ae627

2024-09-06 00:40:50,034 - execution_module.docker_manager -raylib - INFO - Step 12/16 : COPY ./raylib /tmp/raylib
2024-09-06 00:43:20,299 - execution_module.docker_manager -raylib - INFO -  ---> a25afd578ce2

2024-09-06 00:43:20,299 - execution_module.docker_manager -raylib - INFO - Step 13/16 : WORKDIR /tmp/raylib
2024-09-06 00:43:21,931 - execution_module.docker_manager -raylib - INFO -  ---> Running in b1a56c627deb

2024-09-06 00:43:23,588 - execution_module.docker_manager -raylib - INFO -  ---> ed91d1c25e56

2024-09-06 00:43:23,588 - execution_module.docker_manager -raylib - INFO - Step 14/16 : RUN cmake -B build
2024-09-06 00:43:24,584 - execution_module.docker_manager -raylib - INFO -  ---> Running in c0e3fa0ccadf

2024-09-06 00:43:26,259 - execution_module.docker_manager -raylib - INFO - -- The C compiler identification is GNU 9.4.0

2024-09-06 00:43:26,362 - execution_module.docker_manager -raylib - INFO - -- The CXX compiler identification is GNU 9.4.0

2024-09-06 00:43:26,365 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc

2024-09-06 00:43:26,448 - execution_module.docker_manager -raylib - INFO - -- Check for working C compiler: /usr/bin/cc -- works

2024-09-06 00:43:26,449 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info

2024-09-06 00:43:26,543 - execution_module.docker_manager -raylib - INFO - -- Detecting C compiler ABI info - done

2024-09-06 00:43:26,552 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features

2024-09-06 00:43:26,552 - execution_module.docker_manager -raylib - INFO - -- Detecting C compile features - done

2024-09-06 00:43:26,554 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++

2024-09-06 00:43:26,686 - execution_module.docker_manager -raylib - INFO - -- Check for working CXX compiler: /usr/bin/c++ -- works

2024-09-06 00:43:26,686 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info

2024-09-06 00:43:26,968 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compiler ABI info - done

2024-09-06 00:43:26,975 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features

2024-09-06 00:43:26,976 - execution_module.docker_manager -raylib - INFO - -- Detecting CXX compile features - done

2024-09-06 00:43:26,977 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES

2024-09-06 00:43:27,376 - execution_module.docker_manager -raylib - INFO - -- Performing Test COMPILER_HAS_THOSE_TOGGLES - Success

2024-09-06 00:43:27,376 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=pointer-arith can be used -- compiles

2024-09-06 00:43:27,376 - execution_module.docker_manager -raylib - INFO - -- Testing if -Werror=implicit-function-declaration can be used -- compiles

2024-09-06 00:43:27,376 - execution_module.docker_manager -raylib - INFO - -- Testing if -fno-strict-aliasing can be used -- compiles

2024-09-06 00:43:27,382 - execution_module.docker_manager -raylib - INFO - -- Setting build type to 'Debug' as none was specified.

2024-09-06 00:43:27,382 - execution_module.docker_manager -raylib - INFO - -- Using raylib's GLFW

2024-09-06 00:43:27,385 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h

2024-09-06 00:43:27,523 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread.h - found

2024-09-06 00:43:27,523 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD

2024-09-06 00:43:27,719 - execution_module.docker_manager -raylib - INFO - -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed

2024-09-06 00:43:27,719 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads

2024-09-06 00:43:27,774 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthreads - not found

2024-09-06 00:43:27,775 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread

2024-09-06 00:43:27,861 - execution_module.docker_manager -raylib - INFO - -- Looking for pthread_create in pthread - found

2024-09-06 00:43:27,862 - execution_module.docker_manager -raylib - INFO - -- Found Threads: TRUE  

2024-09-06 00:43:27,862 - execution_module.docker_manager -raylib - INFO - -- Including Wayland support
-- Including X11 support

2024-09-06 00:43:27,949 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create

2024-09-06 00:43:28,080 - execution_module.docker_manager -raylib - INFO - -- Looking for memfd_create - found

2024-09-06 00:43:28,097 - execution_module.docker_manager -raylib - INFO - -- Found PkgConfig: /usr/bin/pkg-config (found version "0.29.1") 

2024-09-06 00:43:28,098 - execution_module.docker_manager -raylib - INFO - -- Checking for modules 'wayland-client>=0.2.7;wayland-cursor>=0.2.7;wayland-egl>=0.2.7;xkbcommon>=0.5.0'

2024-09-06 00:43:28,111 - execution_module.docker_manager -raylib - INFO - --   Found wayland-client, version 1.18.0

2024-09-06 00:43:28,118 - execution_module.docker_manager -raylib - INFO - --   Found wayland-cursor, version 1.18.0

2024-09-06 00:43:28,124 - execution_module.docker_manager -raylib - INFO - --   Found wayland-egl, version 18.1.0

2024-09-06 00:43:28,131 - execution_module.docker_manager -raylib - INFO - --   Found xkbcommon, version 0.10.0

2024-09-06 00:43:28,325 - execution_module.docker_manager -raylib - INFO - -- Found X11: /usr/include   

2024-09-06 00:43:28,326 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so

2024-09-06 00:43:28,381 - execution_module.docker_manager -raylib - INFO - -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so - found

2024-09-06 00:43:28,381 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname

2024-09-06 00:43:28,438 - execution_module.docker_manager -raylib - INFO - -- Looking for gethostbyname - found

2024-09-06 00:43:28,438 - execution_module.docker_manager -raylib - INFO - -- Looking for connect

2024-09-06 00:43:28,490 - execution_module.docker_manager -raylib - INFO - -- Looking for connect - found

2024-09-06 00:43:28,490 - execution_module.docker_manager -raylib - INFO - -- Looking for remove

2024-09-06 00:43:28,546 - execution_module.docker_manager -raylib - INFO - -- Looking for remove - found

2024-09-06 00:43:28,546 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat

2024-09-06 00:43:28,840 - execution_module.docker_manager -raylib - INFO - -- Looking for shmat - found

2024-09-06 00:43:29,020 - execution_module.docker_manager -raylib - INFO - -- Audio Backend: miniaudio

2024-09-06 00:43:29,043 - execution_module.docker_manager -raylib - INFO - -- Building raylib static library

2024-09-06 00:43:29,046 - execution_module.docker_manager -raylib - INFO - -- Generated build type: Debug
-- Compiling with the flags:
--   PLATFORM=PLATFORM_DESKTOP
--   GRAPHICS=GRAPHICS_API_OPENGL_33

2024-09-06 00:43:29,104 - execution_module.docker_manager -raylib - INFO - -- Building examples is enabled

2024-09-06 00:43:29,127 - execution_module.docker_manager -raylib - INFO - -- Looking for CLOCK_MONOTONIC

2024-09-06 00:43:29,235 - execution_module.docker_manager -raylib - INFO - -- Looking for CLOCK_MONOTONIC - found

2024-09-06 00:43:29,235 - execution_module.docker_manager -raylib - INFO - -- Looking for QueryPerformanceCounter

2024-09-06 00:43:29,264 - execution_module.docker_manager -raylib - INFO - -- Looking for QueryPerformanceCounter - not found

2024-09-06 00:43:29,265 - execution_module.docker_manager -raylib - INFO - -- Looking for stdatomic.h

2024-09-06 00:43:29,405 - execution_module.docker_manager -raylib - INFO - -- Looking for stdatomic.h - found

2024-09-06 00:43:29,407 - execution_module.docker_manager -raylib - INFO - -- Testing if -std=c11 can be used -- compiles

2024-09-06 00:43:29,450 - execution_module.docker_manager -raylib - INFO - -- Configuring done

2024-09-06 00:43:29,866 - execution_module.docker_manager -raylib - INFO - -- Generating done

2024-09-06 00:43:29,893 - execution_module.docker_manager -raylib - INFO - -- Build files have been written to: /tmp/raylib/build

2024-09-06 00:45:33,888 - execution_module.docker_manager -raylib - INFO -  ---> b7050570eb96

2024-09-06 00:45:33,888 - execution_module.docker_manager -raylib - INFO - Step 15/16 : RUN cmake --build build
2024-09-06 00:45:41,042 - execution_module.docker_manager -raylib - INFO -  ---> Running in a3d87599de56

2024-09-06 00:45:42,041 - execution_module.docker_manager -raylib - INFO - [  1%] Generating xdg-decoration-unstable-v1-client-protocol-code.h

2024-09-06 00:45:42,189 - execution_module.docker_manager -raylib - INFO - [  1%] Generating wayland-client-protocol.h

2024-09-06 00:45:42,193 - execution_module.docker_manager -raylib - INFO - [91m/tmp/raylib/src/external/glfw/deps/wayland/wayland.xml:185: element event: validity error : No declaration for attribute type of element event
[0m
2024-09-06 00:45:42,194 - execution_module.docker_manager -raylib - INFO - [91m*******************************************************
*                                                     *
* WARNING: XML failed validation against built-in DTD *
*                                                     *
*******************************************************
[0m
2024-09-06 00:45:42,203 - execution_module.docker_manager -raylib - INFO - [  1%] Generating wayland-client-protocol-code.h

2024-09-06 00:45:42,207 - execution_module.docker_manager -raylib - INFO - [91m/tmp/raylib/src/external/glfw/deps/wayland/wayland.xml:185: element event: validity error : No declaration for attribute type of element event
[0m
2024-09-06 00:45:42,207 - execution_module.docker_manager -raylib - INFO - [91m*******************************************************
*                                                     *
* WARNING: XML failed validation against built-in DTD *
*                                                     *
*******************************************************
[0m
2024-09-06 00:45:42,215 - execution_module.docker_manager -raylib - INFO - [  1%] Generating viewporter-client-protocol.h

2024-09-06 00:45:42,223 - execution_module.docker_manager -raylib - INFO - [  2%] Generating viewporter-client-protocol-code.h

2024-09-06 00:45:42,233 - execution_module.docker_manager -raylib - INFO - [  2%] Generating xdg-shell-client-protocol.h

2024-09-06 00:45:42,242 - execution_module.docker_manager -raylib - INFO - [  2%] Generating xdg-shell-client-protocol-code.h

2024-09-06 00:45:42,253 - execution_module.docker_manager -raylib - INFO - [  3%] Generating idle-inhibit-unstable-v1-client-protocol.h

2024-09-06 00:45:42,263 - execution_module.docker_manager -raylib - INFO - [  3%] Generating idle-inhibit-unstable-v1-client-protocol-code.h

2024-09-06 00:45:42,271 - execution_module.docker_manager -raylib - INFO - [  3%] Generating pointer-constraints-unstable-v1-client-protocol.h

2024-09-06 00:45:42,280 - execution_module.docker_manager -raylib - INFO - [  3%] Generating pointer-constraints-unstable-v1-client-protocol-code.h

2024-09-06 00:45:42,290 - execution_module.docker_manager -raylib - INFO - [  4%] Generating relative-pointer-unstable-v1-client-protocol.h

2024-09-06 00:45:42,299 - execution_module.docker_manager -raylib - INFO - [  4%] Generating relative-pointer-unstable-v1-client-protocol-code.h

2024-09-06 00:45:42,308 - execution_module.docker_manager -raylib - INFO - [  4%] Generating fractional-scale-v1-client-protocol.h

2024-09-06 00:45:42,316 - execution_module.docker_manager -raylib - INFO - [  5%] Generating fractional-scale-v1-client-protocol-code.h

2024-09-06 00:45:42,371 - execution_module.docker_manager -raylib - INFO - [  5%] Generating xdg-activation-v1-client-protocol.h

2024-09-06 00:45:42,380 - execution_module.docker_manager -raylib - INFO - [  5%] Generating xdg-activation-v1-client-protocol-code.h

2024-09-06 00:45:42,388 - execution_module.docker_manager -raylib - INFO - [  5%] Generating xdg-decoration-unstable-v1-client-protocol.h

2024-09-06 00:45:42,411 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target glfw

2024-09-06 00:45:42,421 - execution_module.docker_manager -raylib - INFO - [  5%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/context.c.o

2024-09-06 00:45:42,924 - execution_module.docker_manager -raylib - INFO - [  5%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/init.c.o

2024-09-06 00:45:43,732 - execution_module.docker_manager -raylib - INFO - [  6%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/input.c.o

2024-09-06 00:45:45,656 - execution_module.docker_manager -raylib - INFO - [  6%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/monitor.c.o

2024-09-06 00:45:47,256 - execution_module.docker_manager -raylib - INFO - [  6%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/platform.c.o

2024-09-06 00:45:48,626 - execution_module.docker_manager -raylib - INFO - [  6%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/vulkan.c.o

2024-09-06 00:45:50,472 - execution_module.docker_manager -raylib - INFO - [  7%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/window.c.o

2024-09-06 00:45:51,327 - execution_module.docker_manager -raylib - INFO - [  7%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/egl_context.c.o

2024-09-06 00:45:52,714 - execution_module.docker_manager -raylib - INFO - [  7%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/osmesa_context.c.o

2024-09-06 00:45:53,906 - execution_module.docker_manager -raylib - INFO - [  8%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/null_init.c.o

2024-09-06 00:45:55,221 - execution_module.docker_manager -raylib - INFO - [  8%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/null_monitor.c.o

2024-09-06 00:45:55,336 - execution_module.docker_manager -raylib - INFO - [  8%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/null_window.c.o

2024-09-06 00:45:55,975 - execution_module.docker_manager -raylib - INFO - [  8%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/null_joystick.c.o

2024-09-06 00:45:56,252 - execution_module.docker_manager -raylib - INFO - [  9%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/posix_module.c.o

2024-09-06 00:45:56,675 - execution_module.docker_manager -raylib - INFO - [  9%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/posix_time.c.o

2024-09-06 00:45:57,131 - execution_module.docker_manager -raylib - INFO - [  9%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/posix_thread.c.o

2024-09-06 00:45:57,259 - execution_module.docker_manager -raylib - INFO - [ 10%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/x11_init.c.o

2024-09-06 00:45:57,904 - execution_module.docker_manager -raylib - INFO - [ 10%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/x11_monitor.c.o

2024-09-06 00:45:58,053 - execution_module.docker_manager -raylib - INFO - [ 10%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/x11_window.c.o

2024-09-06 00:45:58,606 - execution_module.docker_manager -raylib - INFO - [ 10%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/xkb_unicode.c.o

2024-09-06 00:45:58,729 - execution_module.docker_manager -raylib - INFO - [ 11%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/glx_context.c.o

2024-09-06 00:45:58,849 - execution_module.docker_manager -raylib - INFO - [ 11%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/wl_init.c.o

2024-09-06 00:45:59,124 - execution_module.docker_manager -raylib - INFO - [ 11%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/wl_monitor.c.o

2024-09-06 00:45:59,241 - execution_module.docker_manager -raylib - INFO - [ 12%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/wl_window.c.o

2024-09-06 00:45:59,560 - execution_module.docker_manager -raylib - INFO - [ 12%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/linux_joystick.c.o

2024-09-06 00:45:59,679 - execution_module.docker_manager -raylib - INFO - [ 12%] Building C object raylib/external/glfw/src/CMakeFiles/glfw.dir/posix_poll.c.o

2024-09-06 00:45:59,800 - execution_module.docker_manager -raylib - INFO - [ 12%] Built target glfw

2024-09-06 00:46:00,125 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target raylib

2024-09-06 00:46:00,134 - execution_module.docker_manager -raylib - INFO - [ 12%] Building C object raylib/CMakeFiles/raylib.dir/raudio.c.o

2024-09-06 00:46:03,601 - execution_module.docker_manager -raylib - INFO - [ 12%] Building C object raylib/CMakeFiles/raylib.dir/rcore.c.o

2024-09-06 00:46:05,730 - execution_module.docker_manager -raylib - INFO - [ 12%] Building C object raylib/CMakeFiles/raylib.dir/rmodels.c.o

2024-09-06 00:46:07,404 - execution_module.docker_manager -raylib - INFO - [ 13%] Building C object raylib/CMakeFiles/raylib.dir/rshapes.c.o

2024-09-06 00:46:07,695 - execution_module.docker_manager -raylib - INFO - [ 13%] Building C object raylib/CMakeFiles/raylib.dir/rtext.c.o

2024-09-06 00:46:08,096 - execution_module.docker_manager -raylib - INFO - [ 13%] Building C object raylib/CMakeFiles/raylib.dir/rtextures.c.o

2024-09-06 00:46:09,706 - execution_module.docker_manager -raylib - INFO - [ 13%] Building C object raylib/CMakeFiles/raylib.dir/utils.c.o

2024-09-06 00:46:09,798 - execution_module.docker_manager -raylib - INFO - [ 14%] Linking C static library libraylib.a

2024-09-06 00:46:10,941 - execution_module.docker_manager -raylib - INFO - [ 14%] Built target raylib

2024-09-06 00:46:10,950 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_to_image

2024-09-06 00:46:10,958 - execution_module.docker_manager -raylib - INFO - [ 14%] Building C object examples/CMakeFiles/textures_to_image.dir/textures/textures_to_image.c.o

2024-09-06 00:46:10,991 - execution_module.docker_manager -raylib - INFO - [ 14%] Linking C executable textures_to_image

2024-09-06 00:46:11,765 - execution_module.docker_manager -raylib - INFO - [ 14%] Built target textures_to_image

2024-09-06 00:46:11,773 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_sprite_explosion

2024-09-06 00:46:11,780 - execution_module.docker_manager -raylib - INFO - [ 15%] Building C object examples/CMakeFiles/textures_sprite_explosion.dir/textures/textures_sprite_explosion.c.o

2024-09-06 00:46:11,812 - execution_module.docker_manager -raylib - INFO - [ 15%] Linking C executable textures_sprite_explosion

2024-09-06 00:46:11,955 - execution_module.docker_manager -raylib - INFO - [ 15%] Built target textures_sprite_explosion

2024-09-06 00:46:11,972 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_sprite_anim

2024-09-06 00:46:11,984 - execution_module.docker_manager -raylib - INFO - [ 16%] Building C object examples/CMakeFiles/textures_sprite_anim.dir/textures/textures_sprite_anim.c.o

2024-09-06 00:46:12,032 - execution_module.docker_manager -raylib - INFO - [ 16%] Linking C executable textures_sprite_anim

2024-09-06 00:46:12,100 - execution_module.docker_manager -raylib - INFO - [ 16%] Built target textures_sprite_anim

2024-09-06 00:46:12,111 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_raw_data

2024-09-06 00:46:12,122 - execution_module.docker_manager -raylib - INFO - [ 16%] Building C object examples/CMakeFiles/textures_raw_data.dir/textures/textures_raw_data.c.o

2024-09-06 00:46:12,996 - execution_module.docker_manager -raylib - INFO - [ 16%] Linking C executable textures_raw_data

2024-09-06 00:46:13,082 - execution_module.docker_manager -raylib - INFO - [ 16%] Built target textures_raw_data

2024-09-06 00:46:13,093 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_polygon

2024-09-06 00:46:13,106 - execution_module.docker_manager -raylib - INFO - [ 16%] Building C object examples/CMakeFiles/textures_polygon.dir/textures/textures_polygon.c.o

2024-09-06 00:46:14,046 - execution_module.docker_manager -raylib - INFO - [ 17%] Linking C executable textures_polygon

2024-09-06 00:46:14,210 - execution_module.docker_manager -raylib - INFO - [ 17%] Built target textures_polygon

2024-09-06 00:46:14,987 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_particles_blending

2024-09-06 00:46:15,002 - execution_module.docker_manager -raylib - INFO - [ 17%] Building C object examples/CMakeFiles/textures_particles_blending.dir/textures/textures_particles_blending.c.o

2024-09-06 00:46:15,832 - execution_module.docker_manager -raylib - INFO - [ 17%] Linking C executable textures_particles_blending

2024-09-06 00:46:16,438 - execution_module.docker_manager -raylib - INFO - [ 17%] Built target textures_particles_blending

2024-09-06 00:46:16,719 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_npatch_drawing

2024-09-06 00:46:16,727 - execution_module.docker_manager -raylib - INFO - [ 17%] Building C object examples/CMakeFiles/textures_npatch_drawing.dir/textures/textures_npatch_drawing.c.o

2024-09-06 00:46:16,862 - execution_module.docker_manager -raylib - INFO - [ 18%] Linking C executable textures_npatch_drawing

2024-09-06 00:46:17,351 - execution_module.docker_manager -raylib - INFO - [ 18%] Built target textures_npatch_drawing

2024-09-06 00:46:17,507 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_mouse_painting

2024-09-06 00:46:17,523 - execution_module.docker_manager -raylib - INFO - [ 19%] Building C object examples/CMakeFiles/textures_mouse_painting.dir/textures/textures_mouse_painting.c.o

2024-09-06 00:46:17,698 - execution_module.docker_manager -raylib - INFO - [ 19%] Linking C executable textures_mouse_painting

2024-09-06 00:46:17,809 - execution_module.docker_manager -raylib - INFO - [ 19%] Built target textures_mouse_painting

2024-09-06 00:46:17,817 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_logo_raylib

2024-09-06 00:46:17,825 - execution_module.docker_manager -raylib - INFO - [ 19%] Building C object examples/CMakeFiles/textures_logo_raylib.dir/textures/textures_logo_raylib.c.o

2024-09-06 00:46:18,120 - execution_module.docker_manager -raylib - INFO - [ 19%] Linking C executable textures_logo_raylib

2024-09-06 00:46:18,195 - execution_module.docker_manager -raylib - INFO - [ 19%] Built target textures_logo_raylib

2024-09-06 00:46:18,205 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_image_kernel

2024-09-06 00:46:18,212 - execution_module.docker_manager -raylib - INFO - [ 19%] Building C object examples/CMakeFiles/textures_image_kernel.dir/textures/textures_image_kernel.c.o

2024-09-06 00:46:18,304 - execution_module.docker_manager -raylib - INFO - [ 20%] Linking C executable textures_image_kernel

2024-09-06 00:46:18,452 - execution_module.docker_manager -raylib - INFO - [ 20%] Built target textures_image_kernel

2024-09-06 00:46:18,477 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_image_generation

2024-09-06 00:46:18,514 - execution_module.docker_manager -raylib - INFO - [ 21%] Building C object examples/CMakeFiles/textures_image_generation.dir/textures/textures_image_generation.c.o

2024-09-06 00:46:18,711 - execution_module.docker_manager -raylib - INFO - [ 21%] Linking C executable textures_image_generation

2024-09-06 00:46:18,847 - execution_module.docker_manager -raylib - INFO - [ 21%] Built target textures_image_generation

2024-09-06 00:46:18,991 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_image_channel

2024-09-06 00:46:19,090 - execution_module.docker_manager -raylib - INFO - [ 22%] Building C object examples/CMakeFiles/textures_image_channel.dir/textures/textures_image_channel.c.o

2024-09-06 00:46:19,169 - execution_module.docker_manager -raylib - INFO - [ 22%] Linking C executable textures_image_channel

2024-09-06 00:46:19,451 - execution_module.docker_manager -raylib - INFO - [ 22%] Built target textures_image_channel

2024-09-06 00:46:19,592 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_gif_player

2024-09-06 00:46:19,667 - execution_module.docker_manager -raylib - INFO - [ 22%] Building C object examples/CMakeFiles/textures_gif_player.dir/textures/textures_gif_player.c.o

2024-09-06 00:46:19,932 - execution_module.docker_manager -raylib - INFO - [ 22%] Linking C executable textures_gif_player

2024-09-06 00:46:19,997 - execution_module.docker_manager -raylib - INFO - [ 22%] Built target textures_gif_player

2024-09-06 00:46:20,005 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_loading_gltf

2024-09-06 00:46:20,012 - execution_module.docker_manager -raylib - INFO - [ 22%] Building C object examples/CMakeFiles/models_loading_gltf.dir/models/models_loading_gltf.c.o

2024-09-06 00:46:20,047 - execution_module.docker_manager -raylib - INFO - [ 22%] Linking C executable models_loading_gltf

2024-09-06 00:46:20,152 - execution_module.docker_manager -raylib - INFO - [ 22%] Built target models_loading_gltf

2024-09-06 00:46:20,161 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_first_person_maze

2024-09-06 00:46:20,167 - execution_module.docker_manager -raylib - INFO - [ 22%] Building C object examples/CMakeFiles/models_first_person_maze.dir/models/models_first_person_maze.c.o

2024-09-06 00:46:20,206 - execution_module.docker_manager -raylib - INFO - [ 22%] Linking C executable models_first_person_maze

2024-09-06 00:46:20,275 - execution_module.docker_manager -raylib - INFO - [ 22%] Built target models_first_person_maze

2024-09-06 00:46:20,315 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_draw_cube_texture

2024-09-06 00:46:20,322 - execution_module.docker_manager -raylib - INFO - [ 22%] Building C object examples/CMakeFiles/models_draw_cube_texture.dir/models/models_draw_cube_texture.c.o

2024-09-06 00:46:20,415 - execution_module.docker_manager -raylib - INFO - [ 23%] Linking C executable models_draw_cube_texture

2024-09-06 00:46:20,533 - execution_module.docker_manager -raylib - INFO - [ 23%] Built target models_draw_cube_texture

2024-09-06 00:46:20,552 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_cubicmap

2024-09-06 00:46:20,558 - execution_module.docker_manager -raylib - INFO - [ 24%] Building C object examples/CMakeFiles/models_cubicmap.dir/models/models_cubicmap.c.o

2024-09-06 00:46:20,611 - execution_module.docker_manager -raylib - INFO - [ 24%] Linking C executable models_cubicmap

2024-09-06 00:46:20,694 - execution_module.docker_manager -raylib - INFO - [ 24%] Built target models_cubicmap

2024-09-06 00:46:20,703 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target raymath_vector_angle

2024-09-06 00:46:20,710 - execution_module.docker_manager -raylib - INFO - [ 25%] Building C object examples/CMakeFiles/raymath_vector_angle.dir/others/raymath_vector_angle.c.o

2024-09-06 00:46:20,875 - execution_module.docker_manager -raylib - INFO - [ 25%] Linking C executable raymath_vector_angle

2024-09-06 00:46:20,946 - execution_module.docker_manager -raylib - INFO - [ 25%] Built target raymath_vector_angle

2024-09-06 00:46:20,955 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_image_processing

2024-09-06 00:46:20,963 - execution_module.docker_manager -raylib - INFO - [ 25%] Building C object examples/CMakeFiles/textures_image_processing.dir/textures/textures_image_processing.c.o

2024-09-06 00:46:21,085 - execution_module.docker_manager -raylib - INFO - [ 26%] Linking C executable textures_image_processing

2024-09-06 00:46:21,252 - execution_module.docker_manager -raylib - INFO - [ 26%] Built target textures_image_processing

2024-09-06 00:46:21,261 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_basic_shapes

2024-09-06 00:46:21,410 - execution_module.docker_manager -raylib - INFO - [ 27%] Building C object examples/CMakeFiles/shapes_basic_shapes.dir/shapes/shapes_basic_shapes.c.o

2024-09-06 00:46:21,479 - execution_module.docker_manager -raylib - INFO - [ 27%] Linking C executable shapes_basic_shapes

2024-09-06 00:46:21,571 - execution_module.docker_manager -raylib - INFO - [ 27%] Built target shapes_basic_shapes

2024-09-06 00:46:21,579 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_mesh_picking

2024-09-06 00:46:21,586 - execution_module.docker_manager -raylib - INFO - [ 27%] Building C object examples/CMakeFiles/models_mesh_picking.dir/models/models_mesh_picking.c.o

2024-09-06 00:46:21,845 - execution_module.docker_manager -raylib - INFO - [ 28%] Linking C executable models_mesh_picking

2024-09-06 00:46:22,046 - execution_module.docker_manager -raylib - INFO - [ 28%] Built target models_mesh_picking

2024-09-06 00:46:22,069 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_texture_tiling

2024-09-06 00:46:22,077 - execution_module.docker_manager -raylib - INFO - [ 28%] Building C object examples/CMakeFiles/shaders_texture_tiling.dir/shaders/shaders_texture_tiling.c.o

2024-09-06 00:46:22,144 - execution_module.docker_manager -raylib - INFO - [ 28%] Linking C executable shaders_texture_tiling

2024-09-06 00:46:22,483 - execution_module.docker_manager -raylib - INFO - [ 28%] Built target shaders_texture_tiling

2024-09-06 00:46:22,498 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_sprite_button

2024-09-06 00:46:22,508 - execution_module.docker_manager -raylib - INFO - [ 28%] Building C object examples/CMakeFiles/textures_sprite_button.dir/textures/textures_sprite_button.c.o

2024-09-06 00:46:23,050 - execution_module.docker_manager -raylib - INFO - [ 28%] Linking C executable textures_sprite_button

2024-09-06 00:46:23,131 - execution_module.docker_manager -raylib - INFO - [ 28%] Built target textures_sprite_button

2024-09-06 00:46:23,141 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_custom_logging

2024-09-06 00:46:23,149 - execution_module.docker_manager -raylib - INFO - [ 29%] Building C object examples/CMakeFiles/core_custom_logging.dir/core/core_custom_logging.c.o

2024-09-06 00:46:23,495 - execution_module.docker_manager -raylib - INFO - [ 29%] Linking C executable core_custom_logging

2024-09-06 00:46:23,598 - execution_module.docker_manager -raylib - INFO - [ 29%] Built target core_custom_logging

2024-09-06 00:46:23,766 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_window_should_close

2024-09-06 00:46:23,783 - execution_module.docker_manager -raylib - INFO - [ 30%] Building C object examples/CMakeFiles/core_window_should_close.dir/core/core_window_should_close.c.o

2024-09-06 00:46:24,217 - execution_module.docker_manager -raylib - INFO - [ 30%] Linking C executable core_window_should_close

2024-09-06 00:46:24,280 - execution_module.docker_manager -raylib - INFO - [ 30%] Built target core_window_should_close

2024-09-06 00:46:24,717 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_image_rotate

2024-09-06 00:46:24,732 - execution_module.docker_manager -raylib - INFO - [ 30%] Building C object examples/CMakeFiles/textures_image_rotate.dir/textures/textures_image_rotate.c.o

2024-09-06 00:46:24,860 - execution_module.docker_manager -raylib - INFO - [ 30%] Linking C executable textures_image_rotate

2024-09-06 00:46:24,929 - execution_module.docker_manager -raylib - INFO - [ 30%] Built target textures_image_rotate

2024-09-06 00:46:24,938 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_window_letterbox

2024-09-06 00:46:24,945 - execution_module.docker_manager -raylib - INFO - [ 30%] Building C object examples/CMakeFiles/core_window_letterbox.dir/core/core_window_letterbox.c.o

2024-09-06 00:46:25,027 - execution_module.docker_manager -raylib - INFO - [ 30%] Linking C executable core_window_letterbox

2024-09-06 00:46:25,115 - execution_module.docker_manager -raylib - INFO - [ 30%] Built target core_window_letterbox

2024-09-06 00:46:25,122 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_loading_thread

2024-09-06 00:46:25,130 - execution_module.docker_manager -raylib - INFO - [ 30%] Building C object examples/CMakeFiles/core_loading_thread.dir/core/core_loading_thread.c.o

2024-09-06 00:46:25,328 - execution_module.docker_manager -raylib - INFO - [ 31%] Linking C executable core_loading_thread

2024-09-06 00:46:25,439 - execution_module.docker_manager -raylib - INFO - [ 31%] Built target core_loading_thread

2024-09-06 00:46:25,448 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_logo_raylib

2024-09-06 00:46:25,455 - execution_module.docker_manager -raylib - INFO - [ 31%] Building C object examples/CMakeFiles/shapes_logo_raylib.dir/shapes/shapes_logo_raylib.c.o

2024-09-06 00:46:25,526 - execution_module.docker_manager -raylib - INFO - [ 32%] Linking C executable shapes_logo_raylib

2024-09-06 00:46:25,678 - execution_module.docker_manager -raylib - INFO - [ 32%] Built target shapes_logo_raylib

2024-09-06 00:46:25,785 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_rlgl_solar_system

2024-09-06 00:46:25,793 - execution_module.docker_manager -raylib - INFO - [ 33%] Building C object examples/CMakeFiles/models_rlgl_solar_system.dir/models/models_rlgl_solar_system.c.o

2024-09-06 00:46:25,851 - execution_module.docker_manager -raylib - INFO - [ 33%] Linking C executable models_rlgl_solar_system

2024-09-06 00:46:25,974 - execution_module.docker_manager -raylib - INFO - [ 33%] Built target models_rlgl_solar_system

2024-09-06 00:46:26,053 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_custom_frame_control

2024-09-06 00:46:26,060 - execution_module.docker_manager -raylib - INFO - [ 33%] Building C object examples/CMakeFiles/core_custom_frame_control.dir/core/core_custom_frame_control.c.o

2024-09-06 00:46:26,103 - execution_module.docker_manager -raylib - INFO - [ 33%] Linking C executable core_custom_frame_control

2024-09-06 00:46:26,249 - execution_module.docker_manager -raylib - INFO - [ 33%] Built target core_custom_frame_control

2024-09-06 00:46:26,256 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_vr_simulator

2024-09-06 00:46:26,264 - execution_module.docker_manager -raylib - INFO - [ 33%] Building C object examples/CMakeFiles/core_vr_simulator.dir/core/core_vr_simulator.c.o

2024-09-06 00:46:26,334 - execution_module.docker_manager -raylib - INFO - [ 33%] Linking C executable core_vr_simulator

2024-09-06 00:46:26,482 - execution_module.docker_manager -raylib - INFO - [ 33%] Built target core_vr_simulator

2024-09-06 00:46:26,491 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_smooth_pixelperfect

2024-09-06 00:46:26,499 - execution_module.docker_manager -raylib - INFO - [ 34%] Building C object examples/CMakeFiles/core_smooth_pixelperfect.dir/core/core_smooth_pixelperfect.c.o

2024-09-06 00:46:26,539 - execution_module.docker_manager -raylib - INFO - [ 34%] Linking C executable core_smooth_pixelperfect

2024-09-06 00:46:26,639 - execution_module.docker_manager -raylib - INFO - [ 34%] Built target core_smooth_pixelperfect

2024-09-06 00:46:26,648 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_input_mouse_wheel

2024-09-06 00:46:26,656 - execution_module.docker_manager -raylib - INFO - [ 34%] Building C object examples/CMakeFiles/core_input_mouse_wheel.dir/core/core_input_mouse_wheel.c.o

2024-09-06 00:46:26,767 - execution_module.docker_manager -raylib - INFO - [ 35%] Linking C executable core_input_mouse_wheel

2024-09-06 00:46:26,843 - execution_module.docker_manager -raylib - INFO - [ 35%] Built target core_input_mouse_wheel

2024-09-06 00:46:26,852 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target rlgl_compute_shader

2024-09-06 00:46:26,859 - execution_module.docker_manager -raylib - INFO - [ 35%] Building C object examples/CMakeFiles/rlgl_compute_shader.dir/others/rlgl_compute_shader.c.o

2024-09-06 00:46:27,181 - execution_module.docker_manager -raylib - INFO - [ 35%] Linking C executable rlgl_compute_shader

2024-09-06 00:46:27,477 - execution_module.docker_manager -raylib - INFO - [ 35%] Built target rlgl_compute_shader

2024-09-06 00:46:27,570 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_geometric_shapes

2024-09-06 00:46:27,577 - execution_module.docker_manager -raylib - INFO - [ 35%] Building C object examples/CMakeFiles/models_geometric_shapes.dir/models/models_geometric_shapes.c.o

2024-09-06 00:46:28,005 - execution_module.docker_manager -raylib - INFO - [ 36%] Linking C executable models_geometric_shapes

2024-09-06 00:46:28,100 - execution_module.docker_manager -raylib - INFO - [ 36%] Built target models_geometric_shapes

2024-09-06 00:46:28,164 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target audio_stream_effects

2024-09-06 00:46:28,182 - execution_module.docker_manager -raylib - INFO - [ 36%] Building C object examples/CMakeFiles/audio_stream_effects.dir/audio/audio_stream_effects.c.o

2024-09-06 00:46:28,348 - execution_module.docker_manager -raylib - INFO - [ 36%] Linking C executable audio_stream_effects

2024-09-06 00:46:28,416 - execution_module.docker_manager -raylib - INFO - [ 36%] Built target audio_stream_effects

2024-09-06 00:46:28,427 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_deferred_render

2024-09-06 00:46:28,435 - execution_module.docker_manager -raylib - INFO - [ 36%] Building C object examples/CMakeFiles/shaders_deferred_render.dir/shaders/shaders_deferred_render.c.o

2024-09-06 00:46:28,584 - execution_module.docker_manager -raylib - INFO - [ 36%] Linking C executable shaders_deferred_render

2024-09-06 00:46:28,818 - execution_module.docker_manager -raylib - INFO - [ 36%] Built target shaders_deferred_render

2024-09-06 00:46:28,826 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_font_spritefont

2024-09-06 00:46:28,833 - execution_module.docker_manager -raylib - INFO - [ 36%] Building C object examples/CMakeFiles/text_font_spritefont.dir/text/text_font_spritefont.c.o

2024-09-06 00:46:29,024 - execution_module.docker_manager -raylib - INFO - [ 36%] Linking C executable text_font_spritefont

2024-09-06 00:46:29,277 - execution_module.docker_manager -raylib - INFO - [ 36%] Built target text_font_spritefont

2024-09-06 00:46:29,285 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_2d_camera_platformer

2024-09-06 00:46:29,292 - execution_module.docker_manager -raylib - INFO - [ 36%] Building C object examples/CMakeFiles/core_2d_camera_platformer.dir/core/core_2d_camera_platformer.c.o

2024-09-06 00:46:29,692 - execution_module.docker_manager -raylib - INFO - [ 36%] Linking C executable core_2d_camera_platformer

2024-09-06 00:46:29,964 - execution_module.docker_manager -raylib - INFO - [ 36%] Built target core_2d_camera_platformer

2024-09-06 00:46:29,974 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_2d_camera

2024-09-06 00:46:29,983 - execution_module.docker_manager -raylib - INFO - [ 37%] Building C object examples/CMakeFiles/core_2d_camera.dir/core/core_2d_camera.c.o

2024-09-06 00:46:30,215 - execution_module.docker_manager -raylib - INFO - [ 37%] Linking C executable core_2d_camera

2024-09-06 00:46:30,467 - execution_module.docker_manager -raylib - INFO - [ 37%] Built target core_2d_camera

2024-09-06 00:46:30,640 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target audio_sound_multi

2024-09-06 00:46:30,651 - execution_module.docker_manager -raylib - INFO - [ 38%] Building C object examples/CMakeFiles/audio_sound_multi.dir/audio/audio_sound_multi.c.o

2024-09-06 00:46:30,704 - execution_module.docker_manager -raylib - INFO - [ 38%] Linking C executable audio_sound_multi

2024-09-06 00:46:30,955 - execution_module.docker_manager -raylib - INFO - [ 38%] Built target audio_sound_multi

2024-09-06 00:46:31,034 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_following_eyes

2024-09-06 00:46:31,042 - execution_module.docker_manager -raylib - INFO - [ 38%] Building C object examples/CMakeFiles/shapes_following_eyes.dir/shapes/shapes_following_eyes.c.o

2024-09-06 00:46:31,111 - execution_module.docker_manager -raylib - INFO - [ 39%] Linking C executable shapes_following_eyes

2024-09-06 00:46:31,295 - execution_module.docker_manager -raylib - INFO - [ 39%] Built target shapes_following_eyes

2024-09-06 00:46:31,338 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_image_text

2024-09-06 00:46:31,352 - execution_module.docker_manager -raylib - INFO - [ 40%] Building C object examples/CMakeFiles/textures_image_text.dir/textures/textures_image_text.c.o

2024-09-06 00:46:31,517 - execution_module.docker_manager -raylib - INFO - [ 40%] Linking C executable textures_image_text

2024-09-06 00:46:31,624 - execution_module.docker_manager -raylib - INFO - [ 40%] Built target textures_image_text

2024-09-06 00:46:31,656 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_random_sequence

2024-09-06 00:46:31,673 - execution_module.docker_manager -raylib - INFO - [ 40%] Building C object examples/CMakeFiles/core_random_sequence.dir/core/core_random_sequence.c.o

2024-09-06 00:46:31,849 - execution_module.docker_manager -raylib - INFO - [ 40%] Linking C executable core_random_sequence

2024-09-06 00:46:31,946 - execution_module.docker_manager -raylib - INFO - [ 40%] Built target core_random_sequence

2024-09-06 00:46:31,954 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_3d_camera_free

2024-09-06 00:46:31,961 - execution_module.docker_manager -raylib - INFO - [ 41%] Building C object examples/CMakeFiles/core_3d_camera_free.dir/core/core_3d_camera_free.c.o

2024-09-06 00:46:32,009 - execution_module.docker_manager -raylib - INFO - [ 41%] Linking C executable core_3d_camera_free

2024-09-06 00:46:32,244 - execution_module.docker_manager -raylib - INFO - [ 41%] Built target core_3d_camera_free

2024-09-06 00:46:32,491 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_input_gamepad_info

2024-09-06 00:46:32,500 - execution_module.docker_manager -raylib - INFO - [ 41%] Building C object examples/CMakeFiles/core_input_gamepad_info.dir/core/core_input_gamepad_info.c.o

2024-09-06 00:46:32,540 - execution_module.docker_manager -raylib - INFO - [ 42%] Linking C executable core_input_gamepad_info

2024-09-06 00:46:32,968 - execution_module.docker_manager -raylib - INFO - [ 42%] Built target core_input_gamepad_info

2024-09-06 00:46:33,014 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_input_gestures_web

2024-09-06 00:46:33,023 - execution_module.docker_manager -raylib - INFO - [ 43%] Building C object examples/CMakeFiles/core_input_gestures_web.dir/core/core_input_gestures_web.c.o

2024-09-06 00:46:33,094 - execution_module.docker_manager -raylib - INFO - [ 43%] Linking C executable core_input_gestures_web

2024-09-06 00:46:33,177 - execution_module.docker_manager -raylib - INFO - [ 43%] Built target core_input_gestures_web

2024-09-06 00:46:33,184 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_image_drawing

2024-09-06 00:46:33,192 - execution_module.docker_manager -raylib - INFO - [ 43%] Building C object examples/CMakeFiles/textures_image_drawing.dir/textures/textures_image_drawing.c.o

2024-09-06 00:46:33,348 - execution_module.docker_manager -raylib - INFO - [ 43%] Linking C executable textures_image_drawing

2024-09-06 00:46:33,583 - execution_module.docker_manager -raylib - INFO - [ 43%] Built target textures_image_drawing

2024-09-06 00:46:33,591 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_basic_window

2024-09-06 00:46:33,598 - execution_module.docker_manager -raylib - INFO - [ 43%] Building C object examples/CMakeFiles/core_basic_window.dir/core/core_basic_window.c.o

2024-09-06 00:46:33,836 - execution_module.docker_manager -raylib - INFO - [ 43%] Linking C executable core_basic_window

2024-09-06 00:46:34,054 - execution_module.docker_manager -raylib - INFO - [ 43%] Built target core_basic_window

2024-09-06 00:46:34,063 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_input_multitouch

2024-09-06 00:46:34,071 - execution_module.docker_manager -raylib - INFO - [ 43%] Building C object examples/CMakeFiles/core_input_multitouch.dir/core/core_input_multitouch.c.o

2024-09-06 00:46:34,323 - execution_module.docker_manager -raylib - INFO - [ 43%] Linking C executable core_input_multitouch

2024-09-06 00:46:34,612 - execution_module.docker_manager -raylib - INFO - [ 43%] Built target core_input_multitouch

2024-09-06 00:46:34,621 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_world_screen

2024-09-06 00:46:34,630 - execution_module.docker_manager -raylib - INFO - [ 43%] Building C object examples/CMakeFiles/core_world_screen.dir/core/core_world_screen.c.o

2024-09-06 00:46:34,884 - execution_module.docker_manager -raylib - INFO - [ 43%] Linking C executable core_world_screen

2024-09-06 00:46:35,020 - execution_module.docker_manager -raylib - INFO - [ 43%] Built target core_world_screen

2024-09-06 00:46:35,027 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_model_shader

2024-09-06 00:46:35,076 - execution_module.docker_manager -raylib - INFO - [ 44%] Building C object examples/CMakeFiles/shaders_model_shader.dir/shaders/shaders_model_shader.c.o

2024-09-06 00:46:35,117 - execution_module.docker_manager -raylib - INFO - [ 44%] Linking C executable shaders_model_shader

2024-09-06 00:46:35,466 - execution_module.docker_manager -raylib - INFO - [ 44%] Built target shaders_model_shader

2024-09-06 00:46:35,475 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_input_gestures

2024-09-06 00:46:35,483 - execution_module.docker_manager -raylib - INFO - [ 44%] Building C object examples/CMakeFiles/core_input_gestures.dir/core/core_input_gestures.c.o

2024-09-06 00:46:35,594 - execution_module.docker_manager -raylib - INFO - [ 44%] Linking C executable core_input_gestures

2024-09-06 00:46:35,843 - execution_module.docker_manager -raylib - INFO - [ 44%] Built target core_input_gestures

2024-09-06 00:46:35,850 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_loading_vox

2024-09-06 00:46:35,857 - execution_module.docker_manager -raylib - INFO - [ 44%] Building C object examples/CMakeFiles/models_loading_vox.dir/models/models_loading_vox.c.o

2024-09-06 00:46:35,985 - execution_module.docker_manager -raylib - INFO - [ 45%] Linking C executable models_loading_vox

2024-09-06 00:46:36,075 - execution_module.docker_manager -raylib - INFO - [ 45%] Built target models_loading_vox

2024-09-06 00:46:36,084 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target audio_music_stream

2024-09-06 00:46:36,090 - execution_module.docker_manager -raylib - INFO - [ 45%] Building C object examples/CMakeFiles/audio_music_stream.dir/audio/audio_music_stream.c.o

2024-09-06 00:46:36,152 - execution_module.docker_manager -raylib - INFO - [ 45%] Linking C executable audio_music_stream

2024-09-06 00:46:36,271 - execution_module.docker_manager -raylib - INFO - [ 45%] Built target audio_music_stream

2024-09-06 00:46:36,280 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_animation

2024-09-06 00:46:36,287 - execution_module.docker_manager -raylib - INFO - [ 45%] Building C object examples/CMakeFiles/models_animation.dir/models/models_animation.c.o

2024-09-06 00:46:36,321 - execution_module.docker_manager -raylib - INFO - [ 46%] Linking C executable models_animation

2024-09-06 00:46:36,518 - execution_module.docker_manager -raylib - INFO - [ 46%] Built target models_animation

2024-09-06 00:46:36,605 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_basic_window_web

2024-09-06 00:46:36,625 - execution_module.docker_manager -raylib - INFO - [ 47%] Building C object examples/CMakeFiles/core_basic_window_web.dir/core/core_basic_window_web.c.o

2024-09-06 00:46:36,663 - execution_module.docker_manager -raylib - INFO - [ 47%] Linking C executable core_basic_window_web

2024-09-06 00:46:36,875 - execution_module.docker_manager -raylib - INFO - [ 47%] Built target core_basic_window_web

2024-09-06 00:46:36,999 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_storage_values

2024-09-06 00:46:37,011 - execution_module.docker_manager -raylib - INFO - [ 47%] Building C object examples/CMakeFiles/core_storage_values.dir/core/core_storage_values.c.o

2024-09-06 00:46:37,281 - execution_module.docker_manager -raylib - INFO - [ 48%] Linking C executable core_storage_values

2024-09-06 00:46:37,544 - execution_module.docker_manager -raylib - INFO - [ 48%] Built target core_storage_values

2024-09-06 00:46:37,559 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_fog_of_war

2024-09-06 00:46:37,567 - execution_module.docker_manager -raylib - INFO - [ 48%] Building C object examples/CMakeFiles/textures_fog_of_war.dir/textures/textures_fog_of_war.c.o

2024-09-06 00:46:37,618 - execution_module.docker_manager -raylib - INFO - [ 49%] Linking C executable textures_fog_of_war

2024-09-06 00:46:38,085 - execution_module.docker_manager -raylib - INFO - [ 49%] Built target textures_fog_of_war

2024-09-06 00:46:38,095 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_orthographic_projection

2024-09-06 00:46:38,102 - execution_module.docker_manager -raylib - INFO - [ 49%] Building C object examples/CMakeFiles/models_orthographic_projection.dir/models/models_orthographic_projection.c.o

2024-09-06 00:46:38,168 - execution_module.docker_manager -raylib - INFO - [ 49%] Linking C executable models_orthographic_projection

2024-09-06 00:46:38,305 - execution_module.docker_manager -raylib - INFO - [ 49%] Built target models_orthographic_projection

2024-09-06 00:46:38,347 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_scissor_test

2024-09-06 00:46:38,355 - execution_module.docker_manager -raylib - INFO - [ 49%] Building C object examples/CMakeFiles/core_scissor_test.dir/core/core_scissor_test.c.o

2024-09-06 00:46:38,387 - execution_module.docker_manager -raylib - INFO - [ 49%] Linking C executable core_scissor_test

2024-09-06 00:46:38,670 - execution_module.docker_manager -raylib - INFO - [ 49%] Built target core_scissor_test

2024-09-06 00:46:38,678 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_srcrec_dstrec

2024-09-06 00:46:38,686 - execution_module.docker_manager -raylib - INFO - [ 49%] Building C object examples/CMakeFiles/textures_srcrec_dstrec.dir/textures/textures_srcrec_dstrec.c.o

2024-09-06 00:46:38,772 - execution_module.docker_manager -raylib - INFO - [ 50%] Linking C executable textures_srcrec_dstrec

2024-09-06 00:46:38,953 - execution_module.docker_manager -raylib - INFO - [ 50%] Built target textures_srcrec_dstrec

2024-09-06 00:46:38,963 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_draw_rectangle_rounded

2024-09-06 00:46:38,971 - execution_module.docker_manager -raylib - INFO - [ 50%] Building C object examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/shapes/shapes_draw_rectangle_rounded.c.o

2024-09-06 00:46:39,702 - execution_module.docker_manager -raylib - INFO - [ 51%] Linking C executable shapes_draw_rectangle_rounded

2024-09-06 00:46:39,892 - execution_module.docker_manager -raylib - INFO - [ 51%] Built target shapes_draw_rectangle_rounded

2024-09-06 00:46:40,012 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target audio_raw_stream

2024-09-06 00:46:40,022 - execution_module.docker_manager -raylib - INFO - [ 51%] Building C object examples/CMakeFiles/audio_raw_stream.dir/audio/audio_raw_stream.c.o

2024-09-06 00:46:40,179 - execution_module.docker_manager -raylib - INFO - [ 52%] Linking C executable audio_raw_stream

2024-09-06 00:46:40,364 - execution_module.docker_manager -raylib - INFO - [ 52%] Built target audio_raw_stream

2024-09-06 00:46:40,371 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target audio_sound_loading

2024-09-06 00:46:40,378 - execution_module.docker_manager -raylib - INFO - [ 52%] Building C object examples/CMakeFiles/audio_sound_loading.dir/audio/audio_sound_loading.c.o

2024-09-06 00:46:40,514 - execution_module.docker_manager -raylib - INFO - [ 52%] Linking C executable audio_sound_loading

2024-09-06 00:46:40,657 - execution_module.docker_manager -raylib - INFO - [ 52%] Built target audio_sound_loading

2024-09-06 00:46:40,664 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_2d_camera_split_screen

2024-09-06 00:46:40,671 - execution_module.docker_manager -raylib - INFO - [ 52%] Building C object examples/CMakeFiles/core_2d_camera_split_screen.dir/core/core_2d_camera_split_screen.c.o

2024-09-06 00:46:40,755 - execution_module.docker_manager -raylib - INFO - [ 53%] Linking C executable core_2d_camera_split_screen

2024-09-06 00:46:40,843 - execution_module.docker_manager -raylib - INFO - [ 53%] Built target core_2d_camera_split_screen

2024-09-06 00:46:40,868 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_shadowmap

2024-09-06 00:46:40,877 - execution_module.docker_manager -raylib - INFO - [ 53%] Building C object examples/CMakeFiles/shaders_shadowmap.dir/shaders/shaders_shadowmap.c.o

2024-09-06 00:46:41,176 - execution_module.docker_manager -raylib - INFO - [ 54%] Linking C executable shaders_shadowmap

2024-09-06 00:46:41,376 - execution_module.docker_manager -raylib - INFO - [ 54%] Built target shaders_shadowmap

2024-09-06 00:46:41,384 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target audio_mixed_processor

2024-09-06 00:46:41,392 - execution_module.docker_manager -raylib - INFO - [ 54%] Building C object examples/CMakeFiles/audio_mixed_processor.dir/audio/audio_mixed_processor.c.o

2024-09-06 00:46:41,565 - execution_module.docker_manager -raylib - INFO - [ 54%] Linking C executable audio_mixed_processor

2024-09-06 00:46:41,873 - execution_module.docker_manager -raylib - INFO - [ 54%] Built target audio_mixed_processor

2024-09-06 00:46:41,880 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_input_keys

2024-09-06 00:46:41,889 - execution_module.docker_manager -raylib - INFO - [ 54%] Building C object examples/CMakeFiles/core_input_keys.dir/core/core_input_keys.c.o

2024-09-06 00:46:42,036 - execution_module.docker_manager -raylib - INFO - [ 54%] Linking C executable core_input_keys

2024-09-06 00:46:42,177 - execution_module.docker_manager -raylib - INFO - [ 54%] Built target core_input_keys

2024-09-06 00:46:42,186 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_3d_camera_mode

2024-09-06 00:46:42,194 - execution_module.docker_manager -raylib - INFO - [ 54%] Building C object examples/CMakeFiles/core_3d_camera_mode.dir/core/core_3d_camera_mode.c.o

2024-09-06 00:46:42,237 - execution_module.docker_manager -raylib - INFO - [ 54%] Linking C executable core_3d_camera_mode

2024-09-06 00:46:42,382 - execution_module.docker_manager -raylib - INFO - [ 54%] Built target core_3d_camera_mode

2024-09-06 00:46:42,389 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_3d_picking

2024-09-06 00:46:42,395 - execution_module.docker_manager -raylib - INFO - [ 54%] Building C object examples/CMakeFiles/core_3d_picking.dir/core/core_3d_picking.c.o

2024-09-06 00:46:42,553 - execution_module.docker_manager -raylib - INFO - [ 55%] Linking C executable core_3d_picking

2024-09-06 00:46:42,839 - execution_module.docker_manager -raylib - INFO - [ 55%] Built target core_3d_picking

2024-09-06 00:46:42,953 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_basic_screen_manager

2024-09-06 00:46:42,997 - execution_module.docker_manager -raylib - INFO - [ 55%] Building C object examples/CMakeFiles/core_basic_screen_manager.dir/core/core_basic_screen_manager.c.o

2024-09-06 00:46:43,203 - execution_module.docker_manager -raylib - INFO - [ 56%] Linking C executable core_basic_screen_manager

2024-09-06 00:46:43,422 - execution_module.docker_manager -raylib - INFO - [ 56%] Built target core_basic_screen_manager

2024-09-06 00:46:43,433 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_drop_files

2024-09-06 00:46:43,440 - execution_module.docker_manager -raylib - INFO - [ 56%] Building C object examples/CMakeFiles/core_drop_files.dir/core/core_drop_files.c.o

2024-09-06 00:46:43,654 - execution_module.docker_manager -raylib - INFO - [ 57%] Linking C executable core_drop_files

2024-09-06 00:46:43,858 - execution_module.docker_manager -raylib - INFO - [ 57%] Built target core_drop_files

2024-09-06 00:46:43,998 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_loading_m3d

2024-09-06 00:46:44,034 - execution_module.docker_manager -raylib - INFO - [ 58%] Building C object examples/CMakeFiles/models_loading_m3d.dir/models/models_loading_m3d.c.o

2024-09-06 00:46:44,080 - execution_module.docker_manager -raylib - INFO - [ 58%] Linking C executable models_loading_m3d

2024-09-06 00:46:44,437 - execution_module.docker_manager -raylib - INFO - [ 58%] Built target models_loading_m3d

2024-09-06 00:46:44,445 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_yaw_pitch_roll

2024-09-06 00:46:44,453 - execution_module.docker_manager -raylib - INFO - [ 58%] Building C object examples/CMakeFiles/models_yaw_pitch_roll.dir/models/models_yaw_pitch_roll.c.o

2024-09-06 00:46:44,849 - execution_module.docker_manager -raylib - INFO - [ 59%] Linking C executable models_yaw_pitch_roll

2024-09-06 00:46:45,093 - execution_module.docker_manager -raylib - INFO - [ 59%] Built target models_yaw_pitch_roll

2024-09-06 00:46:45,103 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target embedded_files_loading

2024-09-06 00:46:45,111 - execution_module.docker_manager -raylib - INFO - [ 59%] Building C object examples/CMakeFiles/embedded_files_loading.dir/others/embedded_files_loading.c.o

2024-09-06 00:46:45,954 - execution_module.docker_manager -raylib - INFO - [ 60%] Linking C executable embedded_files_loading

2024-09-06 00:46:46,301 - execution_module.docker_manager -raylib - INFO - [ 60%] Built target embedded_files_loading

2024-09-06 00:46:46,307 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_julia_set

2024-09-06 00:46:46,578 - execution_module.docker_manager -raylib - INFO - [ 60%] Building C object examples/CMakeFiles/shaders_julia_set.dir/shaders/shaders_julia_set.c.o

2024-09-06 00:46:46,619 - execution_module.docker_manager -raylib - INFO - [ 60%] Linking C executable shaders_julia_set

2024-09-06 00:46:47,004 - execution_module.docker_manager -raylib - INFO - [ 60%] Built target shaders_julia_set

2024-09-06 00:46:47,012 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_box_collisions

2024-09-06 00:46:47,019 - execution_module.docker_manager -raylib - INFO - [ 60%] Building C object examples/CMakeFiles/models_box_collisions.dir/models/models_box_collisions.c.o

2024-09-06 00:46:47,440 - execution_module.docker_manager -raylib - INFO - [ 60%] Linking C executable models_box_collisions

2024-09-06 00:46:47,522 - execution_module.docker_manager -raylib - INFO - [ 60%] Built target models_box_collisions

2024-09-06 00:46:47,632 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_loading

2024-09-06 00:46:47,641 - execution_module.docker_manager -raylib - INFO - [ 61%] Building C object examples/CMakeFiles/models_loading.dir/models/models_loading.c.o

2024-09-06 00:46:47,977 - execution_module.docker_manager -raylib - INFO - [ 61%] Linking C executable models_loading

2024-09-06 00:46:48,053 - execution_module.docker_manager -raylib - INFO - [ 61%] Built target models_loading

2024-09-06 00:46:48,262 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_textured_curve

2024-09-06 00:46:48,270 - execution_module.docker_manager -raylib - INFO - [ 61%] Building C object examples/CMakeFiles/textures_textured_curve.dir/textures/textures_textured_curve.c.o

2024-09-06 00:46:48,642 - execution_module.docker_manager -raylib - INFO - [ 62%] Linking C executable textures_textured_curve

2024-09-06 00:46:49,100 - execution_module.docker_manager -raylib - INFO - [ 62%] Built target textures_textured_curve

2024-09-06 00:46:49,184 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_bone_socket

2024-09-06 00:46:49,192 - execution_module.docker_manager -raylib - INFO - [ 63%] Building C object examples/CMakeFiles/models_bone_socket.dir/models/models_bone_socket.c.o

2024-09-06 00:46:49,250 - execution_module.docker_manager -raylib - INFO - [ 63%] Linking C executable models_bone_socket

2024-09-06 00:46:49,642 - execution_module.docker_manager -raylib - INFO - [ 63%] Built target models_bone_socket

2024-09-06 00:46:49,651 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_random_values

2024-09-06 00:46:49,658 - execution_module.docker_manager -raylib - INFO - [ 64%] Building C object examples/CMakeFiles/core_random_values.dir/core/core_random_values.c.o

2024-09-06 00:46:50,004 - execution_module.docker_manager -raylib - INFO - [ 64%] Linking C executable core_random_values

2024-09-06 00:46:50,408 - execution_module.docker_manager -raylib - INFO - [ 64%] Built target core_random_values

2024-09-06 00:46:50,417 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_automation_events

2024-09-06 00:46:50,424 - execution_module.docker_manager -raylib - INFO - [ 64%] Building C object examples/CMakeFiles/core_automation_events.dir/core/core_automation_events.c.o

2024-09-06 00:46:50,677 - execution_module.docker_manager -raylib - INFO - [ 64%] Linking C executable core_automation_events

2024-09-06 00:46:50,915 - execution_module.docker_manager -raylib - INFO - [ 64%] Built target core_automation_events

2024-09-06 00:46:50,924 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_input_gamepad

2024-09-06 00:46:50,930 - execution_module.docker_manager -raylib - INFO - [ 64%] Building C object examples/CMakeFiles/core_input_gamepad.dir/core/core_input_gamepad.c.o

2024-09-06 00:46:51,157 - execution_module.docker_manager -raylib - INFO - [ 64%] Linking C executable core_input_gamepad

2024-09-06 00:46:51,444 - execution_module.docker_manager -raylib - INFO - [ 64%] Built target core_input_gamepad

2024-09-06 00:46:51,452 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_fog

2024-09-06 00:46:51,459 - execution_module.docker_manager -raylib - INFO - [ 64%] Building C object examples/CMakeFiles/shaders_fog.dir/shaders/shaders_fog.c.o

2024-09-06 00:46:51,640 - execution_module.docker_manager -raylib - INFO - [ 64%] Linking C executable shaders_fog

2024-09-06 00:46:51,707 - execution_module.docker_manager -raylib - INFO - [ 64%] Built target shaders_fog

2024-09-06 00:46:51,919 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_waving_cubes

2024-09-06 00:46:51,945 - execution_module.docker_manager -raylib - INFO - [ 65%] Building C object examples/CMakeFiles/models_waving_cubes.dir/models/models_waving_cubes.c.o

2024-09-06 00:46:52,121 - execution_module.docker_manager -raylib - INFO - [ 65%] Linking C executable models_waving_cubes

2024-09-06 00:46:52,467 - execution_module.docker_manager -raylib - INFO - [ 65%] Built target models_waving_cubes

2024-09-06 00:46:52,475 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_easings_ball_anim

2024-09-06 00:46:52,483 - execution_module.docker_manager -raylib - INFO - [ 66%] Building C object examples/CMakeFiles/shapes_easings_ball_anim.dir/shapes/shapes_easings_ball_anim.c.o

2024-09-06 00:46:52,555 - execution_module.docker_manager -raylib - INFO - [ 66%] Linking C executable shapes_easings_ball_anim

2024-09-06 00:46:53,055 - execution_module.docker_manager -raylib - INFO - [ 66%] Built target shapes_easings_ball_anim

2024-09-06 00:46:53,267 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_window_flags

2024-09-06 00:46:53,281 - execution_module.docker_manager -raylib - INFO - [ 66%] Building C object examples/CMakeFiles/core_window_flags.dir/core/core_window_flags.c.o

2024-09-06 00:46:53,316 - execution_module.docker_manager -raylib - INFO - [ 67%] Linking C executable core_window_flags

2024-09-06 00:46:53,686 - execution_module.docker_manager -raylib - INFO - [ 67%] Built target core_window_flags

2024-09-06 00:46:53,694 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target easings_testbed

2024-09-06 00:46:54,079 - execution_module.docker_manager -raylib - INFO - [ 68%] Building C object examples/CMakeFiles/easings_testbed.dir/others/easings_testbed.c.o

2024-09-06 00:46:54,256 - execution_module.docker_manager -raylib - INFO - [ 68%] Linking C executable easings_testbed

2024-09-06 00:46:54,595 - execution_module.docker_manager -raylib - INFO - [ 68%] Built target easings_testbed

2024-09-06 00:46:54,606 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target rlgl_standalone

2024-09-06 00:46:54,910 - execution_module.docker_manager -raylib - INFO - [ 69%] Building C object examples/CMakeFiles/rlgl_standalone.dir/others/rlgl_standalone.c.o

2024-09-06 00:46:56,264 - execution_module.docker_manager -raylib - INFO - [ 69%] Linking C executable rlgl_standalone

2024-09-06 00:46:56,335 - execution_module.docker_manager -raylib - INFO - [ 69%] Built target rlgl_standalone

2024-09-06 00:46:56,525 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_mesh_generation

2024-09-06 00:46:56,532 - execution_module.docker_manager -raylib - INFO - [ 69%] Building C object examples/CMakeFiles/models_mesh_generation.dir/models/models_mesh_generation.c.o

2024-09-06 00:46:56,570 - execution_module.docker_manager -raylib - INFO - [ 69%] Linking C executable models_mesh_generation

2024-09-06 00:46:57,113 - execution_module.docker_manager -raylib - INFO - [ 69%] Built target models_mesh_generation

2024-09-06 00:46:57,122 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_basic_lighting

2024-09-06 00:46:57,130 - execution_module.docker_manager -raylib - INFO - [ 69%] Building C object examples/CMakeFiles/shaders_basic_lighting.dir/shaders/shaders_basic_lighting.c.o

2024-09-06 00:46:57,407 - execution_module.docker_manager -raylib - INFO - [ 70%] Linking C executable shaders_basic_lighting

2024-09-06 00:46:57,704 - execution_module.docker_manager -raylib - INFO - [ 70%] Built target shaders_basic_lighting

2024-09-06 00:46:57,716 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_write_depth

2024-09-06 00:46:57,723 - execution_module.docker_manager -raylib - INFO - [ 70%] Building C object examples/CMakeFiles/shaders_write_depth.dir/shaders/shaders_write_depth.c.o

2024-09-06 00:46:58,120 - execution_module.docker_manager -raylib - INFO - [ 70%] Linking C executable shaders_write_depth

2024-09-06 00:46:58,489 - execution_module.docker_manager -raylib - INFO - [ 70%] Built target shaders_write_depth

2024-09-06 00:46:58,497 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_basic_pbr

2024-09-06 00:46:58,504 - execution_module.docker_manager -raylib - INFO - [ 70%] Building C object examples/CMakeFiles/shaders_basic_pbr.dir/shaders/shaders_basic_pbr.c.o

2024-09-06 00:46:58,880 - execution_module.docker_manager -raylib - INFO - [ 70%] Linking C executable shaders_basic_pbr

2024-09-06 00:46:59,082 - execution_module.docker_manager -raylib - INFO - [ 70%] Built target shaders_basic_pbr

2024-09-06 00:46:59,091 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_custom_uniform

2024-09-06 00:46:59,100 - execution_module.docker_manager -raylib - INFO - [ 70%] Building C object examples/CMakeFiles/shaders_custom_uniform.dir/shaders/shaders_custom_uniform.c.o

2024-09-06 00:46:59,374 - execution_module.docker_manager -raylib - INFO - [ 71%] Linking C executable shaders_custom_uniform

2024-09-06 00:46:59,586 - execution_module.docker_manager -raylib - INFO - [ 71%] Built target shaders_custom_uniform

2024-09-06 00:46:59,594 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_eratosthenes

2024-09-06 00:46:59,601 - execution_module.docker_manager -raylib - INFO - [ 72%] Building C object examples/CMakeFiles/shaders_eratosthenes.dir/shaders/shaders_eratosthenes.c.o

2024-09-06 00:46:59,840 - execution_module.docker_manager -raylib - INFO - [ 72%] Linking C executable shaders_eratosthenes

2024-09-06 00:47:00,020 - execution_module.docker_manager -raylib - INFO - [ 72%] Built target shaders_eratosthenes

2024-09-06 00:47:00,028 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_multi_sample2d

2024-09-06 00:47:00,119 - execution_module.docker_manager -raylib - INFO - [ 72%] Building C object examples/CMakeFiles/shaders_multi_sample2d.dir/shaders/shaders_multi_sample2d.c.o

2024-09-06 00:47:00,246 - execution_module.docker_manager -raylib - INFO - [ 72%] Linking C executable shaders_multi_sample2d

2024-09-06 00:47:00,306 - execution_module.docker_manager -raylib - INFO - [ 72%] Built target shaders_multi_sample2d

2024-09-06 00:47:00,362 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_hot_reloading

2024-09-06 00:47:00,374 - execution_module.docker_manager -raylib - INFO - [ 73%] Building C object examples/CMakeFiles/shaders_hot_reloading.dir/shaders/shaders_hot_reloading.c.o

2024-09-06 00:47:00,541 - execution_module.docker_manager -raylib - INFO - [ 73%] Linking C executable shaders_hot_reloading

2024-09-06 00:47:00,750 - execution_module.docker_manager -raylib - INFO - [ 73%] Built target shaders_hot_reloading

2024-09-06 00:47:00,783 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_hybrid_render

2024-09-06 00:47:00,799 - execution_module.docker_manager -raylib - INFO - [ 73%] Building C object examples/CMakeFiles/shaders_hybrid_render.dir/shaders/shaders_hybrid_render.c.o

2024-09-06 00:47:01,091 - execution_module.docker_manager -raylib - INFO - [ 74%] Linking C executable shaders_hybrid_render

2024-09-06 00:47:01,503 - execution_module.docker_manager -raylib - INFO - [ 74%] Built target shaders_hybrid_render

2024-09-06 00:47:01,523 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_heightmap

2024-09-06 00:47:01,665 - execution_module.docker_manager -raylib - INFO - [ 74%] Building C object examples/CMakeFiles/models_heightmap.dir/models/models_heightmap.c.o

2024-09-06 00:47:01,705 - execution_module.docker_manager -raylib - INFO - [ 74%] Linking C executable models_heightmap

2024-09-06 00:47:01,771 - execution_module.docker_manager -raylib - INFO - [ 74%] Built target models_heightmap

2024-09-06 00:47:01,780 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_mesh_instancing

2024-09-06 00:47:01,787 - execution_module.docker_manager -raylib - INFO - [ 74%] Building C object examples/CMakeFiles/shaders_mesh_instancing.dir/shaders/shaders_mesh_instancing.c.o

2024-09-06 00:47:01,848 - execution_module.docker_manager -raylib - INFO - [ 74%] Linking C executable shaders_mesh_instancing

2024-09-06 00:47:02,027 - execution_module.docker_manager -raylib - INFO - [ 74%] Built target shaders_mesh_instancing

2024-09-06 00:47:02,149 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_3d_camera_split_screen

2024-09-06 00:47:02,158 - execution_module.docker_manager -raylib - INFO - [ 75%] Building C object examples/CMakeFiles/core_3d_camera_split_screen.dir/core/core_3d_camera_split_screen.c.o

2024-09-06 00:47:02,266 - execution_module.docker_manager -raylib - INFO - [ 75%] Linking C executable core_3d_camera_split_screen

2024-09-06 00:47:02,359 - execution_module.docker_manager -raylib - INFO - [ 75%] Built target core_3d_camera_split_screen

2024-09-06 00:47:02,368 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_lightmap

2024-09-06 00:47:02,376 - execution_module.docker_manager -raylib - INFO - [ 75%] Building C object examples/CMakeFiles/shaders_lightmap.dir/shaders/shaders_lightmap.c.o

2024-09-06 00:47:02,427 - execution_module.docker_manager -raylib - INFO - [ 76%] Linking C executable shaders_lightmap

2024-09-06 00:47:02,699 - execution_module.docker_manager -raylib - INFO - [ 76%] Built target shaders_lightmap

2024-09-06 00:47:02,711 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_palette_switch

2024-09-06 00:47:02,719 - execution_module.docker_manager -raylib - INFO - [ 77%] Building C object examples/CMakeFiles/shaders_palette_switch.dir/shaders/shaders_palette_switch.c.o

2024-09-06 00:47:02,847 - execution_module.docker_manager -raylib - INFO - [ 77%] Linking C executable shaders_palette_switch

2024-09-06 00:47:03,005 - execution_module.docker_manager -raylib - INFO - [ 77%] Built target shaders_palette_switch

2024-09-06 00:47:03,101 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_postprocessing

2024-09-06 00:47:03,109 - execution_module.docker_manager -raylib - INFO - [ 77%] Building C object examples/CMakeFiles/shaders_postprocessing.dir/shaders/shaders_postprocessing.c.o

2024-09-06 00:47:03,246 - execution_module.docker_manager -raylib - INFO - [ 78%] Linking C executable shaders_postprocessing

2024-09-06 00:47:03,431 - execution_module.docker_manager -raylib - INFO - [ 78%] Built target shaders_postprocessing

2024-09-06 00:47:03,544 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target raylib_opengl_interop

2024-09-06 00:47:03,554 - execution_module.docker_manager -raylib - INFO - [ 78%] Building C object examples/CMakeFiles/raylib_opengl_interop.dir/others/raylib_opengl_interop.c.o

2024-09-06 00:47:03,824 - execution_module.docker_manager -raylib - INFO - [ 78%] Linking C executable raylib_opengl_interop

2024-09-06 00:47:03,965 - execution_module.docker_manager -raylib - INFO - [ 78%] Built target raylib_opengl_interop

2024-09-06 00:47:03,973 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_raylib_fonts

2024-09-06 00:47:03,980 - execution_module.docker_manager -raylib - INFO - [ 78%] Building C object examples/CMakeFiles/text_raylib_fonts.dir/text/text_raylib_fonts.c.o

2024-09-06 00:47:04,110 - execution_module.docker_manager -raylib - INFO - [ 78%] Linking C executable text_raylib_fonts

2024-09-06 00:47:04,237 - execution_module.docker_manager -raylib - INFO - [ 78%] Built target text_raylib_fonts

2024-09-06 00:47:04,246 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_raymarching

2024-09-06 00:47:04,358 - execution_module.docker_manager -raylib - INFO - [ 78%] Building C object examples/CMakeFiles/shaders_raymarching.dir/shaders/shaders_raymarching.c.o

2024-09-06 00:47:04,407 - execution_module.docker_manager -raylib - INFO - [ 78%] Linking C executable shaders_raymarching

2024-09-06 00:47:04,536 - execution_module.docker_manager -raylib - INFO - [ 78%] Built target shaders_raymarching

2024-09-06 00:47:04,648 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_shapes_textures

2024-09-06 00:47:04,657 - execution_module.docker_manager -raylib - INFO - [ 78%] Building C object examples/CMakeFiles/shaders_shapes_textures.dir/shaders/shaders_shapes_textures.c.o

2024-09-06 00:47:04,688 - execution_module.docker_manager -raylib - INFO - [ 78%] Linking C executable shaders_shapes_textures

2024-09-06 00:47:04,942 - execution_module.docker_manager -raylib - INFO - [ 78%] Built target shaders_shapes_textures

2024-09-06 00:47:04,951 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_font_filters

2024-09-06 00:47:04,958 - execution_module.docker_manager -raylib - INFO - [ 78%] Building C object examples/CMakeFiles/text_font_filters.dir/text/text_font_filters.c.o

2024-09-06 00:47:05,107 - execution_module.docker_manager -raylib - INFO - [ 79%] Linking C executable text_font_filters

2024-09-06 00:47:05,364 - execution_module.docker_manager -raylib - INFO - [ 79%] Built target text_font_filters

2024-09-06 00:47:05,374 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_3d_camera_first_person

2024-09-06 00:47:05,382 - execution_module.docker_manager -raylib - INFO - [ 79%] Building C object examples/CMakeFiles/core_3d_camera_first_person.dir/core/core_3d_camera_first_person.c.o

2024-09-06 00:47:05,518 - execution_module.docker_manager -raylib - INFO - [ 79%] Linking C executable core_3d_camera_first_person

2024-09-06 00:47:05,787 - execution_module.docker_manager -raylib - INFO - [ 79%] Built target core_3d_camera_first_person

2024-09-06 00:47:05,795 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_simple_mask

2024-09-06 00:47:05,801 - execution_module.docker_manager -raylib - INFO - [ 80%] Building C object examples/CMakeFiles/shaders_simple_mask.dir/shaders/shaders_simple_mask.c.o

2024-09-06 00:47:05,844 - execution_module.docker_manager -raylib - INFO - [ 80%] Linking C executable shaders_simple_mask

2024-09-06 00:47:05,908 - execution_module.docker_manager -raylib - INFO - [ 80%] Built target shaders_simple_mask

2024-09-06 00:47:05,919 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_top_down_lights

2024-09-06 00:47:05,931 - execution_module.docker_manager -raylib - INFO - [ 81%] Building C object examples/CMakeFiles/shapes_top_down_lights.dir/shapes/shapes_top_down_lights.c.o

2024-09-06 00:47:06,018 - execution_module.docker_manager -raylib - INFO - [ 81%] Linking C executable shapes_top_down_lights

2024-09-06 00:47:06,264 - execution_module.docker_manager -raylib - INFO - [ 81%] Built target shapes_top_down_lights

2024-09-06 00:47:06,272 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_spotlight

2024-09-06 00:47:06,279 - execution_module.docker_manager -raylib - INFO - [ 81%] Building C object examples/CMakeFiles/shaders_spotlight.dir/shaders/shaders_spotlight.c.o

2024-09-06 00:47:06,334 - execution_module.docker_manager -raylib - INFO - [ 81%] Linking C executable shaders_spotlight

2024-09-06 00:47:06,422 - execution_module.docker_manager -raylib - INFO - [ 81%] Built target shaders_spotlight

2024-09-06 00:47:06,438 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_input_mouse

2024-09-06 00:47:06,447 - execution_module.docker_manager -raylib - INFO - [ 82%] Building C object examples/CMakeFiles/core_input_mouse.dir/core/core_input_mouse.c.o

2024-09-06 00:47:06,488 - execution_module.docker_manager -raylib - INFO - [ 82%] Linking C executable core_input_mouse

2024-09-06 00:47:06,581 - execution_module.docker_manager -raylib - INFO - [ 82%] Built target core_input_mouse

2024-09-06 00:47:06,595 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_codepoints_loading

2024-09-06 00:47:06,602 - execution_module.docker_manager -raylib - INFO - [ 82%] Building C object examples/CMakeFiles/text_codepoints_loading.dir/text/text_codepoints_loading.c.o

2024-09-06 00:47:06,654 - execution_module.docker_manager -raylib - INFO - [ 83%] Linking C executable text_codepoints_loading

2024-09-06 00:47:06,746 - execution_module.docker_manager -raylib - INFO - [ 83%] Built target text_codepoints_loading

2024-09-06 00:47:06,759 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_svg_loading

2024-09-06 00:47:06,766 - execution_module.docker_manager -raylib - INFO - [ 83%] Building C object examples/CMakeFiles/textures_svg_loading.dir/textures/textures_svg_loading.c.o

2024-09-06 00:47:06,795 - execution_module.docker_manager -raylib - INFO - [ 83%] Linking C executable textures_svg_loading

2024-09-06 00:47:06,861 - execution_module.docker_manager -raylib - INFO - [ 83%] Built target textures_svg_loading

2024-09-06 00:47:06,870 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_texture_drawing

2024-09-06 00:47:06,877 - execution_module.docker_manager -raylib - INFO - [ 84%] Building C object examples/CMakeFiles/shaders_texture_drawing.dir/shaders/shaders_texture_drawing.c.o

2024-09-06 00:47:06,934 - execution_module.docker_manager -raylib - INFO - [ 84%] Linking C executable shaders_texture_drawing

2024-09-06 00:47:07,103 - execution_module.docker_manager -raylib - INFO - [ 84%] Built target shaders_texture_drawing

2024-09-06 00:47:07,112 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_texture_outline

2024-09-06 00:47:07,119 - execution_module.docker_manager -raylib - INFO - [ 84%] Building C object examples/CMakeFiles/shaders_texture_outline.dir/shaders/shaders_texture_outline.c.o

2024-09-06 00:47:07,412 - execution_module.docker_manager -raylib - INFO - [ 85%] Linking C executable shaders_texture_outline

2024-09-06 00:47:07,622 - execution_module.docker_manager -raylib - INFO - [ 85%] Built target shaders_texture_outline

2024-09-06 00:47:07,638 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_collision_area

2024-09-06 00:47:07,647 - execution_module.docker_manager -raylib - INFO - [ 86%] Building C object examples/CMakeFiles/shapes_collision_area.dir/shapes/shapes_collision_area.c.o

2024-09-06 00:47:07,752 - execution_module.docker_manager -raylib - INFO - [ 86%] Linking C executable shapes_collision_area

2024-09-06 00:47:07,826 - execution_module.docker_manager -raylib - INFO - [ 86%] Built target shapes_collision_area

2024-09-06 00:47:07,834 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shaders_texture_waves

2024-09-06 00:47:07,854 - execution_module.docker_manager -raylib - INFO - [ 86%] Building C object examples/CMakeFiles/shaders_texture_waves.dir/shaders/shaders_texture_waves.c.o

2024-09-06 00:47:07,908 - execution_module.docker_manager -raylib - INFO - [ 87%] Linking C executable shaders_texture_waves

2024-09-06 00:47:08,068 - execution_module.docker_manager -raylib - INFO - [ 87%] Built target shaders_texture_waves

2024-09-06 00:47:08,077 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_easings_box_anim

2024-09-06 00:47:08,085 - execution_module.docker_manager -raylib - INFO - [ 87%] Building C object examples/CMakeFiles/shapes_easings_box_anim.dir/shapes/shapes_easings_box_anim.c.o

2024-09-06 00:47:08,236 - execution_module.docker_manager -raylib - INFO - [ 87%] Linking C executable shapes_easings_box_anim

2024-09-06 00:47:08,426 - execution_module.docker_manager -raylib - INFO - [ 87%] Built target shapes_easings_box_anim

2024-09-06 00:47:08,435 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_bouncing_ball

2024-09-06 00:47:08,443 - execution_module.docker_manager -raylib - INFO - [ 87%] Building C object examples/CMakeFiles/shapes_bouncing_ball.dir/shapes/shapes_bouncing_ball.c.o

2024-09-06 00:47:08,488 - execution_module.docker_manager -raylib - INFO - [ 87%] Linking C executable shapes_bouncing_ball

2024-09-06 00:47:08,795 - execution_module.docker_manager -raylib - INFO - [ 87%] Built target shapes_bouncing_ball

2024-09-06 00:47:08,805 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_draw_3d

2024-09-06 00:47:08,812 - execution_module.docker_manager -raylib - INFO - [ 87%] Building C object examples/CMakeFiles/text_draw_3d.dir/text/text_draw_3d.c.o

2024-09-06 00:47:09,334 - execution_module.docker_manager -raylib - INFO - [ 87%] Linking C executable text_draw_3d

2024-09-06 00:47:09,486 - execution_module.docker_manager -raylib - INFO - [ 87%] Built target text_draw_3d

2024-09-06 00:47:09,497 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_skybox

2024-09-06 00:47:09,504 - execution_module.docker_manager -raylib - INFO - [ 87%] Building C object examples/CMakeFiles/models_skybox.dir/models/models_skybox.c.o

2024-09-06 00:47:09,807 - execution_module.docker_manager -raylib - INFO - [ 87%] Linking C executable models_skybox

2024-09-06 00:47:09,976 - execution_module.docker_manager -raylib - INFO - [ 87%] Built target models_skybox

2024-09-06 00:47:09,984 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_colors_palette

2024-09-06 00:47:09,991 - execution_module.docker_manager -raylib - INFO - [ 87%] Building C object examples/CMakeFiles/shapes_colors_palette.dir/shapes/shapes_colors_palette.c.o

2024-09-06 00:47:10,141 - execution_module.docker_manager -raylib - INFO - [ 88%] Linking C executable shapes_colors_palette

2024-09-06 00:47:10,300 - execution_module.docker_manager -raylib - INFO - [ 88%] Built target shapes_colors_palette

2024-09-06 00:47:10,310 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_draw_circle_sector

2024-09-06 00:47:10,318 - execution_module.docker_manager -raylib - INFO - [ 88%] Building C object examples/CMakeFiles/shapes_draw_circle_sector.dir/shapes/shapes_draw_circle_sector.c.o

2024-09-06 00:47:10,882 - execution_module.docker_manager -raylib - INFO - [ 88%] Linking C executable shapes_draw_circle_sector

2024-09-06 00:47:10,942 - execution_module.docker_manager -raylib - INFO - [ 88%] Built target shapes_draw_circle_sector

2024-09-06 00:47:11,108 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_draw_ring

2024-09-06 00:47:11,117 - execution_module.docker_manager -raylib - INFO - [ 88%] Building C object examples/CMakeFiles/shapes_draw_ring.dir/shapes/shapes_draw_ring.c.o

2024-09-06 00:47:11,760 - execution_module.docker_manager -raylib - INFO - [ 88%] Linking C executable shapes_draw_ring

2024-09-06 00:47:11,906 - execution_module.docker_manager -raylib - INFO - [ 88%] Built target shapes_draw_ring

2024-09-06 00:47:11,989 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_easings_rectangle_array

2024-09-06 00:47:12,004 - execution_module.docker_manager -raylib - INFO - [ 89%] Building C object examples/CMakeFiles/shapes_easings_rectangle_array.dir/shapes/shapes_easings_rectangle_array.c.o

2024-09-06 00:47:12,247 - execution_module.docker_manager -raylib - INFO - [ 89%] Linking C executable shapes_easings_rectangle_array

2024-09-06 00:47:12,321 - execution_module.docker_manager -raylib - INFO - [ 89%] Built target shapes_easings_rectangle_array

2024-09-06 00:47:12,330 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_lines_bezier

2024-09-06 00:47:12,338 - execution_module.docker_manager -raylib - INFO - [ 89%] Building C object examples/CMakeFiles/shapes_lines_bezier.dir/shapes/shapes_lines_bezier.c.o

2024-09-06 00:47:12,371 - execution_module.docker_manager -raylib - INFO - [ 89%] Linking C executable shapes_lines_bezier

2024-09-06 00:47:12,438 - execution_module.docker_manager -raylib - INFO - [ 89%] Built target shapes_lines_bezier

2024-09-06 00:47:12,446 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_logo_raylib_anim

2024-09-06 00:47:12,453 - execution_module.docker_manager -raylib - INFO - [ 89%] Building C object examples/CMakeFiles/shapes_logo_raylib_anim.dir/shapes/shapes_logo_raylib_anim.c.o

2024-09-06 00:47:12,490 - execution_module.docker_manager -raylib - INFO - [ 89%] Linking C executable shapes_logo_raylib_anim

2024-09-06 00:47:12,551 - execution_module.docker_manager -raylib - INFO - [ 89%] Built target shapes_logo_raylib_anim

2024-09-06 00:47:12,599 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target core_2d_camera_mouse_zoom

2024-09-06 00:47:12,606 - execution_module.docker_manager -raylib - INFO - [ 89%] Building C object examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/core/core_2d_camera_mouse_zoom.c.o

2024-09-06 00:47:12,719 - execution_module.docker_manager -raylib - INFO - [ 90%] Linking C executable core_2d_camera_mouse_zoom

2024-09-06 00:47:12,826 - execution_module.docker_manager -raylib - INFO - [ 90%] Built target core_2d_camera_mouse_zoom

2024-09-06 00:47:12,836 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_draw_tiled

2024-09-06 00:47:12,852 - execution_module.docker_manager -raylib - INFO - [ 90%] Building C object examples/CMakeFiles/textures_draw_tiled.dir/textures/textures_draw_tiled.c.o

2024-09-06 00:47:12,956 - execution_module.docker_manager -raylib - INFO - [ 90%] Linking C executable textures_draw_tiled

2024-09-06 00:47:13,268 - execution_module.docker_manager -raylib - INFO - [ 90%] Built target textures_draw_tiled

2024-09-06 00:47:13,280 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_rectangle_scaling

2024-09-06 00:47:13,295 - execution_module.docker_manager -raylib - INFO - [ 91%] Building C object examples/CMakeFiles/shapes_rectangle_scaling.dir/shapes/shapes_rectangle_scaling.c.o

2024-09-06 00:47:13,484 - execution_module.docker_manager -raylib - INFO - [ 91%] Linking C executable shapes_rectangle_scaling

2024-09-06 00:47:13,571 - execution_module.docker_manager -raylib - INFO - [ 91%] Built target shapes_rectangle_scaling

2024-09-06 00:47:13,579 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target models_billboard

2024-09-06 00:47:13,587 - execution_module.docker_manager -raylib - INFO - [ 91%] Building C object examples/CMakeFiles/models_billboard.dir/models/models_billboard.c.o

2024-09-06 00:47:13,628 - execution_module.docker_manager -raylib - INFO - [ 91%] Linking C executable models_billboard

2024-09-06 00:47:13,716 - execution_module.docker_manager -raylib - INFO - [ 91%] Built target models_billboard

2024-09-06 00:47:13,724 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_font_loading

2024-09-06 00:47:13,731 - execution_module.docker_manager -raylib - INFO - [ 91%] Building C object examples/CMakeFiles/text_font_loading.dir/text/text_font_loading.c.o

2024-09-06 00:47:13,785 - execution_module.docker_manager -raylib - INFO - [ 91%] Linking C executable text_font_loading

2024-09-06 00:47:13,862 - execution_module.docker_manager -raylib - INFO - [ 91%] Built target text_font_loading

2024-09-06 00:47:13,871 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_background_scrolling

2024-09-06 00:47:13,877 - execution_module.docker_manager -raylib - INFO - [ 91%] Building C object examples/CMakeFiles/textures_background_scrolling.dir/textures/textures_background_scrolling.c.o

2024-09-06 00:47:13,927 - execution_module.docker_manager -raylib - INFO - [ 91%] Linking C executable textures_background_scrolling

2024-09-06 00:47:13,993 - execution_module.docker_manager -raylib - INFO - [ 91%] Built target textures_background_scrolling

2024-09-06 00:47:14,003 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target shapes_splines_drawing

2024-09-06 00:47:14,011 - execution_module.docker_manager -raylib - INFO - [ 91%] Building C object examples/CMakeFiles/shapes_splines_drawing.dir/shapes/shapes_splines_drawing.c.o

2024-09-06 00:47:14,350 - execution_module.docker_manager -raylib - INFO - [ 91%] Linking C executable shapes_splines_drawing

2024-09-06 00:47:14,423 - execution_module.docker_manager -raylib - INFO - [ 91%] Built target shapes_splines_drawing

2024-09-06 00:47:14,432 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_font_sdf

2024-09-06 00:47:14,440 - execution_module.docker_manager -raylib - INFO - [ 92%] Building C object examples/CMakeFiles/text_font_sdf.dir/text/text_font_sdf.c.o

2024-09-06 00:47:14,494 - execution_module.docker_manager -raylib - INFO - [ 92%] Linking C executable text_font_sdf

2024-09-06 00:47:14,650 - execution_module.docker_manager -raylib - INFO - [ 92%] Built target text_font_sdf

2024-09-06 00:47:14,657 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target audio_module_playing

2024-09-06 00:47:14,665 - execution_module.docker_manager -raylib - INFO - [ 92%] Building C object examples/CMakeFiles/audio_module_playing.dir/audio/audio_module_playing.c.o

2024-09-06 00:47:14,715 - execution_module.docker_manager -raylib - INFO - [ 93%] Linking C executable audio_module_playing

2024-09-06 00:47:14,867 - execution_module.docker_manager -raylib - INFO - [ 93%] Built target audio_module_playing

2024-09-06 00:47:14,882 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_format_text

2024-09-06 00:47:14,890 - execution_module.docker_manager -raylib - INFO - [ 94%] Building C object examples/CMakeFiles/text_format_text.dir/text/text_format_text.c.o

2024-09-06 00:47:14,981 - execution_module.docker_manager -raylib - INFO - [ 94%] Linking C executable text_format_text

2024-09-06 00:47:15,100 - execution_module.docker_manager -raylib - INFO - [ 94%] Built target text_format_text

2024-09-06 00:47:15,107 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_input_box

2024-09-06 00:47:15,137 - execution_module.docker_manager -raylib - INFO - [ 94%] Building C object examples/CMakeFiles/text_input_box.dir/text/text_input_box.c.o

2024-09-06 00:47:15,197 - execution_module.docker_manager -raylib - INFO - [ 95%] Linking C executable text_input_box

2024-09-06 00:47:15,338 - execution_module.docker_manager -raylib - INFO - [ 95%] Built target text_input_box

2024-09-06 00:47:15,346 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_rectangle_bounds

2024-09-06 00:47:15,354 - execution_module.docker_manager -raylib - INFO - [ 95%] Building C object examples/CMakeFiles/text_rectangle_bounds.dir/text/text_rectangle_bounds.c.o

2024-09-06 00:47:15,604 - execution_module.docker_manager -raylib - INFO - [ 96%] Linking C executable text_rectangle_bounds

2024-09-06 00:47:15,925 - execution_module.docker_manager -raylib - INFO - [ 96%] Built target text_rectangle_bounds

2024-09-06 00:47:15,933 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_unicode

2024-09-06 00:47:16,007 - execution_module.docker_manager -raylib - INFO - [ 96%] Building C object examples/CMakeFiles/text_unicode.dir/text/text_unicode.c.o

2024-09-06 00:47:16,075 - execution_module.docker_manager -raylib - INFO - [ 96%] Linking C executable text_unicode

2024-09-06 00:47:16,225 - execution_module.docker_manager -raylib - INFO - [ 96%] Built target text_unicode

2024-09-06 00:47:16,234 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target text_writing_anim

2024-09-06 00:47:16,240 - execution_module.docker_manager -raylib - INFO - [ 97%] Building C object examples/CMakeFiles/text_writing_anim.dir/text/text_writing_anim.c.o

2024-09-06 00:47:16,424 - execution_module.docker_manager -raylib - INFO - [ 97%] Linking C executable text_writing_anim

2024-09-06 00:47:16,766 - execution_module.docker_manager -raylib - INFO - [ 97%] Built target text_writing_anim

2024-09-06 00:47:16,776 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_image_loading

2024-09-06 00:47:16,783 - execution_module.docker_manager -raylib - INFO - [ 97%] Building C object examples/CMakeFiles/textures_image_loading.dir/textures/textures_image_loading.c.o

2024-09-06 00:47:16,849 - execution_module.docker_manager -raylib - INFO - [ 97%] Linking C executable textures_image_loading

2024-09-06 00:47:16,987 - execution_module.docker_manager -raylib - INFO - [ 97%] Built target textures_image_loading

2024-09-06 00:47:16,994 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_blend_modes

2024-09-06 00:47:17,002 - execution_module.docker_manager -raylib - INFO - [ 98%] Building C object examples/CMakeFiles/textures_blend_modes.dir/textures/textures_blend_modes.c.o

2024-09-06 00:47:17,100 - execution_module.docker_manager -raylib - INFO - [ 98%] Linking C executable textures_blend_modes

2024-09-06 00:47:17,227 - execution_module.docker_manager -raylib - INFO - [ 98%] Built target textures_blend_modes

2024-09-06 00:47:17,234 - execution_module.docker_manager -raylib - INFO - Scanning dependencies of target textures_bunnymark

2024-09-06 00:47:17,241 - execution_module.docker_manager -raylib - INFO - [ 98%] Building C object examples/CMakeFiles/textures_bunnymark.dir/textures/textures_bunnymark.c.o

2024-09-06 00:47:17,334 - execution_module.docker_manager -raylib - INFO - [100%] Linking C executable textures_bunnymark

2024-09-06 00:47:17,437 - execution_module.docker_manager -raylib - INFO - [100%] Built target textures_bunnymark

2024-09-06 00:51:31,328 - execution_module.docker_manager -raylib - INFO -  ---> a6da38eb6150

2024-09-06 00:51:31,329 - execution_module.docker_manager -raylib - INFO - Step 16/16 : RUN rm -rf /tmp/raylib
2024-09-06 00:51:47,503 - execution_module.docker_manager -raylib - INFO -  ---> Running in d2bca1fa9059

2024-09-06 00:53:30,960 - execution_module.docker_manager -raylib - INFO -  ---> d7dfccde5c1f

2024-09-06 00:53:30,975 - execution_module.docker_manager -raylib - INFO - Successfully built d7dfccde5c1f

2024-09-06 00:53:32,510 - cxxcrafter -raylib - INFO - Execution Module Finishes
2024-09-06 00:53:32,518 - cxxcrafter -raylib - INFO - raylib is good!
