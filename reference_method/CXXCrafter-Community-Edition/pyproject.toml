[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "cxxcrafter"
version = "1.0.0"
description = "An llm agent for C/C++ project building"
authors = [
  { name = "<PERSON><PERSON>", email = "<EMAIL>" },
	{ name = "<PERSON><PERSON>", email = "<EMAIL>" }
]
readme = "README.md"
license = { file = "LICENSE" }
requires-python = ">=3.9"
dependencies = [
    "openai",
    "tiktoken",
    "docker",
    "socksio",
    "json5",
    "gitpython",
    "beautifulsoup4"
]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent"
]

[tool.setuptools.packages.find]
where = ["src"]


