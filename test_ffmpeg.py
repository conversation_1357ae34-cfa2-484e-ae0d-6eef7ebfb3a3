#!/usr/bin/env python3
# test_ffmpeg.py - FFmpeg编译测试脚本（严格按照方案执行）
import os
import sys
import json
import logging
import time
from main import AutoCompileSystem

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ffmpeg_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

def test_ffmpeg_compilation():
    """
    严格按照方案测试FFmpeg项目编译
    完整流程：项目下载 -> 副本创建 -> 项目分析 -> 依赖安装 -> 编译执行 -> 错误处理 -> 产物收集
    """
    print("=" * 80)
    print("AutoCompile System - FFmpeg Compilation Test")
    print("严格按照自动化编译方案总览执行")
    print("=" * 80)
    
    # 检查Docker环境
    try:
        import docker
        client = docker.from_env()
        client.ping()
        logging.info("✅ Docker environment is ready")
        
        # 检查可用的Ubuntu镜像
        images = client.images.list()
        ubuntu_images = [img for img in images if any('ubuntu' in tag.lower() for tag in img.tags)]
        if not ubuntu_images:
            logging.error("❌ No Ubuntu Docker images found")
            logging.error("Please ensure Ubuntu Docker images are available")
            return False
        else:
            logging.info(f"✅ Found {len(ubuntu_images)} Ubuntu Docker images")
            # 显示可用的镜像
            for img in ubuntu_images[:3]:  # 显示前3个
                logging.info(f"   - {img.tags[0] if img.tags else 'unnamed'}")
            
    except Exception as e:
        logging.error(f"❌ Docker environment check failed: {str(e)}")
        return False
    
    # 初始化自动化编译系统
    dataset_path = "./ffmpeg_test_output"
    system = AutoCompileSystem(dataset_path)
    
    # FFmpeg项目信息
    ffmpeg_url = "https://github.com/FFmpeg/FFmpeg.git"
    project_name = "FFmpeg_FFmpeg"
    
    logging.info(f"开始FFmpeg项目编译测试")
    logging.info(f"项目URL: {ffmpeg_url}")
    logging.info(f"输出目录: {dataset_path}")
    
    start_time = time.time()
    
    try:
        # 执行完整的编译流程
        logging.info("=" * 60)
        logging.info("阶段1: 项目下载和副本创建")
        logging.info("=" * 60)
        
        result = system.compile_single_project(ffmpeg_url)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 输出详细结果
        logging.info("=" * 60)
        logging.info("FFmpeg编译测试结果")
        logging.info("=" * 60)
        logging.info(f"编译耗时: {duration:.2f} 秒")
        logging.info(f"编译状态: {result.get('status', 'UNKNOWN')}")
        
        if result.get('error'):
            logging.error(f"错误信息: {result['error']}")
        
        if result.get('artifacts'):
            logging.info(f"编译产物: {result['artifacts']}")
        
        # 保存详细结果到文件
        result_file = os.path.join(dataset_path, "ffmpeg_test_result.json")
        os.makedirs(dataset_path, exist_ok=True)
        
        detailed_result = {
            "project_name": project_name,
            "project_url": ffmpeg_url,
            "test_duration": duration,
            "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "compilation_result": result,
            "system_info": {
                "python_version": sys.version,
                "platform": sys.platform
            }
        }
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_result, f, indent=2, ensure_ascii=False)
        
        logging.info(f"详细结果已保存到: {result_file}")
        
        # 判断测试是否成功
        if result.get('status') == 'COMPILATION-SUCCESS':
            logging.info("🎉 FFmpeg编译测试成功！")
            return True
        else:
            logging.error("❌ FFmpeg编译测试失败")
            return False
            
    except Exception as e:
        logging.error(f"❌ FFmpeg编译测试异常: {str(e)}")
        logging.exception("详细异常信息:")
        return False

def check_system_requirements():
    """检查系统要求"""
    logging.info("检查系统要求...")
    
    requirements_met = True
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        logging.error("❌ Python版本需要3.8或更高")
        requirements_met = False
    else:
        logging.info(f"✅ Python版本: {sys.version}")
    
    # 检查必要的Python包
    required_packages = [
        'langchain', 'langchain_openai', 'paramiko',
        'requests', 'bs4', 'docker'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            logging.info(f"✅ {package} 已安装")
        except ImportError:
            logging.error(f"❌ {package} 未安装")
            requirements_met = False
    
    # 检查ccscanner
    try:
        import subprocess
        result = subprocess.run(['ccscanner_print', '--version'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0 or 'ccscanner' in result.stdout.lower() or 'ccscanner' in result.stderr.lower():
            logging.info("✅ ccscanner 工具可用")
        else:
            logging.warning("⚠️ ccscanner 工具可能不可用，但继续测试")
    except Exception as e:
        logging.warning(f"⚠️ ccscanner 检查失败，但继续测试: {str(e)}")
    
    return requirements_met

def main():
    """主函数"""
    print("FFmpeg编译测试 - 严格按照自动化编译方案执行")
    print("=" * 80)
    
    # 检查系统要求
    if not check_system_requirements():
        logging.error("系统要求检查失败，请先满足所有要求")
        sys.exit(1)
    
    # 等待用户确认
    print("\n即将开始FFmpeg编译测试，这可能需要较长时间...")
    print("测试将严格按照以下流程执行：")
    print("1. 项目下载和副本创建")
    print("2. Docker容器创建和SSH连接")
    print("3. 项目分析（ccscanner + 文档分析）")
    print("4. 依赖安装（系统级 -> 语言级 -> 源码编译）")
    print("5. 编译执行和实时监控")
    print("6. 错误处理（主控智能体 + 错误处理智能体）")
    print("7. 编译产物识别和保存")
    print()
    
    user_input = input("是否继续？(y/N): ").strip().lower()
    if user_input != 'y':
        print("测试已取消")
        sys.exit(0)
    
    # 执行FFmpeg编译测试
    success = test_ffmpeg_compilation()
    
    if success:
        print("\n🎉 FFmpeg编译测试完成！")
        print("请查看输出目录中的详细结果和日志文件")
        sys.exit(0)
    else:
        print("\n❌ FFmpeg编译测试失败")
        print("请查看日志文件了解详细错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
