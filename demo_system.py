#!/usr/bin/env python3
# demo_system.py - AutoCompile系统功能演示脚本
import os
import sys
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def demo_line_ending_fixer():
    """演示行尾符修复功能"""
    print("🔧 演示：行尾符修复功能")
    print("-" * 50)
    
    try:
        from tools import LineEndingFixer
        
        # 创建有问题的脚本文件
        test_script = "./demo_script.sh"
        with open(test_script, 'wb') as f:
            f.write(b"#!/bin/bash\r\necho 'Hello World'\r\nls -la\r\n")
        
        print(f"✅ 创建了包含CRLF行尾符的测试脚本: {test_script}")
        
        fixer = LineEndingFixer()
        
        # 检测行尾符
        detected = fixer.detect_line_endings(test_script)
        print(f"🔍 检测到的行尾符类型: {detected}")
        
        # 修复行尾符
        result = fixer.fix_line_endings(test_script, 'LF')
        print(f"🔧 修复结果: {result['message']}")
        
        # 验证修复
        final_detected = fixer.detect_line_endings(test_script)
        print(f"✅ 修复后的行尾符类型: {final_detected}")
        
        # 清理
        os.unlink(test_script)
        print("🧹 清理完成")
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")

def demo_error_analyzer():
    """演示错误分析功能"""
    print("\n🧠 演示：智能错误分析功能")
    print("-" * 50)
    
    try:
        from ErrorSolver import ErrorSolver
        
        solver = ErrorSolver("demo_project")
        
        # 演示不同类型的错误分析
        test_errors = [
            {
                "error": "fatal error: stdio.h: No such file or directory",
                "description": "缺少头文件"
            },
            {
                "error": "undefined reference to `main'",
                "description": "链接错误"
            },
            {
                "error": "/bin/sh^M: bad interpreter: No such file or directory",
                "description": "行尾符问题"
            },
            {
                "error": "make: command not found",
                "description": "环境问题"
            },
            {
                "error": "configure: error: C compiler cannot create executables",
                "description": "配置错误"
            }
        ]
        
        for i, test_case in enumerate(test_errors, 1):
            print(f"\n📋 测试案例 {i}: {test_case['description']}")
            print(f"   错误信息: {test_case['error']}")
            
            result = solver.analyze_error_pattern(test_case['error'])
            analysis = json.loads(result)
            
            print(f"   🎯 识别类别: {analysis.get('primary_category', 'unknown')}")
            print(f"   📊 置信度: {analysis.get('confidence', 0.0):.2f}")
            
            key_terms = analysis.get('key_terms', [])
            if key_terms:
                print(f"   🔑 关键术语: {[term['term'] for term in key_terms[:3]]}")
            
            solution_keywords = analysis.get('solution_keywords', [])
            if solution_keywords:
                print(f"   💡 解决方案关键词: {solution_keywords[:3]}")
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")

def demo_artifact_detection():
    """演示编译产物检测功能"""
    print("\n📦 演示：编译产物检测功能")
    print("-" * 50)
    
    try:
        from tools import InteractiveDockerShell
        
        # 创建模拟的编译产物
        test_dir = "./demo_artifacts"
        os.makedirs(test_dir, exist_ok=True)
        
        # 创建不同类型的文件
        artifacts = [
            ("hello", "executable", b"#!/bin/bash\necho 'Hello'"),
            ("libtest.so", "shared_library", b"fake shared library"),
            ("libstatic.a", "static_library", b"fake static library"),
            ("main.o", "object_file", b"fake object file"),
            ("test.c", "source_file", b"#include <stdio.h>\nint main(){return 0;}"),
            ("README.md", "documentation", b"# Test Project")
        ]
        
        for filename, file_type, content in artifacts:
            filepath = os.path.join(test_dir, filename)
            with open(filepath, 'wb') as f:
                f.write(content)
            
            # 设置可执行权限
            if file_type == "executable":
                os.chmod(filepath, 0o755)
        
        print(f"✅ 创建了模拟编译产物目录: {test_dir}")
        
        # 演示产物检测（简化版，不使用Docker）
        from tools import InteractiveDockerShell
        shell = InteractiveDockerShell.__new__(InteractiveDockerShell)
        
        # 模拟检测逻辑
        detected_artifacts = []
        for filename, expected_type, _ in artifacts:
            filepath = os.path.join(test_dir, filename)
            if shell._is_likely_artifact(filename):
                is_executable = os.access(filepath, os.X_OK)
                artifact_type = shell._classify_artifact(filename, is_executable)
                detected_artifacts.append({
                    "path": filename,
                    "type": artifact_type,
                    "executable": is_executable
                })
        
        print(f"🔍 检测到 {len(detected_artifacts)} 个编译产物:")
        for artifact in detected_artifacts:
            print(f"   📄 {artifact['path']} -> {artifact['type']} {'(可执行)' if artifact['executable'] else ''}")
        
        # 清理
        import shutil
        shutil.rmtree(test_dir)
        print("🧹 清理完成")
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")

def demo_system_integration():
    """演示系统集成功能"""
    print("\n🔗 演示：系统集成功能")
    print("-" * 50)
    
    try:
        # 创建一个简单的C项目
        project_dir = "./demo_project"
        os.makedirs(project_dir, exist_ok=True)
        
        # 创建源文件（故意使用CRLF行尾符）
        with open(os.path.join(project_dir, "main.c"), 'wb') as f:
            f.write(b"#include <stdio.h>\r\n\r\nint main() {\r\n    printf(\"Hello, AutoCompile!\\n\");\r\n    return 0;\r\n}\r\n")
        
        # 创建Makefile（也使用CRLF）
        with open(os.path.join(project_dir, "Makefile"), 'wb') as f:
            f.write(b"CC=gcc\r\nCFLAGS=-Wall -g\r\n\r\nhello: main.c\r\n\t$(CC) $(CFLAGS) -o hello main.c\r\n\r\nclean:\r\n\trm -f hello\r\n")
        
        print(f"✅ 创建了演示项目: {project_dir}")
        print("📋 项目包含:")
        print("   - main.c (C源文件，CRLF行尾符)")
        print("   - Makefile (构建文件，CRLF行尾符)")
        
        # 演示行尾符检测和修复
        from tools import LineEndingFixer
        fixer = LineEndingFixer()
        
        print("\n🔧 执行行尾符修复...")
        fix_result = fixer.fix_directory_line_endings(
            project_dir, 
            ['*.c', '*.h', 'Makefile*'], 
            'LF'
        )
        
        print(f"📊 修复统计:")
        print(f"   - 总文件数: {fix_result['total_files']}")
        print(f"   - 修复文件数: {fix_result['fixed_files']}")
        print(f"   - 跳过文件数: {fix_result['skipped_files']}")
        print(f"   - 错误文件数: {fix_result['error_files']}")
        
        # 演示错误分析（模拟编译错误）
        print("\n🧠 模拟编译错误分析...")
        from ErrorSolver import ErrorSolver
        solver = ErrorSolver("demo_project")
        
        mock_error = "gcc: command not found"
        analysis_result = solver.analyze_error_pattern(mock_error)
        analysis = json.loads(analysis_result)
        
        print(f"🎯 错误分析结果:")
        print(f"   - 错误类别: {analysis.get('primary_category')}")
        print(f"   - 置信度: {analysis.get('confidence', 0.0):.2f}")
        print(f"   - 建议解决方案关键词: {analysis.get('solution_keywords', [])[:3]}")
        
        # 清理
        import shutil
        shutil.rmtree(project_dir)
        print("\n🧹 清理完成")
        
        print("\n🎉 系统集成演示完成！")
        print("💡 这展示了AutoCompile系统如何:")
        print("   1. 自动检测和修复行尾符问题")
        print("   2. 智能分析编译错误")
        print("   3. 提供针对性的解决方案建议")
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")

def main():
    """主演示函数"""
    print("🚀 AutoCompile System 功能演示")
    print("=" * 60)
    print("这个演示将展示系统的核心功能:")
    print("1. 行尾符修复 (LineEndingFixer)")
    print("2. 智能错误分析 (ErrorSolver)")
    print("3. 编译产物检测 (Artifact Detection)")
    print("4. 系统集成演示")
    print("=" * 60)
    
    try:
        # 演示各个功能模块
        demo_line_ending_fixer()
        demo_error_analyzer()
        demo_artifact_detection()
        demo_system_integration()
        
        print("\n" + "=" * 60)
        print("🎊 所有演示完成！")
        print("✨ AutoCompile系统已经准备好处理真实的编译任务！")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
