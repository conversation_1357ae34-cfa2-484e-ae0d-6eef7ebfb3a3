[["compile_project", "FFmpeg_FFmpeg", "Master agent compilation failed: MasterAgent._build_tools.<locals>.detect_artifacts() takes 0 positional arguments but 1 was given"], ["analyze_comprehensive", "FFmpeg_FFmpeg", "Project analysis failed: ProjectAnalyzer.analyze_comprehensive.<locals>.<lambda>() missing 1 required positional argument: 'doc_deps'"], ["solve", "FFmpeg_FFmpeg", "Error solving failed: ErrorSolver.solve.<locals>.<lambda>() missing 1 required positional argument: 'error_keywords'"], ["cd /work && pwd", "root@c0mpi1er-c0nta1ner:/work# /work\n\nroot@c0mpi1er-c0nta1ner:/work# ", 0.36548852920532227], ["./configure", "root@c0mpi1er-c0nta1ner:/work# nasm not found or too old. Please install/update nasm or use --disable-x86asm for a build without hand-optimized assembly.\n\nIf you think configure made a mistake, make sure you are using the latest\nversion from Git.  If the latest version fails, report the problem to the\n<EMAIL> mailing list or IRC #ffmpeg on irc.libera.chat.\nInclude the log file \"ffbuild/config.log\" produced by configure as this will help\nsolve the problem.\n\nroot@c0mpi1er-c0nta1ner:/work# ", 1.5941126346588135], ["apt-get update && apt-get install -y nasm -y", "root@c0mpi1er-c0nta1ner:/work# Reading package lists...\n\nE: List directory /var/lib/apt/lists/partial is missing. - Acquire (13: Permission denied)\n\nroot@c0mpi1er-c0nta1ner:/work# ", 0.3805704116821289], ["sudo apt-get update && sudo apt-get install -y nasm -y", "root@c0mpi1er-c0nta1ner:/work# Get:1 https://cli.github.com/packages stable InRelease [3917 B]\nGet:2 http://archive.ubuntu.com/ubuntu jammy InRelease [270 kB]\nGet:3 https://cli.github.com/packages stable/main amd64 Packages [346 B]\nGet:4 http://security.ubuntu.com/ubuntu jammy-security InRelease [129 kB]\nGet:5 https://ppa.launchpadcontent.net/git-core/ppa/ubuntu jammy InRelease [24.6 kB]\nGet:6 http://archive.ubuntu.com/ubuntu jammy-updates InRelease [128 kB]\nGet:7 http://archive.ubuntu.com/ubuntu jammy-backports InRelease [127 kB]\nGet:8 http://security.ubuntu.com/ubuntu jammy-security/universe amd64 Packages [1270 kB]\nGet:9 https://ppa.launchpadcontent.net/git-core/ppa/ubuntu jammy/main amd64 Packages [2966 B]\nGet:10 http://archive.ubuntu.com/ubuntu jammy/universe amd64 Packages [17.5 MB]\nGet:11 http://security.ubuntu.com/ubuntu jammy-security/restricted amd64 Packages [5103 kB]\nGet:12 http://security.ubuntu.com/ubuntu jammy-security/multiverse amd64 Packages [48.5 kB]\nGet:13 http://security.ubuntu.com/ubuntu jammy-security/main amd64 Packages [3207 kB]\nGet:14 http://archive.ubuntu.com/ubuntu jammy/main amd64 Packages [1792 kB]\nGet:15 http://archive.ubuntu.com/ubuntu jammy/multiverse amd64 Packages [266 kB]\nGet:16 http://archive.ubuntu.com/ubuntu jammy/restricted amd64 Packages [164 kB]\nGet:17 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 Packages [3518 kB]\nGet:18 http://archive.ubuntu.com/ubuntu jammy-updates/universe amd64 Packages [1575 kB]\nGet:19 http://archive.ubuntu.com/ubuntu jammy-updates/restricted amd64 Packages [5290 kB]\nGet:20 http://archive.ubuntu.com/ubuntu jammy-updates/multiverse amd64 Packages [75.9 kB]\nGet:21 http://archive.ubuntu.com/ubuntu jammy-backports/main amd64 Packages [83.2 kB]\nGet:22 http://archive.ubuntu.com/ubuntu jammy-backports/universe amd64 Packages [35.2 kB]\nFetched 40.6 MB in 21s (1978 kB/s)\nReading package lists...\nReading package lists...\nBuilding dependency tree...\nReading state information...\nThe following NEW packages will be installed:\n  nasm\n0 upgraded, 1 newly installed, 0 to remove and 7 not upgraded.\nNeed to get 375 kB of archives.\nAfter this operation, 3345 kB of additional disk space will be used.\nGet:1 http://archive.ubuntu.com/ubuntu jammy/universe amd64 nasm amd64 2.15.05-1 [375 kB]\nFetched 375 kB in 2s (187 kB/s)\nSelecting previously unselected package nasm.\n(Reading database ... \n(Reading database ... 5%\n(Reading database ... 10%\n(Reading database ... 15%\n(Reading database ... 20%\n(Reading database ... 25%\n(Reading database ... 30%\n(Reading database ... 35%\n(Reading database ... 40%\n(Reading database ... 45%\n(Reading database ... 50%\n(Reading database ... 55%\n(Reading database ... 60%\n(Reading database ... 65%\n(Reading database ... 70%\n(Reading database ... 75%\n(Reading database ... 80%\n(Reading database ... 85%\n(Reading database ... 90%\n(Reading database ... 95%\n(Reading database ... 100%\n(Reading database ... 14681 files and directories currently installed.)\nPreparing to unpack .../nasm_2.15.05-1_amd64.deb ...\nUnpacking nasm (2.15.05-1) ...\nSetting up nasm (2.15.05-1) ...\n\ndebconf: delaying package configuration, since apt-utils is not installed\n\nroot@c0mpi1er-c0nta1ner:/work# ", 25.943919897079468], ["./configure", "root@c0mpi1er-c0nta1ner:/work# install prefix            /usr/local\nsource path               .\nC compiler                gcc\nC library                 glibc\nARCH                      x86 (generic)\nbig-endian                no\nruntime cpu detection     yes\nstandalone assembly       yes\nx86 assembler             nasm\nMMX enabled               yes\nMMXEXT enabled            yes\n3DNow! enabled            yes\n3DNow! extended enabled   yes\nSSE enabled               yes\nSSSE3 enabled             yes\nAESNI enabled             yes\nAVX enabled               yes\nAVX2 enabled              yes\nAVX-512 enabled           yes\nAVX-512ICL enabled        yes\nXOP enabled               yes\nFMA3 enabled              yes\nFMA4 enabled              yes\ni686 features enabled     yes\nCMOV is fast              yes\nEBX available             yes\nEBP available             yes\ndebug symbols             yes\nstrip symbols             yes\noptimize for size         no\noptimizations             yes\nstatic                    yes\nshared                    no\nnetwork support           yes\nthreading support         pthreads\nsafe bitstream reader     yes\ntexi2html enabled         no\nperl enabled              yes\npod2man enabled           yes\nmakeinfo enabled          no\nmakeinfo supports HTML    no\nxmllint enabled           no\n\nExternal libraries:\niconv\n\nExternal libraries providing hardware acceleration:\nv4l2_m2m\n\nLibraries:\navcodec                 avformat                swscale\navdevice                avutil\navfilter                swresample\n\nPrograms:\nffmpeg                  ffprobe\n\nEnabled decoders:\naac                     ffwavesynth             pfm\naac_fixed               fic                     pgm\naac_latm                fits                    pgmyuv\naasc                    flac                    pgssub\nac3                     flic                    pgx\nac3_fixed               flv                     phm\nacelp_kelvin            fmvc                    photocd\nadpcm_4xm               fourxm                  pictor\nadpcm_adx               fraps                   pixlet\nadpcm_afc               frwu                    pjs\nadpcm_agm               ftr                     ppm\nadpcm_aica              g723_1                  prores\nadpcm_argo              g728                    prosumer\nadpcm_ct                g729                    psd\nadpcm_dtk               gdv                     ptx\nadpcm_ea                gem                     qcelp\nadpcm_ea_maxis_xa       gif                     qdm2\nadpcm_ea_r1             gremlin_dpcm            qdmc\nadpcm_ea_r2             gsm                     qdraw\nadpcm_ea_r3             gsm_ms                  qoa\nadpcm_ea_xas            h261                    qoi\nadpcm_g722              h263                    qpeg\nadpcm_g726              h263_v4l2m2m            qtrle\nadpcm_g726le            h263i                   r10k\nadpcm_ima_acorn         h263p                   r210\nadpcm_ima_alp           h264                    ra_144\nadpcm_ima_amv           h264_v4l2m2m            ra_288\nadpcm_ima_apc           hap                     ralf\nadpcm_ima_apm           hca                     rawvideo\nadpcm_ima_cunning       hcom                    realtext\nadpcm_ima_dat4          hdr                     rka\nadpcm_ima_dk3           hevc                    rl2\nadpcm_ima_dk4           hevc_v4l2m2m            roq\nadpcm_ima_ea_eacs       hnm4_video              roq_dpcm\nadpcm_ima_ea_sead       hq_hqa                  rpza\nadpcm_ima_iss           hqx                     rtv1\nadpcm_ima_moflex        huffyuv                 rv10\nadpcm_ima_mtf           hymt                    rv20\nadpcm_ima_oki           iac                     rv30\nadpcm_ima_qt            idcin                   rv40\nadpcm_ima_rad           idf                     rv60\nadpcm_ima_smjpeg        iff_ilbm                s302m\nadpcm_ima_ssi           ilbc                    sami\nadpcm_ima_wav           imc                     sanm\nadpcm_ima_ws            imm4                    sbc\nadpcm_ima_xbox          imm5                    scpr\nadpcm_ms                indeo2                  sdx2_dpcm\nadpcm_mtaf              indeo3                  sga\nadpcm_psx               indeo4                  sgi\nadpcm_sanyo             indeo5                  sgirle\nadpcm_sbpro_2           interplay_acm           sheervideo\nadpcm_sbpro_3           interplay_dpcm          shorten\nadpcm_sbpro_4           interplay_video         simbiosis_imx\nadpcm_swf               ipu                     sipr\nadpcm_thp               jacosub                 siren\nadpcm_thp_le            jpeg2000                smackaud\nadpcm_vima              jpegls                  smacker\nadpcm_xa                jv                      smc\nadpcm_xmd               kgv1                    smvjpeg\nadpcm_yamaha            kmvc                    snow\nadpcm_zork              lagarith                sol_dpcm\nagm                     lead                    sonic\naic                     loco                    sp5x\nalac                    m101                    speedhq\nalias_pix               mace3                   speex\nals                     mace6                   srt\namrnb                   magicyuv                ssa\namrwb                   mdec                    stl\namv                     media100                subrip\nanm                     metasound               subviewer\nansi                    microdvd                subviewer1\nanull                   mimic                   sunrast\napac                    misc4                   svq1\nape                     mjpeg                   svq3\naptx                    mjpegb                  tak\naptx_hd                 mlp                     targa\napv                     mmvideo                 targa_y216\narbc                    mobiclip                text\nargo                    motionpixels            theora\nass                     movtext                 thp\nasv1                    mp1                     tiertexseqvideo\nasv2                    mp1float                tiff\natrac1                  mp2                     tmv\natrac3                  mp2float                truehd\natrac3al                mp3                     truemotion1\natrac3p                 mp3adu                  truemotion2\natrac3pal               mp3adufloat             truemotion2rt\natrac9                  mp3float                truespeech\naura                    mp3on4                  tscc2\naura2                   mp3on4float             tta\nav1                     mpc7                    twinvq\navrn                    mpc8                    txd\navrp                    mpeg1_v4l2m2m           ulti\navs                     mpeg1video              utvideo\navui                    mpeg2_v4l2m2m           v210\nbethsoftvid             mpeg2video              v210x\nbfi                     mpeg4                   v308\nbink                    mpeg4_v4l2m2m           v408\nbinkaudio_dct           mpegvideo               v410\nbinkaudio_rdft          mpl2                    vb\nbintext                 msa1                    vble\nbitpacked               msmpeg4v1               vbn\nbmp                     msmpeg4v2               vc1\nbmv_audio               msmpeg4v3               vc1_v4l2m2m\nbmv_video               msnsiren                vc1image\nbonk                    msp2                    vcr1\nbrender_pix             msrle                   vmdaudio\nc93                     mss1                    vmdvideo\ncavs                    mss2                    vmix\ncbd2_dpcm               msvideo1                vmnc\nccaption                mszh                    vnull\ncdgraphics              mts2                    vorbis\ncdtoons                 mv30                    vp3\ncdxl                    mvc1                    vp4\ncfhd                    mvc2                    vp5\ncinepak                 mvdv                    vp6\nclearvideo              mxpeg                   vp6a\ncljr                    nellymoser              vp6f\ncllc                    notchlc                 vp7\ncomfortnoise            nuv                     vp8\ncook                    on2avc                  vp8_v4l2m2m\ncpia                    opus                    vp9\ncri                     osq                     vp9_v4l2m2m\ncscd                    paf_audio               vplayer\ncyuv                    paf_video               vqa\ndca                     pam                     vqc\ndds                     pbm                     vvc\nderf_dpcm               pcm_alaw                wady_dpcm\ndfa                     pcm_bluray              wavarc\ndfpwm                   pcm_dvd                 wavpack\ndirac                   pcm_f16le               wbmp\ndnxhd                   pcm_f24le               webp\ndolby_e                 pcm_f32be               webvtt\ndpx                     pcm_f32le               wmalossless\ndsd_lsbf                pcm_f64be               wmapro\ndsd_lsbf_planar         pcm_f64le               wmav1\ndsd_msbf                pcm_lxf                 wmav2\ndsd_msbf_planar         pcm_mulaw               wmavoice\ndsicinaudio             pcm_s16be               wmv1\ndsicinvideo             pcm_s16be_planar        wmv2\ndss_sp                  pcm_s16le               wmv3\ndst                     pcm_s16le_planar        wmv3image\ndvaudio                 pcm_s24be               wnv1\ndvbsub                  pcm_s24daud             wrapped_avframe\ndvdsub                  pcm_s24le               ws_snd1\ndvvideo                 pcm_s24le_planar        xan_dpcm\ndxtory                  pcm_s32be               xan_wc3\ndxv                     pcm_s32le               xan_wc4\neac3                    pcm_s32le_planar        xbin\neacmv                   pcm_s64be               xbm\neamad                   pcm_s64le               xface\neatgq                   pcm_s8                  xl\neatgv                   pcm_s8_planar           xma1\neatqi                   pcm_sga                 xma2\neightbps                pcm_u16be               xpm\neightsvx_exp            pcm_u16le               xsub\neightsvx_fib            pcm_u24be               xwd\nescape124               pcm_u24le               y41p\nescape130               pcm_u32be               ylc\nevrc                    pcm_u32le               yop\nfastaudio               pcm_u8                  yuv4\nffv1                    pcm_vidc                zero12v\nffvhuff                 pcx\n\nEnabled encoders:\na64multi                hdr                     pgmyuv\na64multi5               hevc_v4l2m2m            phm\naac                     huffyuv                 ppm\nac3                     jpeg2000                prores\nac3_fixed               jpegls                  prores_aw\nadpcm_adx               ljpeg                   prores_ks\nadpcm_argo              magicyuv                qoi\nadpcm_g722              mjpeg                   qtrle\nadpcm_g726              mlp                     r10k\nadpcm_g726le            movtext                 r210\nadpcm_ima_alp           mp2                     ra_144\nadpcm_ima_amv           mp2fixed                rawvideo\nadpcm_ima_apm           mpeg1video              roq\nadpcm_ima_qt            mpeg2video              roq_dpcm\nadpcm_ima_ssi           mpeg4                   rpza\nadpcm_ima_wav           mpeg4_v4l2m2m           rv10\nadpcm_ima_ws            msmpeg4v2               rv20\nadpcm_ms                msmpeg4v3               s302m\nadpcm_swf               msrle                   sbc\nadpcm_yamaha            msvideo1                sgi\nalac                    nellymoser              smc\nalias_pix               opus                    snow\namv                     pam                     speedhq\nanull                   pbm                     srt\naptx                    pcm_alaw                ssa\naptx_hd                 pcm_bluray              subrip\nass                     pcm_dvd                 sunrast\nasv1                    pcm_f32be               svq1\nasv2                    pcm_f32le               targa\navrp                    pcm_f64be               text\navui                    pcm_f64le               tiff\nbitpacked               pcm_mulaw               truehd\nbmp                     pcm_s16be               tta\ncfhd                    pcm_s16be_planar        ttml\ncinepak                 pcm_s16le               utvideo\ncljr                    pcm_s16le_planar        v210\ncomfortnoise            pcm_s24be               v308\ndca                     pcm_s24daud             v408\ndfpwm                   pcm_s24le               v410\ndnxhd                   pcm_s24le_planar        vbn\ndpx                     pcm_s32be               vc2\ndvbsub                  pcm_s32le               vnull\ndvdsub                  pcm_s32le_planar        vorbis\ndvvideo                 pcm_s64be               vp8_v4l2m2m\ndxv                     pcm_s64le               wavpack\neac3                    pcm_s8                  wbmp\nffv1                    pcm_s8_planar           webvtt\nffvhuff                 pcm_u16be               wmav1\nfits                    pcm_u16le               wmav2\nflac                    pcm_u24be               wmv1\nflv                     pcm_u24le               wmv2\ng723_1                  pcm_u32be               wrapped_avframe\ngif                     pcm_u32le               xbm\nh261                    pcm_u8                  xface\nh263                    pcm_vidc                xsub\nh263_v4l2m2m            pcx                     xwd\nh263p                   pfm                     y41p\nh264_v4l2m2m            pgm                     yuv4\n\nEnabled hwaccels:\n\nEnabled parsers:\naac                     dvd_nav                 mpeg4video\naac_latm                dvdsub                  mpegaudio\nac3                     evc                     mpegvideo\nadx                     ffv1                    opus\namr                     flac                    png\napv                     ftr                     pnm\nav1                     g723_1                  qoi\navs2                    g729                    rv34\navs3                    gif                     sbc\nbmp                     gsm                     sipr\ncavsvideo               h261                    tak\ncook                    h263                    vc1\ncri                     h264                    vorbis\ndca                     hdr                     vp3\ndirac                   hevc                    vp8\ndnxhd                   ipu                     vp9\ndnxuc                   jpeg2000                vvc\ndolby_e                 jpegxl                  webp\ndpx                     misc4                   xbm\ndvaudio                 mjpeg                   xma\ndvbsub                  mlp                     xwd\n\nEnabled demuxers:\naa                      idcin                   pcm_s16be\naac                     idf                     pcm_s16le\naax                     iff                     pcm_s24be\nac3                     ifv                     pcm_s24le\nac4                     ilbc                    pcm_s32be\nace                     image2                  pcm_s32le\nacm                     image2_alias_pix        pcm_s8\nact                     image2_brender_pix      pcm_u16be\nadf                     image2pipe              pcm_u16le\nadp                     image_bmp_pipe          pcm_u24be\nads                     image_cri_pipe          pcm_u24le\nadx                     image_dds_pipe          pcm_u32be\naea                     image_dpx_pipe          pcm_u32le\nafc                     image_exr_pipe          pcm_u8\naiff                    image_gem_pipe          pcm_vidc\naix                     image_gif_pipe          pdv\nalp                     image_hdr_pipe          pjs\namr                     image_j2k_pipe          pmp\namrnb                   image_jpeg_pipe         pp_bnk\namrwb                   image_jpegls_pipe       pva\nanm                     image_jpegxl_pipe       pvf\napac                    image_pam_pipe          qcp\napc                     image_pbm_pipe          qoa\nape                     image_pcx_pipe          r3d\napm                     image_pfm_pipe          rawvideo\napng                    image_pgm_pipe          rcwt\naptx                    image_pgmyuv_pipe       realtext\naptx_hd                 image_pgx_pipe          redspark\napv                     image_phm_pipe          rka\naqtitle                 image_photocd_pipe      rl2\nargo_asf                image_pictor_pipe       rm\nargo_brp                image_png_pipe          roq\nargo_cvg                image_ppm_pipe          rpl\nasf                     image_psd_pipe          rsd\nasf_o                   image_qdraw_pipe        rso\nass                     image_qoi_pipe          rtp\nast                     image_sgi_pipe          rtsp\nau                      image_sunrast_pipe      s337m\nav1                     image_svg_pipe          sami\navi                     image_tiff_pipe         sap\navr                     image_vbn_pipe          sbc\navs                     image_webp_pipe         sbg\navs2                    image_xbm_pipe          scc\navs3                    image_xpm_pipe          scd\nbethsoftvid             image_xwd_pipe          sdns\nbfi                     ingenient               sdp\nbfstm                   ipmovie                 sdr2\nbink                    ipu                     sds\nbinka                   ircam                   sdx\nbintext                 iss                     segafilm\nbit                     iv8                     ser\nbitpacked               ivf                     sga\nbmv                     ivr                     shorten\nboa                     jacosub                 siff\nbonk                    jpegxl_anim             simbiosis_imx\nbrstm                   jv                      sln\nc93                     kux                     smacker\ncaf                     kvag                    smjpeg\ncavsvideo               laf                     smush\ncdg                     lc3                     sol\ncdxl                    live_flv                sox\ncine                    lmlm4                   spdif\ncodec2                  loas                    srt\ncodec2raw               lrc                     stl\nconcat                  luodat                  str\ndata                    lvf                     subviewer\ndaud                    lxf                     subviewer1\ndcstr                   m4v                     sup\nderf                    matroska                svag\ndfa                     mca                     svs\ndfpwm                   mcc                     swf\ndhav                    mgsts                   tak\ndirac                   microdvd                tedcaptions\ndnxhd                   mjpeg                   thp\ndsf                     mjpeg_2000              threedostr\ndsicin                  mlp                     tiertexseq\ndss                     mlv                     tmv\ndts                     mm                      truehd\ndtshd                   mmf                     tta\ndv                      mods                    tty\ndvbsub                  moflex                  txd\ndvbtxt                  mov                     ty\ndxa                     mp3                     usm\nea                      mpc                     v210\nea_cdata                mpc8                    v210x\neac3                    mpegps                  vag\nepaf                    mpegts                  vc1\nevc                     mpegtsraw               vc1t\nffmetadata              mpegvideo               vividas\nfilmstrip               mpjpeg                  vivo\nfits                    mpl2                    vmd\nflac                    mpsub                   vobsub\nflic                    msf                     voc\nflv                     msnwc_tcp               vpk\nfourxm                  msp                     vplayer\nfrm                     mtaf                    vqf\nfsb                     mtv                     vvc\nfwse                    musx                    w64\ng722                    mv                      wady\ng723_1                  mvi                     wav\ng726                    mxf                     wavarc\ng726le                  mxg                     wc3\ng728                    nc                      webm_dash_manifest\ng729                    nistsphere              webvtt\ngdv                     nsp                     wsaud\ngenh                    nsv                     wsd\ngif                     nut                     wsvqa\ngsm                     nuv                     wtv\ngxf                     obu                     wv\nh261                    ogg                     wve\nh263                    oma                     xa\nh264                    osq                     xbin\nhca                     paf                     xmd\nhcom                    pcm_alaw                xmv\nhevc                    pcm_f32be               xvag\nhls                     pcm_f32le               xwma\nhnm                     pcm_f64be               yop\niamf                    pcm_f64le               yuv4mpegpipe\nico                     pcm_mulaw\n\nEnabled muxers:\na64                     h263                    pcm_s24be\nac3                     h264                    pcm_s24le\nac4                     hash                    pcm_s32be\nadts                    hds                     pcm_s32le\nadx                     hevc                    pcm_s8\naea                     hls                     pcm_u16be\naiff                    iamf                    pcm_u16le\nalp                     ico                     pcm_u24be\namr                     ilbc                    pcm_u24le\namv                     image2                  pcm_u32be\napm                     image2pipe              pcm_u32le\napng                    ipod                    pcm_u8\naptx                    ircam                   pcm_vidc\naptx_hd                 ismv                    psp\napv                     ivf                     rawvideo\nargo_asf                jacosub                 rcwt\nargo_cvg                kvag                    rm\nasf                     latm                    roq\nasf_stream              lc3                     rso\nass                     lrc                     rtp\nast                     m4v                     rtp_mpegts\nau                      matroska                rtsp\navi                     matroska_audio          sap\navif                    md5                     sbc\navm2                    microdvd                scc\navs2                    mjpeg                   segafilm\navs3                    mkvtimestamp_v2         segment\nbit                     mlp                     smjpeg\ncaf                     mmf                     smoothstreaming\ncavsvideo               mov                     sox\ncodec2                  mp2                     spdif\ncodec2raw               mp3                     spx\ncrc                     mp4                     srt\ndash                    mpeg1system             stream_segment\ndata                    mpeg1vcd                streamhash\ndaud                    mpeg1video              sup\ndfpwm                   mpeg2dvd                swf\ndirac                   mpeg2svcd               tee\ndnxhd                   mpeg2video              tg2\ndts                     mpeg2vob                tgp\ndv                      mpegts                  truehd\neac3                    mpjpeg                  tta\nevc                     mxf                     ttml\nf4v                     mxf_d10                 uncodedframecrc\nffmetadata              mxf_opatom              vc1\nfifo                    null                    vc1t\nfilmstrip               nut                     voc\nfits                    obu                     vvc\nflac                    oga                     w64\nflv                     ogg                     wav\nframecrc                ogv                     webm\nframehash               oma                     webm_chunk\nframemd5                opus                    webm_dash_manifest\ng722                    pcm_alaw                webp\ng723_1                  pcm_f32be               webvtt\ng726                    pcm_f32le               wsaud\ng726le                  pcm_f64be               wtv\ngif                     pcm_f64le               wv\ngsm                     pcm_mulaw               yuv4mpegpipe\ngxf                     pcm_s16be\nh261                    pcm_s16le\n\nEnabled protocols:\nasync                   gopher                  rtmp\ncache                   hls                     rtmpt\nconcat                  http                    rtp\nconcatf                 httpproxy               srtp\ncrypto                  icecast                 subfile\ndata                    md5                     tcp\nfd                      mmsh                    tee\nffrtmphttp              mmst                    udp\nfile                    pipe                    udplite\nftp                     prompeg                 unix\n\nEnabled filters:\na3dscope                colorspace              noformat\naap                     colorspectrum           noise\nabench                  colortemperature        normalize\nabitscope               compand                 null\nacompressor             compensationdelay       nullsink\nacontrast               concat                  nullsrc\nacopy                   convolution             oscilloscope\nacrossfade              convolve                overlay\nacrossover              copy                    pad\nacrusher                corr                    pal100bars\nacue                    crop                    pal75bars\naddroi                  crossfeed               palettegen\nadeclick                crystalizer             paletteuse\nadeclip                 cue                     pan\nadecorrelate            curves                  perlin\nadelay                  datascope               perms\nadenorm                 dblur                   photosensitivity\naderivative             dcshift                 pixdesctest\nadrawgraph              dctdnoiz                pixelize\nadrc                    deband                  pixscope\nadynamicequalizer       deblock                 premultiply\nadynamicsmooth          decimate                prewitt\naecho                   deconvolve              pseudocolor\naemphasis               dedot                   psnr\naeval                   deesser                 qp\naevalsrc                deflate                 random\naexciter                deflicker               readeia608\nafade                   dejudder                readvitc\nafdelaysrc              deshake                 realtime\nafftdn                  despill                 remap\nafftfilt                detelecine              removegrain\nafir                    dialoguenhance          removelogo\nafireqsrc               dilation                replaygain\nafirsrc                 displace                reverse\naformat                 doubleweave             rgbashift\nafreqshift              drawbox                 rgbtestsrc\nafwtdn                  drawgraph               roberts\nagate                   drawgrid                rotate\nagraphmonitor           drmeter                 scale\nahistogram              dynaudnorm              scale2ref\naiir                    earwax                  scdet\naintegral               ebur128                 scharr\nainterleave             edgedetect              scroll\nalatency                elbg                    segment\nalimiter                entropy                 select\nallpass                 epx                     selectivecolor\nallrgb                  equalizer               sendcmd\nallyuv                  erosion                 separatefields\naloop                   estdif                  setdar\nalphaextract            exposure                setfield\nalphamerge              extractplanes           setparams\namerge                  extrastereo             setpts\nametadata               fade                    setrange\namix                    feedback                setsar\namovie                  fftdnoiz                settb\namplify                 fftfilt                 shear\namultiply               field                   showcqt\nanequalizer             fieldhint               showcwt\nanlmdn                  fieldmatch              showfreqs\nanlmf                   fieldorder              showinfo\nanlms                   fillborders             showpalette\nanoisesrc               firequalizer            showspatial\nanull                   flanger                 showspectrum\nanullsink               floodfill               showspectrumpic\nanullsrc                format                  showvolume\napad                    fps                     showwaves\naperms                  framepack               showwavespic\naphasemeter             framerate               shuffleframes\naphaser                 framestep               shufflepixels\naphaseshift             freezedetect            shuffleplanes\napsnr                   freezeframes            sidechaincompress\napsyclip                fsync                   sidechaingate\napulsator               gblur                   sidedata\narealtime               geq                     sierpinski\naresample               gradfun                 signalstats\nareverse                gradients               silencedetect\narls                    graphmonitor            silenceremove\narnndn                  grayworld               sinc\nasdr                    greyedge                sine\nasegment                guided                  siti\naselect                 haas                    smptebars\nasendcmd                haldclut                smptehdbars\nasetnsamples            haldclutsrc             sobel\nasetpts                 hdcd                    spectrumsynth\nasetrate                headphone               speechnorm\nasettb                  hflip                   split\nashowinfo               highpass                ssim\nasidedata               highshelf               ssim360\nasisdr                  hilbert                 stereotools\nasoftclip               histogram               stereowiden\naspectralstats          hqx                     streamselect\nasplit                  hstack                  superequalizer\nastats                  hsvhold                 surround\nastreamselect           hsvkey                  swaprect\nasubboost               hue                     swapuv\nasubcut                 huesaturation           tblend\nasupercut               hwdownload              telecine\nasuperpass              hwmap                   testsrc\nasuperstop              hwupload                testsrc2\natadenoise              hysteresis              thistogram\natempo                  identity                threshold\natilt                   idet                    thumbnail\natrim                   il                      tile\navectorscope            inflate                 tiltandshift\navgblur                 interleave              tiltshelf\navsynctest              join                    tlut2\naxcorrelate             kirsch                  tmedian\nbackgroundkey           lagfun                  tmidequalizer\nbandpass                latency                 tmix\nbandreject              lenscorrection          tonemap\nbass                    life                    tpad\nbbox                    limitdiff               transpose\nbench                   limiter                 treble\nbilateral               loop                    tremolo\nbiquad                  loudnorm                trim\nbitplanenoise           lowpass                 unpremultiply\nblackdetect             lowshelf                unsharp\nblend                   lumakey                 untile\nblockdetect             lut                     v360\nblurdetect              lut1d                   varblur\nbm3d                    lut2                    vectorscope\nbwdif                   lut3d                   vflip\ncas                     lutrgb                  vfrdet\nccrepack                lutyuv                  vibrance\ncellauto                mandelbrot              vibrato\nchannelmap              maskedclamp             vif\nchannelsplit            maskedmax               vignette\nchorus                  maskedmerge             virtualbass\nchromahold              maskedmin               vmafmotion\nchromakey               maskedthreshold         volume\nchromanr                maskfun                 volumedetect\nchromashift             mcompand                vstack\nciescope                median                  w3fdif\ncodecview               mergeplanes             waveform\ncolor                   mestimate               weave\ncolorbalance            metadata                xbr\ncolorchannelmixer       midequalizer            xcorrelate\ncolorchart              minterpolate            xfade\ncolorcontrast           mix                     xmedian\ncolorcorrect            monochrome              xpsnr\ncolordetect             morpho                  xstack\ncolorhold               movie                   yadif\ncolorize                msad                    yaepblur\ncolorkey                multiply                yuvtestsrc\ncolorlevels             negate                  zoneplate\ncolormap                nlmeans                 zoompan\n\nEnabled bsfs:\naac_adtstoasc           h264_mp4toannexb        pgs_frame_merge\napv_metadata            h264_redundant_pps      prores_metadata\nav1_frame_merge         hapqa_extract           remove_extradata\nav1_frame_split         hevc_metadata           setts\nav1_metadata            hevc_mp4toannexb        showinfo\nchomp                   imx_dump_header         text2movsub\ndca_core                media100_to_mjpegb      trace_headers\ndovi_rpu                mjpeg2jpeg              truehd_core\ndts2pts                 mjpega_dump_header      vp9_metadata\ndump_extradata          mov2textsub             vp9_raw_reorder\ndv_error_marker         mpeg2_metadata          vp9_superframe\neac3_core               mpeg4_unpack_bframes    vp9_superframe_split\nevc_frame_merge         noise                   vvc_metadata\nextract_extradata       null                    vvc_mp4toannexb\nfilter_units            opus_metadata\nh264_metadata           pcm_rechunk\n\nEnabled indevs:\nfbdev                   oss\nlavfi                   v4l2\n\nEnabled outdevs:\nfbdev                   oss                     v4l2\n\nLicense: LGPL version 2.1 or later\n\nWARNING: pkg-config not found, library detection may fail.\n\nroot@c0mpi1er-c0nta1ner:/work# ", 13.72096586227417], ["sudo apt-get install -y pkg-config -y", "root@c0mpi1er-c0nta1ner:/work# Reading package lists...\nBuilding dependency tree...\nReading state information...\nThe following additional packages will be installed:\n  libdpkg-perl libfile-fcntllock-perl libglib2.0-0 libglib2.0-data libicu70\n  liblocale-gettext-perl libxml2 shared-mime-info xdg-user-dirs xz-utils\nSuggested packages:\n  debian-keyring gnupg bzr dpkg-dev\nThe following NEW packages will be installed:\n  libdpkg-perl libfile-fcntllock-perl libglib2.0-0 libglib2.0-data libicu70\n  liblocale-gettext-perl libxml2 pkg-config shared-mime-info xdg-user-dirs\n  xz-utils\n0 upgraded, 11 newly installed, 0 to remove and 7 not upgraded.\nNeed to get 13.7 MB of archives.\nAfter this operation, 48.2 MB of additional disk space will be used.\nGet:1 http://archive.ubuntu.com/ubuntu jammy/main amd64 liblocale-gettext-perl amd64 1.07-4build3 [17.1 kB]\nGet:2 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 libglib2.0-0 amd64 2.72.4-0ubuntu2.5 [1466 kB]\nGet:3 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 libglib2.0-data all 2.72.4-0ubuntu2.5 [4656 B]\nGet:4 http://archive.ubuntu.com/ubuntu jammy/main amd64 libicu70 amd64 70.1-2 [10.6 MB]\nGet:5 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 libxml2 amd64 2.9.13+dfsg-1ubuntu0.7 [763 kB]\nGet:6 http://archive.ubuntu.com/ubuntu jammy/main amd64 shared-mime-info amd64 2.1-2 [454 kB]\nGet:7 http://archive.ubuntu.com/ubuntu jammy/main amd64 xdg-user-dirs amd64 0.17-2ubuntu4 [53.9 kB]\nGet:8 http://archive.ubuntu.com/ubuntu jammy/main amd64 xz-utils amd64 5.2.5-2ubuntu1 [84.8 kB]\nGet:9 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 libdpkg-perl all 1.21.1ubuntu2.3 [237 kB]\nGet:10 http://archive.ubuntu.com/ubuntu jammy/main amd64 libfile-fcntllock-perl amd64 0.22-3build7 [33.9 kB]\nGet:11 http://archive.ubuntu.com/ubuntu jammy/main amd64 pkg-config amd64 0.29.2-1ubuntu3 [48.2 kB]\nFetched 13.7 MB in 5s (2599 kB/s)\nSelecting previously unselected package liblocale-gettext-perl.\n(Reading database ... \n(Reading database ... 5%\n(Reading database ... 10%\n(Reading database ... 15%\n(Reading database ... 20%\n(Reading database ... 25%\n(Reading database ... 30%\n(Reading database ... 35%\n(Reading database ... 40%\n(Reading database ... 45%\n(Reading database ... 50%\n(Reading database ... 55%\n(Reading database ... 60%\n(Reading database ... 65%\n(Reading database ... 70%\n(Reading database ... 75%\n(Reading database ... 80%\n(Reading database ... 85%\n(Reading database ... 90%\n(Reading database ... 95%\n(Reading database ... 100%\n(Reading database ... 14706 files and directories currently installed.)\nPreparing to unpack .../00-liblocale-gettext-perl_1.07-4build3_amd64.deb ...\nUnpacking liblocale-gettext-perl (1.07-4build3) ...\nSelecting previously unselected package libglib2.0-0:amd64.\nPreparing to unpack .../01-libglib2.0-0_2.72.4-0ubuntu2.5_amd64.deb ...\nUnpacking libglib2.0-0:amd64 (2.72.4-0ubuntu2.5) ...\nSelecting previously unselected package libglib2.0-data.\nPreparing to unpack .../02-libglib2.0-data_2.72.4-0ubuntu2.5_all.deb ...\nUnpacking libglib2.0-data (2.72.4-0ubuntu2.5) ...\nSelecting previously unselected package libicu70:amd64.\nPreparing to unpack .../03-libicu70_70.1-2_amd64.deb ...\nUnpacking libicu70:amd64 (70.1-2) ...\nSelecting previously unselected package libxml2:amd64.\nPreparing to unpack .../04-libxml2_2.9.13+dfsg-1ubuntu0.7_amd64.deb ...\nUnpacking libxml2:amd64 (2.9.13+dfsg-1ubuntu0.7) ...\nSelecting previously unselected package shared-mime-info.\nPreparing to unpack .../05-shared-mime-info_2.1-2_amd64.deb ...\nUnpacking shared-mime-info (2.1-2) ...\nSelecting previously unselected package xdg-user-dirs.\nPreparing to unpack .../06-xdg-user-dirs_0.17-2ubuntu4_amd64.deb ...\nUnpacking xdg-user-dirs (0.17-2ubuntu4) ...\nSelecting previously unselected package xz-utils.\nPreparing to unpack .../07-xz-utils_5.2.5-2ubuntu1_amd64.deb ...\nUnpacking xz-utils (5.2.5-2ubuntu1) ...\nSelecting previously unselected package libdpkg-perl.\nPreparing to unpack .../08-libdpkg-perl_1.21.1ubuntu2.3_all.deb ...\nUnpacking libdpkg-perl (1.21.1ubuntu2.3) ...\nSelecting previously unselected package libfile-fcntllock-perl.\nPreparing to unpack .../09-libfile-fcntllock-perl_0.22-3build7_amd64.deb ...\nUnpacking libfile-fcntllock-perl (0.22-3build7) ...\nSelecting previously unselected package pkg-config.\nPreparing to unpack .../10-pkg-config_0.29.2-1ubuntu3_amd64.deb ...\nUnpacking pkg-config (0.29.2-1ubuntu3) ...\nSetting up xdg-user-dirs (0.17-2ubuntu4) ...\nSetting up libfile-fcntllock-perl (0.22-3build7) ...\nSetting up libglib2.0-0:amd64 (2.72.4-0ubuntu2.5) ...\nNo schema files found: doing nothing.\nSetting up libglib2.0-data (2.72.4-0ubuntu2.5) ...\nSetting up xz-utils (5.2.5-2ubuntu1) ...\nupdate-alternatives: using /usr/bin/xz to provide /usr/bin/lzma (lzma) in auto mode\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/lzma.1.gz because associated file /usr/share/man/man1/xz.1.gz (of link group lzma) doesn't exist\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/unlzma.1.gz because associated file /usr/share/man/man1/unxz.1.gz (of link group lzma) doesn't exist\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/lzcat.1.gz because associated file /usr/share/man/man1/xzcat.1.gz (of link group lzma) doesn't exist\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/lzmore.1.gz because associated file /usr/share/man/man1/xzmore.1.gz (of link group lzma) doesn't exist\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/lzless.1.gz because associated file /usr/share/man/man1/xzless.1.gz (of link group lzma) doesn't exist\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/lzdiff.1.gz because associated file /usr/share/man/man1/xzdiff.1.gz (of link group lzma) doesn't exist\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/lzcmp.1.gz because associated file /usr/share/man/man1/xzcmp.1.gz (of link group lzma) doesn't exist\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/lzgrep.1.gz because associated file /usr/share/man/man1/xzgrep.1.gz (of link group lzma) doesn't exist\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/lzegrep.1.gz because associated file /usr/share/man/man1/xzegrep.1.gz (of link group lzma) doesn't exist\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/lzfgrep.1.gz because associated file /usr/share/man/man1/xzfgrep.1.gz (of link group lzma) doesn't exist\nSetting up libdpkg-perl (1.21.1ubuntu2.3) ...\nSetting up liblocale-gettext-perl (1.07-4build3) ...\nSetting up libicu70:amd64 (70.1-2) ...\nSetting up pkg-config (0.29.2-1ubuntu3) ...\nSetting up libxml2:amd64 (2.9.13+dfsg-1ubuntu0.7) ...\nSetting up shared-mime-info (2.1-2) ...\nProcessing triggers for libc-bin (2.35-0ubuntu3.10) ...\n\ndebconf: delaying package configuration, since apt-utils is not installed\n\nroot@c0mpi1er-c0nta1ner:/work# ", 8.450368404388428], ["make -j$(nproc)", "root@c0mpi1er-c0nta1ner:/work# GEN\tlibavutil/libavutil.version\nGEN\tlibswscale/libswscale.version\nGEN\tlibswresample/libswresample.version\nGEN\tlibavcodec/libavcodec.version\nGEN\tlibavformat/libavformat.version\nGEN\tlibavfilter/libavfilter.version\nGEN\tlibavdevice/libavdevice.version\nCC\tlibavdevice/alldevices.o\nCC\tlibavdevice/avdevice.o\nCC\tlibavdevice/fbdev_common.o\nCC\tlibavdevice/fbdev_dec.o\nCC\tlibavdevice/fbdev_enc.o\nCC\tlibavdevice/lavfi.o\nCC\tlibavdevice/oss.o\nCC\tlibavdevice/oss_dec.o\nCC\tlibavdevice/oss_enc.o\nCC\tlibavdevice/timefilter.o\nCC\tlibavdevice/utils.o\nCC\tlibavdevice/v4l2-common.o\nCC\tlibavdevice/v4l2.o\nCC\tlibavdevice/v4l2enc.o\nCC\tlibavdevice/version.o\nGEN\tlibavdevice/libavdevice.pc\nCC\tlibavfilter/aeval.o\nCC\tlibavfilter/af_aap.o\nCC\tlibavfilter/af_acontrast.o\nCC\tlibavfilter/af_acopy.o\nCC\tlibavfilter/af_acrossover.o\nCC\tlibavfilter/af_acrusher.o\nCC\tlibavfilter/af_adeclick.o\nCC\tlibavfilter/af_adecorrelate.o\nCC\tlibavfilter/af_adelay.o\nCC\tlibavfilter/af_adenorm.o\nCC\tlibavfilter/af_aderivative.o\nCC\tlibavfilter/af_adrc.o\nCC\tlibavfilter/af_adynamicequalizer.o\nCC\tlibavfilter/af_adynamicsmooth.o\nCC\tlibavfilter/af_aecho.o\nCC\tlibavfilter/af_aemphasis.o\nCC\tlibavfilter/af_aexciter.o\nCC\tlibavfilter/af_afade.o\nCC\tlibavfilter/af_afftdn.o\nCC\tlibavfilter/af_afftfilt.o\nCC\tlibavfilter/af_afir.o\nCC\tlibavfilter/af_aformat.o\nCC\tlibavfilter/af_afreqshift.o\nCC\tlibavfilter/af_afwtdn.o\nCC\tlibavfilter/af_agate.o\nCC\tlibavfilter/af_aiir.o\nCC\tlibavfilter/af_alimiter.o\nCC\tlibavfilter/af_amerge.o\nCC\tlibavfilter/af_amix.o\nCC\tlibavfilter/af_amultiply.o\nCC\tlibavfilter/af_anequalizer.o\nCC\tlibavfilter/af_anlmdn.o\nCC\tlibavfilter/af_anlms.o\nCC\tlibavfilter/af_anull.o\nCC\tlibavfilter/af_apad.o\nCC\tlibavfilter/af_aphaser.o\nCC\tlibavfilter/af_apsyclip.o\nCC\tlibavfilter/af_apulsator.o\nCC\tlibavfilter/af_aresample.o\nCC\tlibavfilter/af_arls.o\nCC\tlibavfilter/af_arnndn.o\nCC\tlibavfilter/af_asdr.o\nCC\tlibavfilter/af_asetnsamples.o\nCC\tlibavfilter/af_asetrate.o\nCC\tlibavfilter/af_ashowinfo.o\nCC\tlibavfilter/af_asoftclip.o\nCC\tlibavfilter/af_aspectralstats.o\nCC\tlibavfilter/af_astats.o\nCC\tlibavfilter/af_asubboost.o\nCC\tlibavfilter/af_asupercut.o\nCC\tlibavfilter/af_atempo.o\nCC\tlibavfilter/af_atilt.o\nCC\tlibavfilter/af_axcorrelate.o\nCC\tlibavfilter/af_biquads.o\nCC\tlibavfilter/af_channelmap.o\nCC\tlibavfilter/af_channelsplit.o\nCC\tlibavfilter/af_chorus.o\nCC\tlibavfilter/af_compand.o\nCC\tlibavfilter/af_compensationdelay.o\nCC\tlibavfilter/af_crossfeed.o\nCC\tlibavfilter/af_crystalizer.o\nCC\tlibavfilter/af_dcshift.o\nCC\tlibavfilter/af_deesser.o\nCC\tlibavfilter/af_dialoguenhance.o\nCC\tlibavfilter/af_drmeter.o\nCC\tlibavfilter/af_dynaudnorm.o\nCC\tlibavfilter/af_earwax.o\nCC\tlibavfilter/af_extrastereo.o\nCC\tlibavfilter/af_firequalizer.o\nCC\tlibavfilter/af_flanger.o\nCC\tlibavfilter/af_haas.o\nCC\tlibavfilter/af_hdcd.o\nCC\tlibavfilter/af_headphone.o\nCC\tlibavfilter/af_join.o\nCC\tlibavfilter/af_loudnorm.o\nCC\tlibavfilter/af_mcompand.o\nCC\tlibavfilter/af_pan.o\nCC\tlibavfilter/af_replaygain.o\nCC\tlibavfilter/af_sidechaincompress.o\nCC\tlibavfilter/af_silencedetect.o\nCC\tlibavfilter/af_silenceremove.o\nCC\tlibavfilter/af_speechnorm.o\nCC\tlibavfilter/af_stereotools.o\nCC\tlibavfilter/af_stereowiden.o\nCC\tlibavfilter/af_superequalizer.o\nCC\tlibavfilter/af_surround.o\nCC\tlibavfilter/af_tremolo.o\nCC\tlibavfilter/af_vibrato.o\nCC\tlibavfilter/af_virtualbass.o\nCC\tlibavfilter/af_volume.o\nCC\tlibavfilter/af_volumedetect.o\nCC\tlibavfilter/allfilters.o\nCC\tlibavfilter/asink_anullsink.o\nCC\tlibavfilter/asrc_afdelaysrc.o\nCC\tlibavfilter/asrc_afirsrc.o\nCC\tlibavfilter/asrc_anoisesrc.o\nCC\tlibavfilter/asrc_anullsrc.o\nCC\tlibavfilter/asrc_hilbert.o\nCC\tlibavfilter/asrc_sinc.o\nCC\tlibavfilter/asrc_sine.o\nCC\tlibavfilter/audio.o\nCC\tlibavfilter/avf_a3dscope.o\nCC\tlibavfilter/avf_abitscope.o\nCC\tlibavfilter/avf_ahistogram.o\nCC\tlibavfilter/avf_aphasemeter.o\nCC\tlibavfilter/avf_avectorscope.o\nCC\tlibavfilter/avf_concat.o\nCC\tlibavfilter/avf_showcqt.o\nCC\tlibavfilter/avf_showcwt.o\nCC\tlibavfilter/avf_showfreqs.o\nCC\tlibavfilter/avf_showspatial.o\nCC\tlibavfilter/avf_showspectrum.o\nCC\tlibavfilter/avf_showvolume.o\nCC\tlibavfilter/avf_showwaves.o\nCC\tlibavfilter/avfilter.o\nCC\tlibavfilter/avfiltergraph.o\nCC\tlibavfilter/bbox.o\nCC\tlibavfilter/buffersink.o\nCC\tlibavfilter/buffersrc.o\nCC\tlibavfilter/bwdifdsp.o\nCC\tlibavfilter/ccfifo.o\nCC\tlibavfilter/colorspace.o\nCC\tlibavfilter/colorspacedsp.o\nCC\tlibavfilter/drawutils.o\nCC\tlibavfilter/ebur128.o\nCC\tlibavfilter/edge_common.o\nCC\tlibavfilter/f_bench.o\nCC\tlibavfilter/f_cue.o\nCC\tlibavfilter/f_drawgraph.o\nCC\tlibavfilter/f_ebur128.o\nCC\tlibavfilter/f_graphmonitor.o\nCC\tlibavfilter/f_interleave.o\nCC\tlibavfilter/f_latency.o\nCC\tlibavfilter/f_loop.o\nCC\tlibavfilter/f_metadata.o\nCC\tlibavfilter/f_perms.o\nCC\tlibavfilter/f_realtime.o\nCC\tlibavfilter/f_reverse.o\nCC\tlibavfilter/f_segment.o\nCC\tlibavfilter/f_select.o\nCC\tlibavfilter/f_sendcmd.o\nCC\tlibavfilter/f_sidedata.o\nCC\tlibavfilter/f_streamselect.o\nCC\tlibavfilter/formats.o\nCC\tlibavfilter/framepool.o\nCC\tlibavfilter/framequeue.o\nCC\tlibavfilter/framesync.o\nCC\tlibavfilter/generate_wave_table.o\nCC\tlibavfilter/graphdump.o\nCC\tlibavfilter/graphparser.o\nCC\tlibavfilter/lavfutils.o\nCC\tlibavfilter/lswsutils.o\nCC\tlibavfilter/motion_estimation.o\nCC\tlibavfilter/palette.o\nCC\tlibavfilter/perlin.o\nCC\tlibavfilter/psnr.o\nCC\tlibavfilter/pthread.o\nCC\tlibavfilter/qp_table.o\nCC\tlibavfilter/scale_eval.o\nCC\tlibavfilter/scene_sad.o\nCC\tlibavfilter/setpts.o\nCC\tlibavfilter/settb.o\nCC\tlibavfilter/split.o\nCC\tlibavfilter/src_avsynctest.o\nCC\tlibavfilter/src_movie.o\nCC\tlibavfilter/transform.o\nCC\tlibavfilter/trim.o\nCC\tlibavfilter/vaf_spectrumsynth.o\nCC\tlibavfilter/version.o\nCC\tlibavfilter/vf_addroi.o\nCC\tlibavfilter/vf_alphamerge.o\nCC\tlibavfilter/vf_amplify.o\nCC\tlibavfilter/vf_aspect.o\nCC\tlibavfilter/vf_atadenoise.o\nCC\tlibavfilter/vf_avgblur.o\nCC\tlibavfilter/vf_backgroundkey.o\nCC\tlibavfilter/vf_bbox.o\nCC\tlibavfilter/vf_bilateral.o\nCC\tlibavfilter/vf_bitplanenoise.o\nCC\tlibavfilter/vf_blackdetect.o\nCC\tlibavfilter/vf_blend.o\nCC\tlibavfilter/vf_blockdetect.o\nCC\tlibavfilter/vf_blurdetect.o\nCC\tlibavfilter/vf_bm3d.o\nCC\tlibavfilter/vf_bwdif.o\nCC\tlibavfilter/vf_cas.o\nCC\tlibavfilter/vf_ccrepack.o\nCC\tlibavfilter/vf_chromakey.o\nCC\tlibavfilter/vf_chromanr.o\nCC\tlibavfilter/vf_chromashift.o\nCC\tlibavfilter/vf_ciescope.o\nCC\tlibavfilter/vf_codecview.o\nCC\tlibavfilter/vf_colorbalance.o\nCC\tlibavfilter/vf_colorchannelmixer.o\nCC\tlibavfilter/vf_colorconstancy.o\nCC\tlibavfilter/vf_colorcontrast.o\nCC\tlibavfilter/vf_colorcorrect.o\nCC\tlibavfilter/vf_colordetect.o\nCC\tlibavfilter/vf_colorize.o\nCC\tlibavfilter/vf_colorkey.o\nCC\tlibavfilter/vf_colorlevels.o\nCC\tlibavfilter/vf_colormap.o\nCC\tlibavfilter/vf_colorspace.o\nCC\tlibavfilter/vf_colortemperature.o\nCC\tlibavfilter/vf_convolution.o\nCC\tlibavfilter/vf_convolve.o\nCC\tlibavfilter/vf_copy.o\nCC\tlibavfilter/vf_corr.o\nCC\tlibavfilter/vf_crop.o\nCC\tlibavfilter/vf_curves.o\nCC\tlibavfilter/vf_datascope.o\nCC\tlibavfilter/vf_dblur.o\nCC\tlibavfilter/vf_dctdnoiz.o\nCC\tlibavfilter/vf_deband.o\nCC\tlibavfilter/vf_deblock.o\nCC\tlibavfilter/vf_decimate.o\nCC\tlibavfilter/vf_dedot.o\nCC\tlibavfilter/vf_deflicker.o\nCC\tlibavfilter/vf_dejudder.o\nCC\tlibavfilter/vf_deshake.o\nCC\tlibavfilter/vf_despill.o\nCC\tlibavfilter/vf_detelecine.o\nCC\tlibavfilter/vf_displace.o\nCC\tlibavfilter/vf_drawbox.o\nCC\tlibavfilter/vf_edgedetect.o\nCC\tlibavfilter/vf_elbg.o\nCC\tlibavfilter/vf_entropy.o\nCC\tlibavfilter/vf_epx.o\nCC\tlibavfilter/vf_estdif.o\nCC\tlibavfilter/vf_exposure.o\nCC\tlibavfilter/vf_extractplanes.o\nCC\tlibavfilter/vf_fade.o\nCC\tlibavfilter/vf_feedback.o\nCC\tlibavfilter/vf_fftdnoiz.o\nCC\tlibavfilter/vf_fftfilt.o\nCC\tlibavfilter/vf_field.o\nCC\tlibavfilter/vf_fieldhint.o\nCC\tlibavfilter/vf_fieldmatch.o\nCC\tlibavfilter/vf_fieldorder.o\nCC\tlibavfilter/vf_fillborders.o\nCC\tlibavfilter/vf_floodfill.o\nCC\tlibavfilter/vf_format.o\nCC\tlibavfilter/vf_fps.o\nCC\tlibavfilter/vf_framepack.o\nCC\tlibavfilter/vf_framerate.o\nCC\tlibavfilter/vf_framestep.o\nCC\tlibavfilter/vf_freezedetect.o\nCC\tlibavfilter/vf_freezeframes.o\nCC\tlibavfilter/vf_fsync.o\nCC\tlibavfilter/vf_gblur.o\nCC\tlibavfilter/vf_geq.o\nCC\tlibavfilter/vf_gradfun.o\nCC\tlibavfilter/vf_grayworld.o\nCC\tlibavfilter/vf_guided.o\nCC\tlibavfilter/vf_hflip.o\nCC\tlibavfilter/vf_histogram.o\nCC\tlibavfilter/vf_hqx.o\nCC\tlibavfilter/vf_hsvkey.o\nCC\tlibavfilter/vf_hue.o\nCC\tlibavfilter/vf_huesaturation.o\nCC\tlibavfilter/vf_hwdownload.o\nCC\tlibavfilter/vf_hwmap.o\nCC\tlibavfilter/vf_hwupload.o\nCC\tlibavfilter/vf_hysteresis.o\nCC\tlibavfilter/vf_identity.o\nCC\tlibavfilter/vf_idet.o\nCC\tlibavfilter/vf_il.o\nCC\tlibavfilter/vf_lagfun.o\nCC\tlibavfilter/vf_lenscorrection.o\nCC\tlibavfilter/vf_limitdiff.o\nCC\tlibavfilter/vf_limiter.o\nCC\tlibavfilter/vf_lumakey.o\nCC\tlibavfilter/vf_lut.o\nCC\tlibavfilter/vf_lut2.o\nCC\tlibavfilter/vf_lut3d.o\nCC\tlibavfilter/vf_maskedclamp.o\nCC\tlibavfilter/vf_maskedmerge.o\nCC\tlibavfilter/vf_maskedminmax.o\nCC\tlibavfilter/vf_maskedthreshold.o\nCC\tlibavfilter/vf_maskfun.o\nCC\tlibavfilter/vf_median.o\nCC\tlibavfilter/vf_mergeplanes.o\nCC\tlibavfilter/vf_mestimate.o\nCC\tlibavfilter/vf_midequalizer.o\nCC\tlibavfilter/vf_minterpolate.o\nCC\tlibavfilter/vf_mix.o\nCC\tlibavfilter/vf_monochrome.o\nCC\tlibavfilter/vf_morpho.o\nCC\tlibavfilter/vf_multiply.o\nCC\tlibavfilter/vf_negate.o\nCC\tlibavfilter/vf_neighbor.o\nCC\tlibavfilter/vf_nlmeans.o\nCC\tlibavfilter/vf_noise.o\nCC\tlibavfilter/vf_normalize.o\nCC\tlibavfilter/vf_null.o\nCC\tlibavfilter/vf_overlay.o\nCC\tlibavfilter/vf_pad.o\nCC\tlibavfilter/vf_palettegen.o\nCC\tlibavfilter/vf_paletteuse.o\nCC\tlibavfilter/vf_photosensitivity.o\nCC\tlibavfilter/vf_pixdesctest.o\nCC\tlibavfilter/vf_pixelize.o\nCC\tlibavfilter/vf_premultiply.o\nCC\tlibavfilter/vf_pseudocolor.o\nCC\tlibavfilter/vf_psnr.o\nCC\tlibavfilter/vf_qp.o\nCC\tlibavfilter/vf_random.o\nCC\tlibavfilter/vf_readeia608.o\nCC\tlibavfilter/vf_readvitc.o\nCC\tlibavfilter/vf_remap.o\nCC\tlibavfilter/vf_removegrain.o\nCC\tlibavfilter/vf_removelogo.o\nCC\tlibavfilter/vf_rotate.o\nCC\tlibavfilter/vf_scale.o\nCC\tlibavfilter/vf_scdet.o\nCC\tlibavfilter/vf_scroll.o\nCC\tlibavfilter/vf_selectivecolor.o\nCC\tlibavfilter/vf_separatefields.o\nCC\tlibavfilter/vf_setparams.o\nCC\tlibavfilter/vf_shear.o\nCC\tlibavfilter/vf_showinfo.o\nCC\tlibavfilter/vf_showpalette.o\nCC\tlibavfilter/vf_shuffleframes.o\nCC\tlibavfilter/vf_shufflepixels.o\nCC\tlibavfilter/vf_shuffleplanes.o\nCC\tlibavfilter/vf_signalstats.o\nCC\tlibavfilter/vf_siti.o\nCC\tlibavfilter/vf_ssim.o\nCC\tlibavfilter/vf_ssim360.o\nCC\tlibavfilter/vf_stack.o\nCC\tlibavfilter/vf_swaprect.o\nCC\tlibavfilter/vf_swapuv.o\nCC\tlibavfilter/vf_telecine.o\nCC\tlibavfilter/vf_threshold.o\nCC\tlibavfilter/vf_thumbnail.o\nCC\tlibavfilter/vf_tile.o\nCC\tlibavfilter/vf_tiltandshift.o\nCC\tlibavfilter/vf_tmidequalizer.o\nCC\tlibavfilter/vf_tonemap.o\nCC\tlibavfilter/vf_tpad.o\nCC\tlibavfilter/vf_transpose.o\nCC\tlibavfilter/vf_unsharp.o\nCC\tlibavfilter/vf_untile.o\nCC\tlibavfilter/vf_v360.o\nCC\tlibavfilter/vf_varblur.o\nCC\tlibavfilter/vf_vectorscope.o\nCC\tlibavfilter/vf_vflip.o\nCC\tlibavfilter/vf_vfrdet.o\nCC\tlibavfilter/vf_vibrance.o\nCC\tlibavfilter/vf_vif.o\nCC\tlibavfilter/vf_vignette.o\nCC\tlibavfilter/vf_vmafmotion.o\nCC\tlibavfilter/vf_w3fdif.o\nCC\tlibavfilter/vf_waveform.o\nCC\tlibavfilter/vf_weave.o\nCC\tlibavfilter/vf_xbr.o\nCC\tlibavfilter/vf_xfade.o\nCC\tlibavfilter/vf_xmedian.o\nCC\tlibavfilter/vf_xpsnr.o\nCC\tlibavfilter/vf_yadif.o\nCC\tlibavfilter/vf_yaepblur.o\nCC\tlibavfilter/vf_zoompan.o\nCC\tlibavfilter/video.o\nCC\tlibavfilter/vsink_nullsink.o\nCC\tlibavfilter/vsrc_cellauto.o\nCC\tlibavfilter/vsrc_gradients.o\nCC\tlibavfilter/vsrc_life.o\nCC\tlibavfilter/vsrc_mandelbrot.o\nCC\tlibavfilter/vsrc_perlin.o\nCC\tlibavfilter/vsrc_sierpinski.o\nCC\tlibavfilter/vsrc_testsrc.o\nX86ASM\tlibavfilter/x86/af_afir.o\nCC\tlibavfilter/x86/af_afir_init.o\nX86ASM\tlibavfilter/x86/af_anlmdn.o\nCC\tlibavfilter/x86/af_anlmdn_init.o\nX86ASM\tlibavfilter/x86/af_volume.o\nCC\tlibavfilter/x86/af_volume_init.o\nX86ASM\tlibavfilter/x86/avf_showcqt.o\nCC\tlibavfilter/x86/avf_showcqt_init.o\nX86ASM\tlibavfilter/x86/colorspacedsp.o\nCC\tlibavfilter/x86/colorspacedsp_init.o\nX86ASM\tlibavfilter/x86/f_ebur128.o\nCC\tlibavfilter/x86/f_ebur128_init.o\nX86ASM\tlibavfilter/x86/scene_sad.o\nSTRIP\tlibavfilter/x86/af_afir.o\nCC\tlibavfilter/x86/scene_sad_init.o\nX86ASM\tlibavfilter/x86/vf_atadenoise.o\nCC\tlibavfilter/x86/vf_atadenoise_init.o\nX86ASM\tlibavfilter/x86/vf_blackdetect.o\nCC\tlibavfilter/x86/vf_blackdetect_init.o\nSTRIP\tlibavfilter/x86/af_anlmdn.o\nX86ASM\tlibavfilter/x86/vf_blend.o\nCC\tlibavfilter/x86/vf_blend_init.o\nX86ASM\tlibavfilter/x86/vf_bwdif.o\nCC\tlibavfilter/x86/vf_bwdif_init.o\nX86ASM\tlibavfilter/x86/vf_colordetect.o\nCC\tlibavfilter/x86/vf_colordetect_init.o\nX86ASM\tlibavfilter/x86/vf_convolution.o\nCC\tlibavfilter/x86/vf_convolution_init.o\nSTRIP\tlibavfilter/x86/af_volume.o\nX86ASM\tlibavfilter/x86/vf_framerate.o\nCC\tlibavfilter/x86/vf_framerate_init.o\nX86ASM\tlibavfilter/x86/vf_gblur.o\nSTRIP\tlibavfilter/x86/vf_blackdetect.o\nCC\tlibavfilter/x86/vf_gblur_init.o\nX86ASM\tlibavfilter/x86/vf_gradfun.o\nCC\tlibavfilter/x86/vf_gradfun_init.o\nX86ASM\tlibavfilter/x86/vf_hflip.o\nCC\tlibavfilter/x86/vf_hflip_init.o\nSTRIP\tlibavfilter/x86/scene_sad.o\nX86ASM\tlibavfilter/x86/vf_idet.o\nCC\tlibavfilter/x86/vf_idet_init.o\nX86ASM\tlibavfilter/x86/vf_limiter.o\nCC\tlibavfilter/x86/vf_limiter_init.o\nX86ASM\tlibavfilter/x86/vf_lut3d.o\nCC\tlibavfilter/x86/vf_lut3d_init.o\nSTRIP\tlibavfilter/x86/f_ebur128.o\nX86ASM\tlibavfilter/x86/vf_maskedclamp.o\nSTRIP\tlibavfilter/x86/vf_atadenoise.o\nCC\tlibavfilter/x86/vf_maskedclamp_init.o\nX86ASM\tlibavfilter/x86/vf_maskedmerge.o\nCC\tlibavfilter/x86/vf_maskedmerge_init.o\nX86ASM\tlibavfilter/x86/vf_nlmeans.o\nCC\tlibavfilter/x86/vf_nlmeans_init.o\nCC\tlibavfilter/x86/vf_noise.o\nX86ASM\tlibavfilter/x86/vf_overlay.o\nSTRIP\tlibavfilter/x86/vf_framerate.o\nCC\tlibavfilter/x86/vf_overlay_init.o\nX86ASM\tlibavfilter/x86/vf_psnr.o\nCC\tlibavfilter/x86/vf_psnr_init.o\nSTRIP\tlibavfilter/x86/vf_hflip.o\nCC\tlibavfilter/x86/vf_removegrain_init.o\nX86ASM\tlibavfilter/x86/vf_ssim.o\nSTRIP\tlibavfilter/x86/vf_idet.o\nCC\tlibavfilter/x86/vf_ssim_init.o\nSTRIP\tlibavfilter/x86/vf_limiter.o\nX86ASM\tlibavfilter/x86/vf_threshold.o\nCC\tlibavfilter/x86/vf_threshold_init.o\nX86ASM\tlibavfilter/x86/vf_transpose.o\nCC\tlibavfilter/x86/vf_transpose_init.o\nSTRIP\tlibavfilter/x86/vf_gradfun.o\nX86ASM\tlibavfilter/x86/vf_v360.o\nSTRIP\tlibavfilter/x86/vf_colordetect.o\nCC\tlibavfilter/x86/vf_v360_init.o\nX86ASM\tlibavfilter/x86/vf_w3fdif.o\nSTRIP\tlibavfilter/x86/vf_convolution.o\nCC\tlibavfilter/x86/vf_w3fdif_init.o\nX86ASM\tlibavfilter/x86/vf_yadif.o\nSTRIP\tlibavfilter/x86/vf_maskedmerge.o\nCC\tlibavfilter/x86/vf_yadif_init.o\nX86ASM\tlibavfilter/x86/yadif-10.o\nX86ASM\tlibavfilter/x86/yadif-16.o\nSTRIP\tlibavfilter/x86/vf_maskedclamp.o\nSTRIP\tlibavfilter/x86/vf_nlmeans.o\nCC\tlibavfilter/yadif_common.o\nGEN\tlibavfilter/libavfilter.pc\nCC\tlibavformat/3dostr.o\nCC\tlibavformat/4xm.o\nCC\tlibavformat/a64.o\nCC\tlibavformat/aacdec.o\nCC\tlibavformat/aadec.o\nCC\tlibavformat/aaxdec.o\nCC\tlibavformat/ac3dec.o\nCC\tlibavformat/ac4dec.o\nSTRIP\tlibavfilter/x86/vf_psnr.o\nSTRIP\tlibavfilter/x86/vf_overlay.o\nCC\tlibavformat/ac4enc.o\nCC\tlibavformat/acedec.o\nSTRIP\tlibavfilter/x86/vf_threshold.o\nCC\tlibavformat/acm.o\nCC\tlibavformat/act.o\nSTRIP\tlibavfilter/x86/vf_transpose.o\nCC\tlibavformat/adp.o\nSTRIP\tlibavfilter/x86/vf_ssim.o\nCC\tlibavformat/ads.o\nCC\tlibavformat/adtsenc.o\nCC\tlibavformat/adxdec.o\nCC\tlibavformat/aeadec.o\nCC\tlibavformat/aeaenc.o\nSTRIP\tlibavfilter/x86/avf_showcqt.o\nCC\tlibavformat/afc.o\nCC\tlibavformat/aiff.o\nCC\tlibavformat/aiffdec.o\nSTRIP\tlibavfilter/x86/vf_v360.o\nCC\tlibavformat/aiffenc.o\nCC\tlibavformat/aixdec.o\nSTRIP\tlibavfilter/x86/vf_w3fdif.o\nCC\tlibavformat/allformats.o\nCC\tlibavformat/alp.o\nCC\tlibavformat/amr.o\nCC\tlibavformat/amvenc.o\nCC\tlibavformat/anm.o\nCC\tlibavformat/apac.o\nCC\tlibavformat/apc.o\nCC\tlibavformat/ape.o\nCC\tlibavformat/apetag.o\nCC\tlibavformat/apm.o\nCC\tlibavformat/apngdec.o\nCC\tlibavformat/apngenc.o\nCC\tlibavformat/aptxdec.o\nCC\tlibavformat/apv.o\nCC\tlibavformat/apvdec.o\nCC\tlibavformat/apvenc.o\nCC\tlibavformat/aqtitledec.o\nCC\tlibavformat/argo_asf.o\nCC\tlibavformat/argo_brp.o\nCC\tlibavformat/argo_cvg.o\nCC\tlibavformat/asf.o\nCC\tlibavformat/asf_tags.o\nCC\tlibavformat/asfcrypt.o\nCC\tlibavformat/asfdec_f.o\nCC\tlibavformat/asfdec_o.o\nCC\tlibavformat/asfenc.o\nCC\tlibavformat/assdec.o\nCC\tlibavformat/assenc.o\nCC\tlibavformat/ast.o\nCC\tlibavformat/astdec.o\nCC\tlibavformat/astenc.o\nCC\tlibavformat/async.o\nCC\tlibavformat/au.o\nCC\tlibavformat/av1.o\nCC\tlibavformat/av1dec.o\nCC\tlibavformat/avc.o\nCC\tlibavformat/avformat.o\nCC\tlibavformat/avidec.o\nCC\tlibavformat/avienc.o\nCC\tlibavformat/avio.o\nCC\tlibavformat/aviobuf.o\nCC\tlibavformat/avlanguage.o\nCC\tlibavformat/avr.o\nCC\tlibavformat/avs.o\nCC\tlibavformat/avs2dec.o\nCC\tlibavformat/avs3dec.o\nCC\tlibavformat/bethsoftvid.o\nSTRIP\tlibavfilter/x86/vf_gblur.o\nCC\tlibavformat/bfi.o\nCC\tlibavformat/bink.o\nCC\tlibavformat/binka.o\nCC\tlibavformat/bintext.o\nCC\tlibavformat/bit.o\nCC\tlibavformat/bmv.o\nCC\tlibavformat/boadec.o\nCC\tlibavformat/bonk.o\nCC\tlibavformat/brstm.o\nCC\tlibavformat/c93.o\nCC\tlibavformat/cache.o\nCC\tlibavformat/caf.o\nCC\tlibavformat/cafdec.o\nCC\tlibavformat/cafenc.o\nSTRIP\tlibavfilter/x86/vf_blend.o\nCC\tlibavformat/cavsvideodec.o\nCC\tlibavformat/cbs.o\nCC\tlibavformat/cbs_apv.o\nCC\tlibavformat/cbs_av1.o\nCC\tlibavformat/cdg.o\nCC\tlibavformat/cdxl.o\nCC\tlibavformat/cinedec.o\nSTRIP\tlibavfilter/x86/yadif-10.o\nCC\tlibavformat/codec2.o\nCC\tlibavformat/concat.o\nCC\tlibavformat/concatdec.o\nCC\tlibavformat/crcenc.o\nCC\tlibavformat/crypto.o\nCC\tlibavformat/dash.o\nCC\tlibavformat/dashenc.o\nCC\tlibavformat/data_uri.o\nCC\tlibavformat/dauddec.o\nCC\tlibavformat/daudenc.o\nCC\tlibavformat/dcstr.o\nCC\tlibavformat/demux.o\nCC\tlibavformat/demux_utils.o\nCC\tlibavformat/derf.o\nCC\tlibavformat/dfa.o\nCC\tlibavformat/dfpwmdec.o\nCC\tlibavformat/dhav.o\nCC\tlibavformat/diracdec.o\nCC\tlibavformat/dnxhddec.o\nCC\tlibavformat/dovi_isom.o\nCC\tlibavformat/dsfdec.o\nCC\tlibavformat/dsicin.o\nCC\tlibavformat/dss.o\nCC\tlibavformat/dtsdec.o\nCC\tlibavformat/dtshddec.o\nCC\tlibavformat/dump.o\nCC\tlibavformat/dv.o\nSTRIP\tlibavfilter/x86/vf_yadif.o\nCC\tlibavformat/dvbsub.o\nCC\tlibavformat/dvbtxt.o\nCC\tlibavformat/dvdclut.o\nCC\tlibavformat/dvenc.o\nCC\tlibavformat/dxa.o\nCC\tlibavformat/eacdata.o\nCC\tlibavformat/electronicarts.o\nCC\tlibavformat/epafdec.o\nCC\tlibavformat/evc.o\nCC\tlibavformat/evcdec.o\nCC\tlibavformat/ffmetadec.o\nCC\tlibavformat/ffmetaenc.o\nCC\tlibavformat/fifo.o\nCC\tlibavformat/file.o\nCC\tlibavformat/filmstripdec.o\nCC\tlibavformat/filmstripenc.o\nCC\tlibavformat/fitsdec.o\nCC\tlibavformat/fitsenc.o\nCC\tlibavformat/flac_picture.o\nCC\tlibavformat/flacdec.o\nCC\tlibavformat/flacenc.o\nCC\tlibavformat/flacenc_header.o\nCC\tlibavformat/flic.o\nCC\tlibavformat/flvdec.o\nCC\tlibavformat/flvenc.o\nCC\tlibavformat/format.o\nCC\tlibavformat/framecrcenc.o\nSTRIP\tlibavfilter/x86/vf_bwdif.o\nCC\tlibavformat/framehash.o\nCC\tlibavformat/frmdec.o\nCC\tlibavformat/fsb.o\nCC\tlibavformat/ftp.o\nCC\tlibavformat/fwse.o\nCC\tlibavformat/g722.o\nCC\tlibavformat/g723_1.o\nCC\tlibavformat/g726.o\nCC\tlibavformat/g728dec.o\nCC\tlibavformat/g729dec.o\nCC\tlibavformat/gdv.o\nCC\tlibavformat/genh.o\nCC\tlibavformat/gif.o\nCC\tlibavformat/gifdec.o\nCC\tlibavformat/gopher.o\nCC\tlibavformat/gsmdec.o\nCC\tlibavformat/gxf.o\nCC\tlibavformat/gxfenc.o\nCC\tlibavformat/h261dec.o\nCC\tlibavformat/h263dec.o\nCC\tlibavformat/h264dec.o\nCC\tlibavformat/hashenc.o\nCC\tlibavformat/hca.o\nCC\tlibavformat/hcom.o\nCC\tlibavformat/hdsenc.o\nCC\tlibavformat/hevc.o\nCC\tlibavformat/hevcdec.o\nCC\tlibavformat/hls.o\nCC\tlibavformat/hls_sample_encryption.o\nCC\tlibavformat/hlsenc.o\nCC\tlibavformat/hlsplaylist.o\nCC\tlibavformat/hlsproto.o\nCC\tlibavformat/hnm.o\nCC\tlibavformat/http.o\nCC\tlibavformat/httpauth.o\nSTRIP\tlibavfilter/x86/yadif-16.o\nCC\tlibavformat/iamf.o\nCC\tlibavformat/iamf_parse.o\nCC\tlibavformat/iamf_reader.o\nCC\tlibavformat/iamf_writer.o\nCC\tlibavformat/iamfdec.o\nCC\tlibavformat/iamfenc.o\nCC\tlibavformat/icecast.o\nCC\tlibavformat/icodec.o\nCC\tlibavformat/icoenc.o\nCC\tlibavformat/id3v1.o\nCC\tlibavformat/id3v2.o\nCC\tlibavformat/id3v2enc.o\nCC\tlibavformat/idcin.o\nCC\tlibavformat/idroqdec.o\nCC\tlibavformat/idroqenc.o\nCC\tlibavformat/iff.o\nCC\tlibavformat/ifv.o\nCC\tlibavformat/ilbc.o\nCC\tlibavformat/img2.o\nCC\tlibavformat/img2_alias_pix.o\nCC\tlibavformat/img2_brender_pix.o\nCC\tlibavformat/img2dec.o\nCC\tlibavformat/img2enc.o\nCC\tlibavformat/imx.o\nCC\tlibavformat/ingenientdec.o\nCC\tlibavformat/ip.o\nCC\tlibavformat/ipmovie.o\nCC\tlibavformat/ipudec.o\nCC\tlibavformat/ircam.o\nCC\tlibavformat/ircamdec.o\nCC\tlibavformat/ircamenc.o\nCC\tlibavformat/isom.o\nCC\tlibavformat/isom_tags.o\nCC\tlibavformat/iss.o\nCC\tlibavformat/iv8.o\nCC\tlibavformat/ivfdec.o\nCC\tlibavformat/ivfenc.o\nSTRIP\tlibavfilter/x86/vf_lut3d.o\nCC\tlibavformat/jacosubdec.o\nCC\tlibavformat/jacosubenc.o\nCC\tlibavformat/jpegxl_anim_dec.o\nCC\tlibavformat/jvdec.o\nCC\tlibavformat/kvag.o\nCC\tlibavformat/lafdec.o\nCC\tlibavformat/latmenc.o\nCC\tlibavformat/lc3.o\nCC\tlibavformat/lmlm4.o\nCC\tlibavformat/loasdec.o\nCC\tlibavformat/lrc.o\nCC\tlibavformat/lrcdec.o\nCC\tlibavformat/lrcenc.o\nCC\tlibavformat/luodatdec.o\nCC\tlibavformat/lvfdec.o\nCC\tlibavformat/lxfdec.o\nCC\tlibavformat/m4vdec.o\nCC\tlibavformat/matroska.o\nCC\tlibavformat/matroskadec.o\nCC\tlibavformat/matroskaenc.o\nCC\tlibavformat/mca.o\nCC\tlibavformat/mccdec.o\nCC\tlibavformat/md5proto.o\nCC\tlibavformat/metadata.o\nCC\tlibavformat/mgsts.o\nCC\tlibavformat/microdvddec.o\nCC\tlibavformat/microdvdenc.o\nCC\tlibavformat/mj2kdec.o\nCC\tlibavformat/mkvtimestamp_v2.o\nCC\tlibavformat/mlpdec.o\nCC\tlibavformat/mlvdec.o\nCC\tlibavformat/mm.o\nCC\tlibavformat/mmf.o\nCC\tlibavformat/mms.o\nCC\tlibavformat/mmst.o\nCC\tlibavformat/mmsh.o\nCC\tlibavformat/mods.o\nCC\tlibavformat/moflex.o\nCC\tlibavformat/mov.o\nCC\tlibavformat/mov_chan.o\nCC\tlibavformat/mov_esds.o\nCC\tlibavformat/movenc.o\nCC\tlibavformat/movenc_ttml.o\nCC\tlibavformat/movenccenc.o\nCC\tlibavformat/movenchint.o\nCC\tlibavformat/mp3dec.o\nCC\tlibavformat/mp3enc.o\nCC\tlibavformat/mpc.o\nCC\tlibavformat/mpc8.o\nCC\tlibavformat/mpeg.o\nCC\tlibavformat/mpegenc.o\nCC\tlibavformat/mpegts.o\nCC\tlibavformat/mpegtsenc.o\nCC\tlibavformat/mpegvideodec.o\nCC\tlibavformat/mpjpeg.o\nCC\tlibavformat/mpjpegdec.o\nCC\tlibavformat/mpl2dec.o\nCC\tlibavformat/mpsubdec.o\nCC\tlibavformat/msf.o\nCC\tlibavformat/msnwc_tcp.o\nCC\tlibavformat/mspdec.o\nCC\tlibavformat/mtaf.o\nCC\tlibavformat/mtv.o\nCC\tlibavformat/musx.o\nCC\tlibavformat/mux.o\nCC\tlibavformat/mux_utils.o\nCC\tlibavformat/mvdec.o\nCC\tlibavformat/mvi.o\nCC\tlibavformat/mxf.o\nCC\tlibavformat/mxfdec.o\nCC\tlibavformat/mxfenc.o\nCC\tlibavformat/mxg.o\nCC\tlibavformat/nal.o\nCC\tlibavformat/ncdec.o\nCC\tlibavformat/network.o\nCC\tlibavformat/nistspheredec.o\nCC\tlibavformat/nspdec.o\nCC\tlibavformat/nsvdec.o\nCC\tlibavformat/nullenc.o\nCC\tlibavformat/nut.o\nCC\tlibavformat/nutdec.o\nCC\tlibavformat/nutenc.o\nCC\tlibavformat/nuv.o\nCC\tlibavformat/oggdec.o\nCC\tlibavformat/oggenc.o\nCC\tlibavformat/oggparsecelt.o\nCC\tlibavformat/oggparsedirac.o\nCC\tlibavformat/oggparseflac.o\nCC\tlibavformat/oggparseogm.o\nCC\tlibavformat/oggparseopus.o\nCC\tlibavformat/oggparseskeleton.o\nCC\tlibavformat/oggparsespeex.o\nCC\tlibavformat/oggparsetheora.o\nCC\tlibavformat/oggparsevorbis.o\nCC\tlibavformat/oggparsevp8.o\nCC\tlibavformat/oma.o\nCC\tlibavformat/omadec.o\nCC\tlibavformat/omaenc.o\nCC\tlibavformat/options.o\nCC\tlibavformat/os_support.o\nCC\tlibavformat/osq.o\nCC\tlibavformat/pcm.o\nCC\tlibavformat/paf.o\nCC\tlibavformat/pcmdec.o\nCC\tlibavformat/pcmenc.o\nCC\tlibavformat/pdvdec.o\nCC\tlibavformat/pjsdec.o\nCC\tlibavformat/pmpdec.o\nCC\tlibavformat/pp_bnk.o\nCC\tlibavformat/prompeg.o\nCC\tlibavformat/protocols.o\nCC\tlibavformat/psxstr.o\nCC\tlibavformat/pva.o\nCC\tlibavformat/pvfdec.o\nCC\tlibavformat/qcp.o\nCC\tlibavformat/qoadec.o\nCC\tlibavformat/qtpalette.o\nCC\tlibavformat/r3d.o\nCC\tlibavformat/rawdec.o\nCC\tlibavformat/rawenc.o\nCC\tlibavformat/rawutils.o\nCC\tlibavformat/rawvideodec.o\nCC\tlibavformat/rcwtdec.o\nCC\tlibavformat/rcwtenc.o\nCC\tlibavformat/rdt.o\nCC\tlibavformat/realtextdec.o\nCC\tlibavformat/redspark.o\nCC\tlibavformat/replaygain.o\nCC\tlibavformat/riff.o\nCC\tlibavformat/riffdec.o\nCC\tlibavformat/riffenc.o\nCC\tlibavformat/rka.o\nCC\tlibavformat/rl2.o\nCC\tlibavformat/rm.o\nCC\tlibavformat/rmdec.o\nCC\tlibavformat/rmenc.o\nCC\tlibavformat/rmsipr.o\nCC\tlibavformat/rpl.o\nCC\tlibavformat/rsd.o\nCC\tlibavformat/rso.o\nCC\tlibavformat/rsodec.o\nCC\tlibavformat/rsoenc.o\nCC\tlibavformat/rtmpdigest.o\nCC\tlibavformat/rtmphttp.o\nCC\tlibavformat/rtmppkt.o\nCC\tlibavformat/rtmpproto.o\nCC\tlibavformat/rtp.o\nCC\tlibavformat/rtpdec.o\nCC\tlibavformat/rtpdec_ac3.o\nCC\tlibavformat/rtpdec_amr.o\nCC\tlibavformat/rtpdec_asf.o\nCC\tlibavformat/rtpdec_av1.o\nCC\tlibavformat/rtpdec_dv.o\nCC\tlibavformat/rtpdec_g726.o\nCC\tlibavformat/rtpdec_h261.o\nCC\tlibavformat/rtpdec_h263.o\nCC\tlibavformat/rtpdec_h263_rfc2190.o\nCC\tlibavformat/rtpdec_h264.o\nCC\tlibavformat/rtpdec_hevc.o\nCC\tlibavformat/rtpdec_ilbc.o\nCC\tlibavformat/rtpdec_jpeg.o\nCC\tlibavformat/rtpdec_latm.o\nCC\tlibavformat/rtpdec_mpa_robust.o\nCC\tlibavformat/rtpdec_mpeg12.o\nCC\tlibavformat/rtpdec_mpeg4.o\nCC\tlibavformat/rtpdec_mpegts.o\nCC\tlibavformat/rtpdec_opus.o\nCC\tlibavformat/rtpdec_qcelp.o\nCC\tlibavformat/rtpdec_qdm2.o\nCC\tlibavformat/rtpdec_qt.o\nCC\tlibavformat/rtpdec_rfc4175.o\nCC\tlibavformat/rtpdec_svq3.o\nCC\tlibavformat/rtpdec_vc2hq.o\nCC\tlibavformat/rtpdec_vp8.o\nCC\tlibavformat/rtpdec_vp9.o\nCC\tlibavformat/rtpdec_xiph.o\nCC\tlibavformat/rtpenc.o\nCC\tlibavformat/rtpenc_aac.o\nCC\tlibavformat/rtpenc_amr.o\nCC\tlibavformat/rtpenc_av1.o\nCC\tlibavformat/rtpenc_chain.o\nCC\tlibavformat/rtpenc_h261.o\nCC\tlibavformat/rtpenc_h263.o\nCC\tlibavformat/rtpenc_h263_rfc2190.o\nCC\tlibavformat/rtpenc_h264_hevc.o\nCC\tlibavformat/rtpenc_jpeg.o\nCC\tlibavformat/rtpenc_latm.o\nCC\tlibavformat/rtpenc_mpegts.o\nCC\tlibavformat/rtpenc_mpv.o\nCC\tlibavformat/rtpenc_rfc4175.o\nCC\tlibavformat/rtpenc_vc2hq.o\nCC\tlibavformat/rtpenc_vp8.o\nCC\tlibavformat/rtpenc_vp9.o\nCC\tlibavformat/rtpenc_xiph.o\nCC\tlibavformat/rtpproto.o\nCC\tlibavformat/rtsp.o\nCC\tlibavformat/rtspdec.o\nCC\tlibavformat/rtspenc.o\nCC\tlibavformat/s337m.o\nCC\tlibavformat/samidec.o\nCC\tlibavformat/sapdec.o\nCC\tlibavformat/sapenc.o\nCC\tlibavformat/sauce.o\nCC\tlibavformat/sbcdec.o\nCC\tlibavformat/sbgdec.o\nSTRIP\tlibavfilter/x86/colorspacedsp.o\nCC\tlibavformat/sccdec.o\nCC\tlibavformat/sccenc.o\nCC\tlibavformat/scd.o\nCC\tlibavformat/sdns.o\nCC\tlibavformat/sdp.o\nCC\tlibavformat/sdr2.o\nCC\tlibavformat/sdsdec.o\nCC\tlibavformat/sdxdec.o\nCC\tlibavformat/seek.o\nCC\tlibavformat/segafilm.o\nCC\tlibavformat/segafilmenc.o\nCC\tlibavformat/segment.o\nCC\tlibavformat/serdec.o\nCC\tlibavformat/sga.o\nCC\tlibavformat/shortendec.o\nCC\tlibavformat/sierravmd.o\nCC\tlibavformat/siff.o\nCC\tlibavformat/smacker.o\nCC\tlibavformat/smjpeg.o\nCC\tlibavformat/smjpegdec.o\nCC\tlibavformat/smjpegenc.o\nCC\tlibavformat/smoothstreamingenc.o\nCC\tlibavformat/smush.o\nCC\tlibavformat/sol.o\nCC\tlibavformat/soxdec.o\nCC\tlibavformat/soxenc.o\nCC\tlibavformat/spdif.o\nCC\tlibavformat/spdifdec.o\nCC\tlibavformat/spdifenc.o\nCC\tlibavformat/srtdec.o\nCC\tlibavformat/srtenc.o\nCC\tlibavformat/srtp.o\nCC\tlibavformat/srtpproto.o\nCC\tlibavformat/stldec.o\nCC\tlibavformat/subfile.o\nCC\tlibavformat/subtitles.o\nCC\tlibavformat/subviewer1dec.o\nCC\tlibavformat/subviewerdec.o\nCC\tlibavformat/supdec.o\nCC\tlibavformat/supenc.o\nCC\tlibavformat/svag.o\nCC\tlibavformat/svs.o\nCC\tlibavformat/swf.o\nCC\tlibavformat/swfdec.o\nCC\tlibavformat/swfenc.o\nCC\tlibavformat/takdec.o\nCC\tlibavformat/tcp.o\nCC\tlibavformat/tedcaptionsdec.o\nCC\tlibavformat/tee.o\nCC\tlibavformat/tee_common.o\nCC\tlibavformat/teeproto.o\nCC\tlibavformat/thp.o\nCC\tlibavformat/tiertexseq.o\nCC\tlibavformat/tmv.o\nCC\tlibavformat/tta.o\nCC\tlibavformat/ttaenc.o\nCC\tlibavformat/ttmlenc.o\nCC\tlibavformat/tty.o\nCC\tlibavformat/txd.o\nCC\tlibavformat/ty.o\nCC\tlibavformat/udp.o\nCC\tlibavformat/uncodedframecrcenc.o\nCC\tlibavformat/unix.o\nCC\tlibavformat/url.o\nCC\tlibavformat/urldecode.o\nCC\tlibavformat/usmdec.o\nCC\tlibavformat/utils.o\nCC\tlibavformat/vag.o\nCC\tlibavformat/vc1dec.o\nCC\tlibavformat/vc1test.o\nCC\tlibavformat/vc1testenc.o\nCC\tlibavformat/version.o\nCC\tlibavformat/vividas.o\nCC\tlibavformat/vivo.o\nCC\tlibavformat/voc.o\nCC\tlibavformat/voc_packet.o\nCC\tlibavformat/vocdec.o\nCC\tlibavformat/vocenc.o\nCC\tlibavformat/vorbiscomment.o\nCC\tlibavformat/vpcc.o\nCC\tlibavformat/vpk.o\nCC\tlibavformat/vplayerdec.o\nCC\tlibavformat/vqf.o\nCC\tlibavformat/vvc.o\nCC\tlibavformat/vvcdec.o\nCC\tlibavformat/w64.o\nCC\tlibavformat/wady.o\nCC\tlibavformat/wavarc.o\nCC\tlibavformat/wavdec.o\nCC\tlibavformat/wavenc.o\nCC\tlibavformat/wc3movie.o\nCC\tlibavformat/webm_chunk.o\nCC\tlibavformat/webmdashenc.o\nCC\tlibavformat/webpenc.o\nCC\tlibavformat/webvttdec.o\nCC\tlibavformat/webvttenc.o\nCC\tlibavformat/westwood_aud.o\nCC\tlibavformat/westwood_audenc.o\nCC\tlibavformat/westwood_vqa.o\nCC\tlibavformat/wsddec.o\nCC\tlibavformat/wtv_common.o\nCC\tlibavformat/wtvdec.o\nCC\tlibavformat/wtvenc.o\nCC\tlibavformat/wv.o\nCC\tlibavformat/wvedec.o\nCC\tlibavformat/wvdec.o\nCC\tlibavformat/wvenc.o\nCC\tlibavformat/xa.o\nCC\tlibavformat/xmd.o\nCC\tlibavformat/xmv.o\nCC\tlibavformat/xvag.o\nCC\tlibavformat/xwma.o\nCC\tlibavformat/yop.o\nCC\tlibavformat/yuv4mpegdec.o\nCC\tlibavformat/yuv4mpegenc.o\nGEN\tlibavformat/libavformat.pc\nCC\tlibavcodec/012v.o\nCC\tlibavcodec/4xm.o\nCC\tlibavcodec/8bps.o\nCC\tlibavcodec/8svx.o\nCC\tlibavcodec/a64multienc.o\nCC\tlibavcodec/aac/aacdec.o\nCC\tlibavcodec/aac/aacdec_ac.o\nCC\tlibavcodec/aac/aacdec_fixed.o\nCC\tlibavcodec/aac/aacdec_float.o\nCC\tlibavcodec/aac/aacdec_lpd.o\nCC\tlibavcodec/aac/aacdec_tab.o\nCC\tlibavcodec/aac/aacdec_usac.o\nCC\tlibavcodec/aac_ac3_parser.o\nCC\tlibavcodec/aac_parser.o\nCC\tlibavcodec/aaccoder.o\nCC\tlibavcodec/aacenc.o\nCC\tlibavcodec/aacenc_is.o\nCC\tlibavcodec/aacenc_tns.o\nCC\tlibavcodec/aacenctab.o\nCC\tlibavcodec/aacps_common.o\nCC\tlibavcodec/aacps_fixed.o\nCC\tlibavcodec/aacps_float.o\nCC\tlibavcodec/aacpsdsp_fixed.o\nCC\tlibavcodec/aacpsdsp_float.o\nCC\tlibavcodec/aacpsy.o\nCC\tlibavcodec/aacsbr.o\nCC\tlibavcodec/aacsbr_fixed.o\nCC\tlibavcodec/aactab.o\nCC\tlibavcodec/aandcttab.o\nCC\tlibavcodec/aasc.o\nCC\tlibavcodec/ac3.o\nCC\tlibavcodec/ac3_channel_layout_tab.o\nCC\tlibavcodec/ac3_parser.o\nCC\tlibavcodec/ac3dec_data.o\nCC\tlibavcodec/ac3dec_fixed.o\nCC\tlibavcodec/ac3dec_float.o\nCC\tlibavcodec/ac3dsp.o\nCC\tlibavcodec/ac3enc.o\nCC\tlibavcodec/ac3enc_fixed.o\nCC\tlibavcodec/ac3enc_float.o\nCC\tlibavcodec/ac3tab.o\nCC\tlibavcodec/acelp_filters.o\nCC\tlibavcodec/acelp_pitch_delay.o\nCC\tlibavcodec/acelp_vectors.o\nCC\tlibavcodec/adpcm.o\nCC\tlibavcodec/adpcm_data.o\nCC\tlibavcodec/adpcmenc.o\nCC\tlibavcodec/adts_header.o\nCC\tlibavcodec/adts_parser.o\nCC\tlibavcodec/adx.o\nCC\tlibavcodec/adx_parser.o\nCC\tlibavcodec/adxdec.o\nCC\tlibavcodec/adxenc.o\nCC\tlibavcodec/agm.o\nCC\tlibavcodec/aic.o\nCC\tlibavcodec/alac.o\nCC\tlibavcodec/alac_data.o\nCC\tlibavcodec/alacdsp.o\nCC\tlibavcodec/alacenc.o\nCC\tlibavcodec/aliaspixdec.o\nCC\tlibavcodec/aliaspixenc.o\nCC\tlibavcodec/allcodecs.o\nCC\tlibavcodec/alsdec.o\nCC\tlibavcodec/amr_parser.o\nCC\tlibavcodec/amrnbdec.o\nCC\tlibavcodec/amrwbdec.o\nCC\tlibavcodec/anm.o\nCC\tlibavcodec/ansi.o\nCC\tlibavcodec/aom_film_grain.o\nCC\tlibavcodec/apac.o\nCC\tlibavcodec/apedec.o\nCC\tlibavcodec/aptx.o\nCC\tlibavcodec/aptxdec.o\nCC\tlibavcodec/aptxenc.o\nCC\tlibavcodec/apv_decode.o\nCC\tlibavcodec/apv_dsp.o\nCC\tlibavcodec/apv_entropy.o\nCC\tlibavcodec/apv_parser.o\nCC\tlibavcodec/arbc.o\nCC\tlibavcodec/argo.o\nCC\tlibavcodec/ass.o\nCC\tlibavcodec/ass_split.o\nCC\tlibavcodec/assdec.o\nCC\tlibavcodec/assenc.o\nCC\tlibavcodec/asv.o\nCC\tlibavcodec/asvdec.o\nCC\tlibavcodec/asvenc.o\nCC\tlibavcodec/atrac.o\nCC\tlibavcodec/atrac1.o\nCC\tlibavcodec/atrac3.o\nCC\tlibavcodec/atrac3plus.o\nCC\tlibavcodec/atrac3plusdec.o\nCC\tlibavcodec/atrac3plusdsp.o\nCC\tlibavcodec/atrac9dec.o\nCC\tlibavcodec/atsc_a53.o\nCC\tlibavcodec/audio_frame_queue.o\nCC\tlibavcodec/audiodsp.o\nCC\tlibavcodec/aura.o\nCC\tlibavcodec/av1_parse.o\nCC\tlibavcodec/av1_parser.o\nCC\tlibavcodec/av1dec.o\nCC\tlibavcodec/avcodec.o\nCC\tlibavcodec/avdct.o\nCC\tlibavcodec/avrndec.o\nCC\tlibavcodec/avs.o\nCC\tlibavcodec/avs2.o\nCC\tlibavcodec/avs2_parser.o\nCC\tlibavcodec/avs3_parser.o\nCC\tlibavcodec/avuidec.o\nCC\tlibavcodec/avuienc.o\nCC\tlibavcodec/bethsoftvideo.o\nCC\tlibavcodec/bfi.o\nCC\tlibavcodec/bgmc.o\nCC\tlibavcodec/bink.o\nCC\tlibavcodec/binkaudio.o\nCC\tlibavcodec/binkdsp.o\nCC\tlibavcodec/bintext.o\nCC\tlibavcodec/bitpacked_dec.o\nCC\tlibavcodec/bitpacked_enc.o\nCC\tlibavcodec/bitstream.o\nCC\tlibavcodec/bitstream_filters.o\nCC\tlibavcodec/blockdsp.o\nCC\tlibavcodec/bmp.o\nCC\tlibavcodec/bmp_parser.o\nCC\tlibavcodec/bmpenc.o\nCC\tlibavcodec/bmvaudio.o\nCC\tlibavcodec/bmvvideo.o\nCC\tlibavcodec/bonk.o\nCC\tlibavcodec/brenderpix.o\nCC\tlibavcodec/bsf.o\nCC\tlibavcodec/bsf/aac_adtstoasc.o\nCC\tlibavcodec/bsf/apv_metadata.o\nCC\tlibavcodec/bsf/av1_frame_merge.o\nCC\tlibavcodec/bsf/av1_frame_split.o\nCC\tlibavcodec/bsf/av1_metadata.o\nCC\tlibavcodec/bsf/chomp.o\nCC\tlibavcodec/bsf/dca_core.o\nCC\tlibavcodec/bsf/dovi_rpu.o\nCC\tlibavcodec/bsf/dts2pts.o\nCC\tlibavcodec/bsf/dump_extradata.o\nCC\tlibavcodec/bsf/dv_error_marker.o\nCC\tlibavcodec/bsf/eac3_core.o\nCC\tlibavcodec/bsf/evc_frame_merge.o\nCC\tlibavcodec/bsf/extract_extradata.o\nCC\tlibavcodec/bsf/filter_units.o\nCC\tlibavcodec/bsf/h264_metadata.o\nCC\tlibavcodec/bsf/h264_mp4toannexb.o\nCC\tlibavcodec/bsf/h264_redundant_pps.o\nCC\tlibavcodec/bsf/h265_metadata.o\nCC\tlibavcodec/bsf/h266_metadata.o\nCC\tlibavcodec/bsf/hapqa_extract.o\nCC\tlibavcodec/bsf/hevc_mp4toannexb.o\nCC\tlibavcodec/bsf/imx_dump_header.o\nCC\tlibavcodec/bsf/media100_to_mjpegb.o\nCC\tlibavcodec/bsf/mjpeg2jpeg.o\nCC\tlibavcodec/bsf/mjpega_dump_header.o\nCC\tlibavcodec/bsf/movsub.o\nCC\tlibavcodec/bsf/mpeg2_metadata.o\nCC\tlibavcodec/bsf/mpeg4_unpack_bframes.o\nCC\tlibavcodec/bsf/noise.o\nCC\tlibavcodec/bsf/null.o\nCC\tlibavcodec/bsf/opus_metadata.o\nCC\tlibavcodec/bsf/pcm_rechunk.o\nCC\tlibavcodec/bsf/pgs_frame_merge.o\nCC\tlibavcodec/bsf/prores_metadata.o\nCC\tlibavcodec/bsf/remove_extradata.o\nCC\tlibavcodec/bsf/setts.o\nCC\tlibavcodec/bsf/showinfo.o\nCC\tlibavcodec/bsf/trace_headers.o\nCC\tlibavcodec/bsf/truehd_core.o\nCC\tlibavcodec/bsf/vp9_metadata.o\nCC\tlibavcodec/bsf/vp9_raw_reorder.o\nCC\tlibavcodec/bsf/vp9_superframe.o\nCC\tlibavcodec/bsf/vp9_superframe_split.o\nCC\tlibavcodec/bsf/vvc_mp4toannexb.o\nCC\tlibavcodec/bswapdsp.o\nCC\tlibavcodec/c93.o\nCC\tlibavcodec/cabac.o\nCC\tlibavcodec/canopus.o\nCC\tlibavcodec/cavs.o\nCC\tlibavcodec/cavs_parser.o\nCC\tlibavcodec/cavsdata.o\nCC\tlibavcodec/cavsdec.o\nCC\tlibavcodec/cavsdsp.o\nCC\tlibavcodec/cbrt_data.o\nCC\tlibavcodec/cbrt_data_fixed.o\nCC\tlibavcodec/cbs.o\nCC\tlibavcodec/cbs_apv.o\nCC\tlibavcodec/cbs_av1.o\nCC\tlibavcodec/cbs_bsf.o\nCC\tlibavcodec/cbs_h2645.o\nCC\tlibavcodec/cbs_mpeg2.o\nCC\tlibavcodec/cbs_sei.o\nCC\tlibavcodec/cbs_vp8.o\nCC\tlibavcodec/cbs_vp9.o\nCC\tlibavcodec/ccaption_dec.o\nCC\tlibavcodec/cdgraphics.o\nCC\tlibavcodec/cdtoons.o\nCC\tlibavcodec/cdxl.o\nCC\tlibavcodec/celp_filters.o\nCC\tlibavcodec/celp_math.o\nCC\tlibavcodec/cfhd.o\nCC\tlibavcodec/cfhddata.o\nCC\tlibavcodec/cfhddsp.o\nCC\tlibavcodec/cfhdenc.o\nCC\tlibavcodec/cfhdencdsp.o\nCC\tlibavcodec/cga_data.o\nCC\tlibavcodec/cinepak.o\nCC\tlibavcodec/cinepakenc.o\nCC\tlibavcodec/clearvideo.o\nCC\tlibavcodec/cljrdec.o\nCC\tlibavcodec/cljrenc.o\nCC\tlibavcodec/cllc.o\nCC\tlibavcodec/cngdec.o\nCC\tlibavcodec/cngenc.o\nCC\tlibavcodec/codec_desc.o\nCC\tlibavcodec/codec_par.o\nCC\tlibavcodec/cook.o\nCC\tlibavcodec/cook_parser.o\nCC\tlibavcodec/cpia.o\nCC\tlibavcodec/cri.o\nCC\tlibavcodec/cri_parser.o\nCC\tlibavcodec/cscd.o\nCC\tlibavcodec/cyuv.o\nCC\tlibavcodec/d3d11va.o\nCC\tlibavcodec/dca_core.o\nCC\tlibavcodec/dca.o\nCC\tlibavcodec/dca_exss.o\nCC\tlibavcodec/dca_lbr.o\nCC\tlibavcodec/dca_parser.o\nCC\tlibavcodec/dca_sample_rate_tab.o\nCC\tlibavcodec/dca_xll.o\nCC\tlibavcodec/dcaadpcm.o\nCC\tlibavcodec/dcadata.o\nCC\tlibavcodec/dcadct.o\nCC\tlibavcodec/dcadec.o\nCC\tlibavcodec/dcadsp.o\nCC\tlibavcodec/dcaenc.o\nCC\tlibavcodec/dcahuff.o\nCC\tlibavcodec/dct32_fixed.o\nCC\tlibavcodec/dct32_float.o\nCC\tlibavcodec/dds.o\nCC\tlibavcodec/decode.o\nCC\tlibavcodec/dfa.o\nCC\tlibavcodec/dfpwmdec.o\nCC\tlibavcodec/dfpwmenc.o\nCC\tlibavcodec/dirac.o\nCC\tlibavcodec/dirac_arith.o\nCC\tlibavcodec/dirac_dwt.o\nCC\tlibavcodec/dirac_parser.o\nCC\tlibavcodec/dirac_vlc.o\nCC\tlibavcodec/diracdec.o\nCC\tlibavcodec/diracdsp.o\nCC\tlibavcodec/diractab.o\nCC\tlibavcodec/dnxhd_parser.o\nCC\tlibavcodec/dnxhddata.o\nCC\tlibavcodec/dnxhddec.o\nCC\tlibavcodec/dnxhdenc.o\nCC\tlibavcodec/dnxuc_parser.o\nCC\tlibavcodec/dolby_e.o\nCC\tlibavcodec/dolby_e_parse.o\nCC\tlibavcodec/dolby_e_parser.o\nCC\tlibavcodec/dovi_rpu.o\nCC\tlibavcodec/dovi_rpudec.o\nCC\tlibavcodec/dovi_rpuenc.o\nCC\tlibavcodec/dpcm.o\nCC\tlibavcodec/dpx.o\nCC\tlibavcodec/dpx_parser.o\nCC\tlibavcodec/dpxenc.o\nCC\tlibavcodec/dsd.o\nCC\tlibavcodec/dsddec.o\nCC\tlibavcodec/dsicinaudio.o\nCC\tlibavcodec/dsicinvideo.o\nCC\tlibavcodec/dss_sp.o\nCC\tlibavcodec/dstdec.o\nCC\tlibavcodec/dv.o\nCC\tlibavcodec/dv_profile.o\nCC\tlibavcodec/dvaudio_parser.o\nCC\tlibavcodec/dvaudiodec.o\nCC\tlibavcodec/dvbsub_parser.o\nCC\tlibavcodec/dvbsubdec.o\nCC\tlibavcodec/dvbsubenc.o\nCC\tlibavcodec/dvd_nav_parser.o\nCC\tlibavcodec/dvdata.o\nCC\tlibavcodec/dvdec.o\nCC\tlibavcodec/dvdsub.o\nCC\tlibavcodec/dvdsub_parser.o\nCC\tlibavcodec/dvdsubdec.o\nCC\tlibavcodec/dvdsubenc.o\nCC\tlibavcodec/dvenc.o\nCC\tlibavcodec/dxtory.o\nCC\tlibavcodec/dxv.o\nCC\tlibavcodec/dxvenc.o\nCC\tlibavcodec/dynamic_hdr_vivid.o\nCC\tlibavcodec/eac3_data.o\nCC\tlibavcodec/eac3enc.o\nCC\tlibavcodec/eacmv.o\nCC\tlibavcodec/eaidct.o\nCC\tlibavcodec/eamad.o\nCC\tlibavcodec/eatgq.o\nCC\tlibavcodec/eatgv.o\nCC\tlibavcodec/eatqi.o\nCC\tlibavcodec/elbg.o\nCC\tlibavcodec/encode.o\nCC\tlibavcodec/error_resilience.o\nCC\tlibavcodec/escape124.o\nCC\tlibavcodec/escape130.o\nCC\tlibavcodec/evc_parse.o\nCC\tlibavcodec/evc_parser.o\nCC\tlibavcodec/evc_ps.o\nCC\tlibavcodec/evrcdec.o\nCC\tlibavcodec/executor.o\nCC\tlibavcodec/exif.o\nCC\tlibavcodec/faandct.o\nCC\tlibavcodec/faanidct.o\nCC\tlibavcodec/fastaudio.o\nCC\tlibavcodec/faxcompr.o\nCC\tlibavcodec/fdctdsp.o\nCC\tlibavcodec/ffv1.o\nCC\tlibavcodec/ffv1_parse.o\nCC\tlibavcodec/ffv1_parser.o\nCC\tlibavcodec/ffv1dec.o\nCC\tlibavcodec/ffv1enc.o\nCC\tlibavcodec/ffwavesynth.o\nCC\tlibavcodec/fic.o\nCC\tlibavcodec/fits.o\nCC\tlibavcodec/fitsdec.o\nCC\tlibavcodec/fitsenc.o\nCC\tlibavcodec/flac.o\nCC\tlibavcodec/flac_parser.o\nCC\tlibavcodec/flacdata.o\nCC\tlibavcodec/flacdec.o\nCC\tlibavcodec/flacdsp.o\nCC\tlibavcodec/flacenc.o\nCC\tlibavcodec/flacencdsp.o\nCC\tlibavcodec/flicvideo.o\nCC\tlibavcodec/flvdec.o\nCC\tlibavcodec/flvenc.o\nCC\tlibavcodec/fmtconvert.o\nCC\tlibavcodec/fmvc.o\nCC\tlibavcodec/frame_thread_encoder.o\nCC\tlibavcodec/fraps.o\nCC\tlibavcodec/frwu.o\nCC\tlibavcodec/ftr.o\nCC\tlibavcodec/ftr_parser.o\nCC\tlibavcodec/g722.o\nCC\tlibavcodec/g722dec.o\nCC\tlibavcodec/g722dsp.o\nCC\tlibavcodec/g722enc.o\nCC\tlibavcodec/g723_1.o\nCC\tlibavcodec/g723_1_parser.o\nCC\tlibavcodec/g723_1dec.o\nCC\tlibavcodec/g723_1enc.o\nCC\tlibavcodec/g726.o\nCC\tlibavcodec/g728dec.o\nCC\tlibavcodec/g729_parser.o\nCC\tlibavcodec/g729dec.o\nCC\tlibavcodec/g729postfilter.o\nCC\tlibavcodec/gdv.o\nCC\tlibavcodec/gemdec.o\nCC\tlibavcodec/get_buffer.o\nCC\tlibavcodec/gif.o\nCC\tlibavcodec/gif_parser.o\nCC\tlibavcodec/gifdec.o\nCC\tlibavcodec/golomb.o\nCC\tlibavcodec/gsm_parser.o\nCC\tlibavcodec/gsmdec.o\nCC\tlibavcodec/gsmdec_data.o\nCC\tlibavcodec/h261.o\nCC\tlibavcodec/h261_parser.o\nCC\tlibavcodec/h261data.o\nCC\tlibavcodec/h261dec.o\nCC\tlibavcodec/h261enc.o\nCC\tlibavcodec/h263.o\nCC\tlibavcodec/h263_parser.o\nCC\tlibavcodec/h263data.o\nCC\tlibavcodec/h263dec.o\nCC\tlibavcodec/h263dsp.o\nCC\tlibavcodec/h2645_parse.o\nCC\tlibavcodec/h2645_sei.o\nCC\tlibavcodec/h2645_vui.o\nCC\tlibavcodec/h2645data.o\nCC\tlibavcodec/h264_cabac.o\nCC\tlibavcodec/h264_cavlc.o\nCC\tlibavcodec/h264_direct.o\nCC\tlibavcodec/h264_levels.o\nCC\tlibavcodec/h264_loopfilter.o\nCC\tlibavcodec/h264_mb.o\nCC\tlibavcodec/h264_parse.o\nCC\tlibavcodec/h264_parser.o\nCC\tlibavcodec/h264_picture.o\nCC\tlibavcodec/h264_ps.o\nCC\tlibavcodec/h264_refs.o\nCC\tlibavcodec/h264_sei.o\nCC\tlibavcodec/h264_slice.o\nCC\tlibavcodec/h264chroma.o\nCC\tlibavcodec/h264data.o\nCC\tlibavcodec/h264dec.o\nCC\tlibavcodec/h264dsp.o\nCC\tlibavcodec/h264idct.o\nCC\tlibavcodec/h264pred.o\nCC\tlibavcodec/h264qpel.o\nCC\tlibavcodec/h265_profile_level.o\nCC\tlibavcodec/h274.o\nCC\tlibavcodec/hap.o\nCC\tlibavcodec/hapdec.o\nCC\tlibavcodec/hashtable.o\nCC\tlibavcodec/hcadec.o\nCC\tlibavcodec/hcom.o\nCC\tlibavcodec/hdr_parser.o\nCC\tlibavcodec/hdrdec.o\nCC\tlibavcodec/hdrenc.o\nCC\tlibavcodec/hevc/cabac.o\nCC\tlibavcodec/hevc/data.o\nCC\tlibavcodec/hevc/dsp.o\nCC\tlibavcodec/hevc/filter.o\nCC\tlibavcodec/hevc/hevcdec.o\nCC\tlibavcodec/hevc/mvs.o\nCC\tlibavcodec/hevc/parse.o\nCC\tlibavcodec/hevc/parser.o\nCC\tlibavcodec/hevc/pred.o\nCC\tlibavcodec/hevc/ps.o\nCC\tlibavcodec/hevc/refs.o\nCC\tlibavcodec/hevc/sei.o\nCC\tlibavcodec/hnm4video.o\nCC\tlibavcodec/hpeldsp.o\nCC\tlibavcodec/hq_common.o\nCC\tlibavcodec/hq_hqa.o\nCC\tlibavcodec/hq_hqadsp.o\nCC\tlibavcodec/hqx.o\nCC\tlibavcodec/hqxdsp.o\nCC\tlibavcodec/htmlsubtitles.o\nCC\tlibavcodec/huffman.o\nCC\tlibavcodec/huffyuv.o\nCC\tlibavcodec/huffyuvdec.o\nCC\tlibavcodec/huffyuvdsp.o\nCC\tlibavcodec/huffyuvenc.o\nCC\tlibavcodec/huffyuvencdsp.o\nCC\tlibavcodec/idcinvideo.o\nCC\tlibavcodec/idctdsp.o\nCC\tlibavcodec/iff.o\nCC\tlibavcodec/ilbcdec.o\nCC\tlibavcodec/imc.o\nCC\tlibavcodec/imgconvert.o\nCC\tlibavcodec/imm4.o\nCC\tlibavcodec/imm5.o\nCC\tlibavcodec/imx.o\nCC\tlibavcodec/indeo2.o\nCC\tlibavcodec/indeo3.o\nCC\tlibavcodec/indeo4.o\nCC\tlibavcodec/indeo5.o\nCC\tlibavcodec/intelh263dec.o\nCC\tlibavcodec/interplayacm.o\nCC\tlibavcodec/interplayvideo.o\nCC\tlibavcodec/intrax8.o\nCC\tlibavcodec/intrax8dsp.o\nCC\tlibavcodec/ipu_parser.o\nCC\tlibavcodec/ituh263dec.o\nCC\tlibavcodec/ituh263enc.o\nCC\tlibavcodec/ivi.o\nCC\tlibavcodec/ivi_dsp.o\nCC\tlibavcodec/j2kenc.o\nCC\tlibavcodec/jacosubdec.o\nCC\tlibavcodec/jfdctfst.o\nCC\tlibavcodec/jfdctint.o\nCC\tlibavcodec/jni.o\nCC\tlibavcodec/jpeg2000.o\nCC\tlibavcodec/jpeg2000_parser.o\nCC\tlibavcodec/jpeg2000dec.o\nCC\tlibavcodec/jpeg2000dsp.o\nCC\tlibavcodec/jpeg2000dwt.o\nCC\tlibavcodec/jpeg2000htdec.o\nCC\tlibavcodec/jpegls.o\nCC\tlibavcodec/jpeglsdec.o\nCC\tlibavcodec/jpeglsenc.o\nCC\tlibavcodec/jpegquanttables.o\nCC\tlibavcodec/jpegtables.o\nCC\tlibavcodec/jpegxl_parse.o\nCC\tlibavcodec/jpegxl_parser.o\nCC\tlibavcodec/jrevdct.o\nCC\tlibavcodec/jvdec.o\nCC\tlibavcodec/kbdwin.o\nCC\tlibavcodec/kgv1dec.o\nCC\tlibavcodec/kmvc.o\nCC\tlibavcodec/lagarith.o\nCC\tlibavcodec/lagarithrac.o\nCC\tlibavcodec/latm_parser.o\nCC\tlibavcodec/lcevcdec.o\nCC\tlibavcodec/lcldec.o\nCC\tlibavcodec/leaddec.o\nCC\tlibavcodec/ljpegenc.o\nCC\tlibavcodec/loco.o\nCC\tlibavcodec/lossless_audiodsp.o\nCC\tlibavcodec/lossless_videodsp.o\nCC\tlibavcodec/lossless_videoencdsp.o\nCC\tlibavcodec/lpc.o\nCC\tlibavcodec/lsp.o\nCC\tlibavcodec/lzf.o\nCC\tlibavcodec/lzw.o\nCC\tlibavcodec/lzwenc.o\nCC\tlibavcodec/m101.o\nCC\tlibavcodec/mace.o\nCC\tlibavcodec/magicyuv.o\nCC\tlibavcodec/magicyuvenc.o\nCC\tlibavcodec/mathtables.o\nCC\tlibavcodec/mdec.o\nCC\tlibavcodec/me_cmp.o\nCC\tlibavcodec/mediacodec.o\nCC\tlibavcodec/metasound.o\nCC\tlibavcodec/microdvddec.o\nCC\tlibavcodec/midivid.o\nCC\tlibavcodec/mimic.o\nCC\tlibavcodec/misc4.o\nCC\tlibavcodec/misc4_parser.o\nCC\tlibavcodec/mjpeg_parser.o\nCC\tlibavcodec/mjpegbdec.o\nCC\tlibavcodec/mjpegdec.o\nCC\tlibavcodec/mjpegdec_common.o\nCC\tlibavcodec/mjpegenc.o\nCC\tlibavcodec/mjpegenc_common.o\nCC\tlibavcodec/mjpegenc_huffman.o\nCC\tlibavcodec/mlp.o\nCC\tlibavcodec/mlp_parse.o\nCC\tlibavcodec/mlp_parser.o\nCC\tlibavcodec/mlpdec.o\nCC\tlibavcodec/mlpdsp.o\nCC\tlibavcodec/mlpenc.o\nCC\tlibavcodec/mlz.o\nCC\tlibavcodec/mmvideo.o\nCC\tlibavcodec/mobiclip.o\nCC\tlibavcodec/motion_est.o\nCC\tlibavcodec/motionpixels.o\nCC\tlibavcodec/movtextdec.o\nCC\tlibavcodec/movtextenc.o\nCC\tlibavcodec/mpc.o\nCC\tlibavcodec/mpc7.o\nCC\tlibavcodec/mpc8.o\nCC\tlibavcodec/mpeg12.o\nCC\tlibavcodec/mpeg12data.o\nCC\tlibavcodec/mpeg12dec.o\nCC\tlibavcodec/mpeg12enc.o\nCC\tlibavcodec/mpeg12framerate.o\nCC\tlibavcodec/mpeg4audio.o\nCC\tlibavcodec/mpeg4audio_sample_rates.o\nCC\tlibavcodec/mpeg4video.o\nCC\tlibavcodec/mpeg4video_parser.o\nCC\tlibavcodec/mpeg4videodec.o\nCC\tlibavcodec/mpeg4videodsp.o\nCC\tlibavcodec/mpeg4videoenc.o\nCC\tlibavcodec/mpeg_er.o\nCC\tlibavcodec/mpegaudio.o\nCC\tlibavcodec/mpegaudio_parser.o\nCC\tlibavcodec/mpegaudiodata.o\nCC\tlibavcodec/mpegaudiodec_common.o\nCC\tlibavcodec/mpegaudiodec_fixed.o\nCC\tlibavcodec/mpegaudiodec_float.o\nCC\tlibavcodec/mpegaudiodecheader.o\nCC\tlibavcodec/mpegaudiodsp.o\nCC\tlibavcodec/mpegaudiodsp_data.o\nCC\tlibavcodec/mpegaudiodsp_fixed.o\nCC\tlibavcodec/mpegaudiodsp_float.o\nCC\tlibavcodec/mpegaudioenc.o\nCC\tlibavcodec/mpegaudiotabs.o\nCC\tlibavcodec/mpegpicture.o\nCC\tlibavcodec/mpegutils.o\nCC\tlibavcodec/mpegvideo.o\nCC\tlibavcodec/mpegvideo_dec.o\nCC\tlibavcodec/mpegvideo_enc.o\nCC\tlibavcodec/mpegvideo_motion.o\nCC\tlibavcodec/mpegvideo_parser.o\nCC\tlibavcodec/mpegvideo_unquantize.o\nCC\tlibavcodec/mpegvideodata.o\nCC\tlibavcodec/mpegvideoencdsp.o\nCC\tlibavcodec/mpl2dec.o\nCC\tlibavcodec/mqc.o\nCC\tlibavcodec/mqcdec.o\nCC\tlibavcodec/mqcenc.o\nCC\tlibavcodec/msgsmdec.o\nCC\tlibavcodec/msmpeg4.o\nCC\tlibavcodec/msmpeg4_vc1_data.o\nCC\tlibavcodec/msmpeg4data.o\nCC\tlibavcodec/msmpeg4dec.o\nCC\tlibavcodec/msmpeg4enc.o\nCC\tlibavcodec/msp2dec.o\nCC\tlibavcodec/msrle.o\nCC\tlibavcodec/msrledec.o\nCC\tlibavcodec/msrleenc.o\nCC\tlibavcodec/mss1.o\nCC\tlibavcodec/mss12.o\nCC\tlibavcodec/mss2.o\nCC\tlibavcodec/mss2dsp.o\nCC\tlibavcodec/mss3.o\nCC\tlibavcodec/mss34dsp.o\nCC\tlibavcodec/mss4.o\nCC\tlibavcodec/msvideo1enc.o\nCC\tlibavcodec/msvideo1.o\nCC\tlibavcodec/mv30.o\nCC\tlibavcodec/mvcdec.o\nCC\tlibavcodec/mxpegdec.o\nCC\tlibavcodec/nellymoser.o\nCC\tlibavcodec/nellymoserdec.o\nCC\tlibavcodec/nellymoserenc.o\nCC\tlibavcodec/notchlc.o\nCC\tlibavcodec/null.o\nCC\tlibavcodec/nuv.o\nCC\tlibavcodec/on2avc.o\nCC\tlibavcodec/on2avcdata.o\nCC\tlibavcodec/options.o\nCC\tlibavcodec/opus/celt.o\nCC\tlibavcodec/opus/dec.o\nCC\tlibavcodec/opus/dec_celt.o\nCC\tlibavcodec/opus/dsp.o\nCC\tlibavcodec/opus/enc.o\nCC\tlibavcodec/opus/enc_psy.o\nCC\tlibavcodec/opus/parse.o\nCC\tlibavcodec/opus/parser.o\nCC\tlibavcodec/opus/pvq.o\nCC\tlibavcodec/opus/rc.o\nCC\tlibavcodec/opus/silk.o\nCC\tlibavcodec/opus/tab.o\nCC\tlibavcodec/osq.o\nCC\tlibavcodec/packet.o\nCC\tlibavcodec/pafaudio.o\nCC\tlibavcodec/pafvideo.o\nCC\tlibavcodec/pamenc.o\nCC\tlibavcodec/parser.o\nCC\tlibavcodec/parsers.o\nCC\tlibavcodec/pcm-bluray.o\nCC\tlibavcodec/pcm-blurayenc.o\nCC\tlibavcodec/pcm-dvd.o\nCC\tlibavcodec/pcm-dvdenc.o\nCC\tlibavcodec/pcm.o\nCC\tlibavcodec/pcx.o\nCC\tlibavcodec/pcxenc.o\nCC\tlibavcodec/pgssubdec.o\nCC\tlibavcodec/pgxdec.o\nCC\tlibavcodec/photocd.o\nCC\tlibavcodec/pictordec.o\nCC\tlibavcodec/pixblockdsp.o\nCC\tlibavcodec/pixlet.o\nCC\tlibavcodec/png_parser.o\nCC\tlibavcodec/pnm.o\nCC\tlibavcodec/pnm_parser.o\nCC\tlibavcodec/pnmdec.o\nCC\tlibavcodec/pnmenc.o\nCC\tlibavcodec/profiles.o\nCC\tlibavcodec/proresdata.o\nCC\tlibavcodec/proresdec.o\nCC\tlibavcodec/proresdsp.o\nCC\tlibavcodec/proresenc_anatoliy.o\nCC\tlibavcodec/proresenc_kostya.o\nCC\tlibavcodec/prosumer.o\nCC\tlibavcodec/psd.o\nCC\tlibavcodec/psymodel.o\nCC\tlibavcodec/pthread.o\nCC\tlibavcodec/pthread_frame.o\nCC\tlibavcodec/pthread_slice.o\nCC\tlibavcodec/ptx.o\nCC\tlibavcodec/qcelpdec.o\nCC\tlibavcodec/qdm2.o\nCC\tlibavcodec/qdmc.o\nCC\tlibavcodec/qdrw.o\nCC\tlibavcodec/qoadec.o\nCC\tlibavcodec/qoi_parser.o\nCC\tlibavcodec/qoidec.o\nCC\tlibavcodec/qoienc.o\nCC\tlibavcodec/qpeg.o\nCC\tlibavcodec/qpeldsp.o\nCC\tlibavcodec/qsv_api.o\nCC\tlibavcodec/qtrle.o\nCC\tlibavcodec/qtrleenc.o\nCC\tlibavcodec/r210dec.o\nCC\tlibavcodec/r210enc.o\nCC\tlibavcodec/ra144.o\nCC\tlibavcodec/ra144dec.o\nCC\tlibavcodec/ra144enc.o\nCC\tlibavcodec/ra288.o\nCC\tlibavcodec/ralf.o\nCC\tlibavcodec/rangecoder.o\nCC\tlibavcodec/ratecontrol.o\nCC\tlibavcodec/raw.o\nCC\tlibavcodec/rawdec.o\nCC\tlibavcodec/rawenc.o\nCC\tlibavcodec/realtextdec.o\nCC\tlibavcodec/rka.o\nCC\tlibavcodec/rl.o\nCC\tlibavcodec/rl2.o\nCC\tlibavcodec/rle.o\nCC\tlibavcodec/roqaudioenc.o\nCC\tlibavcodec/roqvideo.o\nCC\tlibavcodec/roqvideodec.o\nCC\tlibavcodec/roqvideoenc.o\nCC\tlibavcodec/rpza.o\nCC\tlibavcodec/rpzaenc.o\nCC\tlibavcodec/rtjpeg.o\nCC\tlibavcodec/rtv1.o\nCC\tlibavcodec/rv10.o\nCC\tlibavcodec/rv10enc.o\nCC\tlibavcodec/rv20enc.o\nCC\tlibavcodec/rv30.o\nCC\tlibavcodec/rv30dsp.o\nCC\tlibavcodec/rv34.o\nCC\tlibavcodec/rv34_parser.o\nCC\tlibavcodec/rv34dsp.o\nCC\tlibavcodec/rv40.o\nCC\tlibavcodec/rv40dsp.o\nCC\tlibavcodec/rv60dec.o\nCC\tlibavcodec/rv60dsp.o\nCC\tlibavcodec/s302m.o\nCC\tlibavcodec/s302menc.o\nCC\tlibavcodec/samidec.o\nCC\tlibavcodec/sanm.o\nCC\tlibavcodec/sbc.o\nCC\tlibavcodec/sbc_parser.o\nCC\tlibavcodec/sbcdec.o\nCC\tlibavcodec/sbcdsp.o\nCC\tlibavcodec/sbcenc.o\nCC\tlibavcodec/sbrdsp.o\nCC\tlibavcodec/sbrdsp_fixed.o\nCC\tlibavcodec/scpr.o\nCC\tlibavcodec/sga.o\nCC\tlibavcodec/sgidec.o\nCC\tlibavcodec/sgienc.o\nCC\tlibavcodec/sgirledec.o\nCC\tlibavcodec/sheervideo.o\nCC\tlibavcodec/shorten.o\nCC\tlibavcodec/simple_idct.o\nCC\tlibavcodec/sinewin.o\nCC\tlibavcodec/sipr.o\nCC\tlibavcodec/sipr16k.o\nCC\tlibavcodec/sipr_parser.o\nCC\tlibavcodec/siren.o\nCC\tlibavcodec/smacker.o\nCC\tlibavcodec/smc.o\nCC\tlibavcodec/smcenc.o\nCC\tlibavcodec/snappy.o\nCC\tlibavcodec/snow.o\nCC\tlibavcodec/snow_dwt.o\nCC\tlibavcodec/snowdec.o\nCC\tlibavcodec/snowenc.o\nCC\tlibavcodec/sonic.o\nCC\tlibavcodec/sp5xdec.o\nCC\tlibavcodec/speedhq.o\nCC\tlibavcodec/speedhqdec.o\nCC\tlibavcodec/speedhqenc.o\nCC\tlibavcodec/speexdec.o\nCC\tlibavcodec/srtdec.o\nCC\tlibavcodec/srtenc.o\nCC\tlibavcodec/startcode.o\nCC\tlibavcodec/subviewerdec.o\nCC\tlibavcodec/sunrast.o\nCC\tlibavcodec/sunrastenc.o\nCC\tlibavcodec/svq1.o\nCC\tlibavcodec/svq1dec.o\nCC\tlibavcodec/svq1enc.o\nCC\tlibavcodec/svq3.o\nCC\tlibavcodec/synth_filter.o\nCC\tlibavcodec/tak.o\nCC\tlibavcodec/tak_parser.o\nCC\tlibavcodec/takdec.o\nCC\tlibavcodec/takdsp.o\nCC\tlibavcodec/targa.o\nCC\tlibavcodec/targa_y216dec.o\nCC\tlibavcodec/targaenc.o\nCC\tlibavcodec/textdec.o\nCC\tlibavcodec/texturedsp.o\nCC\tlibavcodec/texturedspenc.o\nCC\tlibavcodec/threadprogress.o\nCC\tlibavcodec/tiertexseqv.o\nCC\tlibavcodec/tiff.o\nCC\tlibavcodec/tiff_common.o\nCC\tlibavcodec/tiffenc.o\nCC\tlibavcodec/tmv.o\nCC\tlibavcodec/to_upper4.o\nCC\tlibavcodec/tpeldsp.o\nCC\tlibavcodec/truemotion1.o\nCC\tlibavcodec/truemotion2.o\nCC\tlibavcodec/truemotion2rt.o\nCC\tlibavcodec/truespeech.o\nCC\tlibavcodec/tscc2.o\nCC\tlibavcodec/tta.o\nCC\tlibavcodec/ttadata.o\nCC\tlibavcodec/ttadsp.o\nCC\tlibavcodec/ttaenc.o\nCC\tlibavcodec/ttaencdsp.o\nCC\tlibavcodec/ttmlenc.o\nCC\tlibavcodec/twinvq.o\nCC\tlibavcodec/twinvqdec.o\nCC\tlibavcodec/txd.o\nCC\tlibavcodec/ulti.o\nCC\tlibavcodec/utils.o\nCC\tlibavcodec/utvideodec.o\nCC\tlibavcodec/utvideodsp.o\nCC\tlibavcodec/utvideoenc.o\nCC\tlibavcodec/v210dec.o\nCC\tlibavcodec/v210enc.o\nCC\tlibavcodec/v210x.o\nCC\tlibavcodec/v308dec.o\nCC\tlibavcodec/v308enc.o\nCC\tlibavcodec/v408dec.o\nCC\tlibavcodec/v408enc.o\nCC\tlibavcodec/v410dec.o\nCC\tlibavcodec/v410enc.o\nCC\tlibavcodec/v4l2_buffers.o\nCC\tlibavcodec/v4l2_context.o\nCC\tlibavcodec/v4l2_fmt.o\nCC\tlibavcodec/v4l2_m2m.o\nCC\tlibavcodec/v4l2_m2m_dec.o\nCC\tlibavcodec/v4l2_m2m_enc.o\nCC\tlibavcodec/vb.o\nCC\tlibavcodec/vble.o\nCC\tlibavcodec/vbndec.o\nCC\tlibavcodec/vbnenc.o\nCC\tlibavcodec/vc1.o\nCC\tlibavcodec/vc1_block.o\nCC\tlibavcodec/vc1_loopfilter.o\nCC\tlibavcodec/vc1_mc.o\nCC\tlibavcodec/vc1_parser.o\nCC\tlibavcodec/vc1_pred.o\nCC\tlibavcodec/vc1data.o\nCC\tlibavcodec/vc1dec.o\nCC\tlibavcodec/vc1dsp.o\nCC\tlibavcodec/vc2enc.o\nCC\tlibavcodec/vc2enc_dwt.o\nCC\tlibavcodec/vcr1.o\nCC\tlibavcodec/version.o\nCC\tlibavcodec/videodsp.o\nCC\tlibavcodec/vima.o\nCC\tlibavcodec/vlc.o\nCC\tlibavcodec/vmdaudio.o\nCC\tlibavcodec/vmdvideo.o\nCC\tlibavcodec/vmixdec.o\nCC\tlibavcodec/vmnc.o\nCC\tlibavcodec/vorbis.o\nCC\tlibavcodec/vorbis_data.o\nCC\tlibavcodec/vorbis_parser.o\nCC\tlibavcodec/vorbisdec.o\nCC\tlibavcodec/vorbisdsp.o\nCC\tlibavcodec/vorbisenc.o\nCC\tlibavcodec/vp3.o\nCC\tlibavcodec/vp3_parser.o\nCC\tlibavcodec/vp3dsp.o\nCC\tlibavcodec/vp5.o\nCC\tlibavcodec/vp56.o\nCC\tlibavcodec/vp56data.o\nCC\tlibavcodec/vp56dsp.o\nCC\tlibavcodec/vp6.o\nCC\tlibavcodec/vp6dsp.o\nCC\tlibavcodec/vp8.o\nCC\tlibavcodec/vp8_parser.o\nCC\tlibavcodec/vp8data.o\nCC\tlibavcodec/vp8dsp.o\nCC\tlibavcodec/vp9.o\nCC\tlibavcodec/vp9_parser.o\nCC\tlibavcodec/vp9block.o\nCC\tlibavcodec/vp9data.o\nCC\tlibavcodec/vp9dsp.o\nCC\tlibavcodec/vp9dsp_10bpp.o\nCC\tlibavcodec/vp9dsp_12bpp.o\nCC\tlibavcodec/vp9dsp_8bpp.o\nCC\tlibavcodec/vp9lpf.o\nCC\tlibavcodec/vp9mvs.o\nCC\tlibavcodec/vp9prob.o\nCC\tlibavcodec/vp9recon.o\nCC\tlibavcodec/vpx_rac.o\nCC\tlibavcodec/vqavideo.o\nCC\tlibavcodec/vqcdec.o\nCC\tlibavcodec/vvc/cabac.o\nCC\tlibavcodec/vvc/ctu.o\nCC\tlibavcodec/vvc/data.o\nCC\tlibavcodec/vvc/dec.o\nCC\tlibavcodec/vvc/dsp.o\nCC\tlibavcodec/vvc/filter.o\nCC\tlibavcodec/vvc/inter.o\nCC\tlibavcodec/vvc/intra.o\nCC\tlibavcodec/vvc/intra_utils.o\nCC\tlibavcodec/vvc/itx_1d.o\nCC\tlibavcodec/vvc/mvs.o\nCC\tlibavcodec/vvc/ps.o\nCC\tlibavcodec/vvc/refs.o\nCC\tlibavcodec/vvc/sei.o\nCC\tlibavcodec/vvc/thread.o\nCC\tlibavcodec/vvc_parser.o\nCC\tlibavcodec/wavarc.o\nCC\tlibavcodec/wavpack.o\nCC\tlibavcodec/wavpackdata.o\nCC\tlibavcodec/wavpackenc.o\nCC\tlibavcodec/wbmpdec.o\nCC\tlibavcodec/wbmpenc.o\nCC\tlibavcodec/webp.o\nCC\tlibavcodec/webp_parser.o\nCC\tlibavcodec/webvttdec.o\nCC\tlibavcodec/webvttenc.o\nCC\tlibavcodec/wma.o\nCC\tlibavcodec/wma_common.o\nCC\tlibavcodec/wma_freqs.o\nCC\tlibavcodec/wmadec.o\nCC\tlibavcodec/wmaenc.o\nCC\tlibavcodec/wmalosslessdec.o\nCC\tlibavcodec/wmaprodec.o\nCC\tlibavcodec/wmavoice.o\nCC\tlibavcodec/wmv2.o\nCC\tlibavcodec/wmv2data.o\nCC\tlibavcodec/wmv2dec.o\nCC\tlibavcodec/wmv2dsp.o\nCC\tlibavcodec/wmv2enc.o\nCC\tlibavcodec/wnv1.o\nCC\tlibavcodec/wrapped_avframe.o\nCC\tlibavcodec/ws-snd1.o\nX86ASM\tlibavcodec/x86/aacencdsp.o\nCC\tlibavcodec/x86/aacencdsp_init.o\nX86ASM\tlibavcodec/x86/aacpsdsp.o\nSTRIP\tlibavcodec/x86/aacencdsp.o\nCC\tlibavcodec/x86/aacpsdsp_init.o\nX86ASM\tlibavcodec/x86/ac3dsp.o\nX86ASM\tlibavcodec/x86/ac3dsp_downmix.o\nCC\tlibavcodec/x86/ac3dsp_init.o\nX86ASM\tlibavcodec/x86/alacdsp.o\nCC\tlibavcodec/x86/alacdsp_init.o\nX86ASM\tlibavcodec/x86/apv_dsp.o\nCC\tlibavcodec/x86/apv_dsp_init.o\nX86ASM\tlibavcodec/x86/audiodsp.o\nCC\tlibavcodec/x86/audiodsp_init.o\nX86ASM\tlibavcodec/x86/blockdsp.o\nCC\tlibavcodec/x86/blockdsp_init.o\nX86ASM\tlibavcodec/x86/bswapdsp.o\nCC\tlibavcodec/x86/bswapdsp_init.o\nCC\tlibavcodec/x86/cavsdsp.o\nX86ASM\tlibavcodec/x86/cavsidct.o\nSTRIP\tlibavcodec/x86/ac3dsp.o\nCC\tlibavcodec/x86/celt_pvq_init.o\nSTRIP\tlibavcodec/x86/apv_dsp.o\nSTRIP\tlibavcodec/x86/alacdsp.o\nX86ASM\tlibavcodec/x86/celt_pvq_search.o\nX86ASM\tlibavcodec/x86/cfhddsp.o\nCC\tlibavcodec/x86/cfhddsp_init.o\nX86ASM\tlibavcodec/x86/cfhdencdsp.o\nSTRIP\tlibavcodec/x86/blockdsp.o\nCC\tlibavcodec/x86/cfhdencdsp_init.o\nCC\tlibavcodec/x86/constants.o\nSTRIP\tlibavcodec/x86/audiodsp.o\nX86ASM\tlibavcodec/x86/dcadsp.o\nCC\tlibavcodec/x86/dcadsp_init.o\nX86ASM\tlibavcodec/x86/dct32.o\nX86ASM\tlibavcodec/x86/dirac_dwt.o\nCC\tlibavcodec/x86/dirac_dwt_init.o\nSTRIP\tlibavcodec/x86/bswapdsp.o\nX86ASM\tlibavcodec/x86/diracdsp.o\nSTRIP\tlibavcodec/x86/cavsidct.o\nCC\tlibavcodec/x86/diracdsp_init.o\nX86ASM\tlibavcodec/x86/dnxhdenc.o\nSTRIP\tlibavcodec/x86/cfhdencdsp.o\nCC\tlibavcodec/x86/dnxhdenc_init.o\nCC\tlibavcodec/x86/fdct.o\nCC\tlibavcodec/x86/fdctdsp_init.o\nX86ASM\tlibavcodec/x86/flacdsp.o\nSTRIP\tlibavcodec/x86/aacpsdsp.o\nCC\tlibavcodec/x86/flacdsp_init.o\nSTRIP\tlibavcodec/x86/dcadsp.o\nCC\tlibavcodec/x86/flacencdsp_init.o\nSTRIP\tlibavcodec/x86/dnxhdenc.o\nX86ASM\tlibavcodec/x86/fmtconvert.o\nCC\tlibavcodec/x86/fmtconvert_init.o\nSTRIP\tlibavcodec/x86/cfhddsp.o\nX86ASM\tlibavcodec/x86/fpel.o\nX86ASM\tlibavcodec/x86/g722dsp.o\nCC\tlibavcodec/x86/g722dsp_init.o\nSTRIP\tlibavcodec/x86/ac3dsp_downmix.o\nX86ASM\tlibavcodec/x86/h263_loopfilter.o\nCC\tlibavcodec/x86/h263dsp_init.o\nX86ASM\tlibavcodec/x86/h264_chromamc.o\nX86ASM\tlibavcodec/x86/h264_chromamc_10bit.o\nX86ASM\tlibavcodec/x86/h264_deblock.o\nSTRIP\tlibavcodec/x86/dirac_dwt.o\nX86ASM\tlibavcodec/x86/h264_deblock_10bit.o\nX86ASM\tlibavcodec/x86/h264_idct.o\nSTRIP\tlibavcodec/x86/diracdsp.o\nX86ASM\tlibavcodec/x86/h264_idct_10bit.o\nSTRIP\tlibavcodec/x86/dct32.o\nX86ASM\tlibavcodec/x86/h264_intrapred.o\nSTRIP\tlibavcodec/x86/fmtconvert.o\nX86ASM\tlibavcodec/x86/h264_intrapred_10bit.o\nSTRIP\tlibavcodec/x86/g722dsp.o\nCC\tlibavcodec/x86/h264_intrapred_init.o\nSTRIP\tlibavcodec/x86/celt_pvq_search.o\nCC\tlibavcodec/x86/h264_qpel.o\nSTRIP\tlibavcodec/x86/fpel.o\nX86ASM\tlibavcodec/x86/h264_qpel_10bit.o\nX86ASM\tlibavcodec/x86/h264_qpel_8bit.o\nX86ASM\tlibavcodec/x86/h264_weight.o\nSTRIP\tlibavcodec/x86/h263_loopfilter.o\nX86ASM\tlibavcodec/x86/h264_weight_10bit.o\nCC\tlibavcodec/x86/h264chroma_init.o\nCC\tlibavcodec/x86/h264dsp_init.o\nX86ASM\tlibavcodec/x86/h26x/h2656_inter.o\nSTRIP\tlibavcodec/x86/h264_chromamc_10bit.o\nCC\tlibavcodec/x86/h26x/h2656dsp.o\nX86ASM\tlibavcodec/x86/hevc/add_res.o\nX86ASM\tlibavcodec/x86/hevc/deblock.o\nCC\tlibavcodec/x86/hevc/dsp_init.o\nSTRIP\tlibavcodec/x86/h264_weight.o\nX86ASM\tlibavcodec/x86/hevc/idct.o\nSTRIP\tlibavcodec/x86/flacdsp.o\nX86ASM\tlibavcodec/x86/hevc/mc.o\nX86ASM\tlibavcodec/x86/hevc/sao.o\nSTRIP\tlibavcodec/x86/h264_weight_10bit.o\nX86ASM\tlibavcodec/x86/hevc/sao_10bit.o\nX86ASM\tlibavcodec/x86/hpeldsp.o\nCC\tlibavcodec/x86/hpeldsp_init.o\nX86ASM\tlibavcodec/x86/huffyuvdsp.o\nSTRIP\tlibavcodec/x86/hevc/add_res.o\nCC\tlibavcodec/x86/huffyuvdsp_init.o\nX86ASM\tlibavcodec/x86/huffyuvencdsp.o\nCC\tlibavcodec/x86/huffyuvencdsp_init.o\nX86ASM\tlibavcodec/x86/idctdsp.o\nCC\tlibavcodec/x86/idctdsp_init.o\nSTRIP\tlibavcodec/x86/h264_idct.o\nX86ASM\tlibavcodec/x86/imdct36.o\nX86ASM\tlibavcodec/x86/jpeg2000dsp.o\nCC\tlibavcodec/x86/jpeg2000dsp_init.o\nX86ASM\tlibavcodec/x86/lossless_audiodsp.o\nSTRIP\tlibavcodec/x86/idctdsp.o\nCC\tlibavcodec/x86/lossless_audiodsp_init.o\nSTRIP\tlibavcodec/x86/h264_chromamc.o\nX86ASM\tlibavcodec/x86/lossless_videodsp.o\nSTRIP\tlibavcodec/x86/huffyuvencdsp.o\nCC\tlibavcodec/x86/lossless_videodsp_init.o\nX86ASM\tlibavcodec/x86/lossless_videoencdsp.o\nCC\tlibavcodec/x86/lossless_videoencdsp_init.o\nSTRIP\tlibavcodec/x86/huffyuvdsp.o\nX86ASM\tlibavcodec/x86/lpc.o\nCC\tlibavcodec/x86/lpc_init.o\nSTRIP\tlibavcodec/x86/hpeldsp.o\nX86ASM\tlibavcodec/x86/me_cmp.o\nSTRIP\tlibavcodec/x86/jpeg2000dsp.o\nCC\tlibavcodec/x86/me_cmp_init.o\nSTRIP\tlibavcodec/x86/h264_idct_10bit.o\nX86ASM\tlibavcodec/x86/mlpdsp.o\nSTRIP\tlibavcodec/x86/h264_deblock_10bit.o\nCC\tlibavcodec/x86/mlpdsp_init.o\nCC\tlibavcodec/x86/mpeg4videodsp.o\nCC\tlibavcodec/x86/mpegaudiodsp.o\nCC\tlibavcodec/x86/mpegvideo.o\nCC\tlibavcodec/x86/mpegvideoenc.o\nSTRIP\tlibavcodec/x86/lossless_videoencdsp.o\nX86ASM\tlibavcodec/x86/mpegvideoencdsp.o\nCC\tlibavcodec/x86/mpegvideoencdsp_init.o\nSTRIP\tlibavcodec/x86/lpc.o\nX86ASM\tlibavcodec/x86/opusdsp.o\nCC\tlibavcodec/x86/opusdsp_init.o\nX86ASM\tlibavcodec/x86/pixblockdsp.o\nSTRIP\tlibavcodec/x86/mlpdsp.o\nCC\tlibavcodec/x86/pixblockdsp_init.o\nX86ASM\tlibavcodec/x86/proresdsp.o\nCC\tlibavcodec/x86/proresdsp_init.o\nSTRIP\tlibavcodec/x86/lossless_audiodsp.o\nSTRIP\tlibavcodec/x86/mpegvideoencdsp.o\nX86ASM\tlibavcodec/x86/qpel.o\nX86ASM\tlibavcodec/x86/qpeldsp.o\nCC\tlibavcodec/x86/qpeldsp_init.o\nX86ASM\tlibavcodec/x86/rv34dsp.o\nSTRIP\tlibavcodec/x86/opusdsp.o\nCC\tlibavcodec/x86/rv34dsp_init.o\nX86ASM\tlibavcodec/x86/rv40dsp.o\nSTRIP\tlibavcodec/x86/pixblockdsp.o\nCC\tlibavcodec/x86/rv40dsp_init.o\nX86ASM\tlibavcodec/x86/sbcdsp.o\nCC\tlibavcodec/x86/sbcdsp_init.o\nX86ASM\tlibavcodec/x86/sbrdsp.o\nCC\tlibavcodec/x86/sbrdsp_init.o\nX86ASM\tlibavcodec/x86/simple_idct10.o\nSTRIP\tlibavcodec/x86/qpel.o\nCC\tlibavcodec/x86/snowdsp.o\nX86ASM\tlibavcodec/x86/svq1enc.o\nSTRIP\tlibavcodec/x86/rv34dsp.o\nCC\tlibavcodec/x86/svq1enc_init.o\nSTRIP\tlibavcodec/x86/hevc/sao.o\nX86ASM\tlibavcodec/x86/synth_filter.o\nCC\tlibavcodec/x86/synth_filter_init.o\nSTRIP\tlibavcodec/x86/lossless_videodsp.o\nX86ASM\tlibavcodec/x86/takdsp.o\nSTRIP\tlibavcodec/x86/sbcdsp.o\nCC\tlibavcodec/x86/takdsp_init.o\nX86ASM\tlibavcodec/x86/ttadsp.o\nSTRIP\tlibavcodec/x86/svq1enc.o\nCC\tlibavcodec/x86/ttadsp_init.o\nSTRIP\tlibavcodec/x86/h264_intrapred_10bit.o\nX86ASM\tlibavcodec/x86/ttaencdsp.o\nSTRIP\tlibavcodec/x86/h264_qpel_10bit.o\nCC\tlibavcodec/x86/ttaencdsp_init.o\nX86ASM\tlibavcodec/x86/utvideodsp.o\nCC\tlibavcodec/x86/utvideodsp_init.o\nCC\tlibavcodec/x86/v210-init.o\nX86ASM\tlibavcodec/x86/v210.o\nX86ASM\tlibavcodec/x86/v210enc.o\nSTRIP\tlibavcodec/x86/h264_qpel_8bit.o\nCC\tlibavcodec/x86/v210enc_init.o\nSTRIP\tlibavcodec/x86/takdsp.o\nCC\tlibavcodec/x86/vc1dsp_init.o\nX86ASM\tlibavcodec/x86/vc1dsp_loopfilter.o\nX86ASM\tlibavcodec/x86/vc1dsp_mc.o\nSTRIP\tlibavcodec/x86/ttadsp.o\nCC\tlibavcodec/x86/vc1dsp_mmx.o\nSTRIP\tlibavcodec/x86/ttaencdsp.o\nX86ASM\tlibavcodec/x86/videodsp.o\nCC\tlibavcodec/x86/videodsp_init.o\nSTRIP\tlibavcodec/x86/utvideodsp.o\nX86ASM\tlibavcodec/x86/vorbisdsp.o\nSTRIP\tlibavcodec/x86/proresdsp.o\nCC\tlibavcodec/x86/vorbisdsp_init.o\nSTRIP\tlibavcodec/x86/sbrdsp.o\nX86ASM\tlibavcodec/x86/vp3dsp.o\nCC\tlibavcodec/x86/vp3dsp_init.o\nX86ASM\tlibavcodec/x86/vp6dsp.o\nCC\tlibavcodec/x86/vp6dsp_init.o\nSTRIP\tlibavcodec/x86/synth_filter.o\nX86ASM\tlibavcodec/x86/vp8dsp.o\nSTRIP\tlibavcodec/x86/rv40dsp.o\nCC\tlibavcodec/x86/vp8dsp_init.o\nX86ASM\tlibavcodec/x86/vp8dsp_loopfilter.o\nCC\tlibavcodec/x86/vp9dsp_init.o\nSTRIP\tlibavcodec/x86/v210.o\nCC\tlibavcodec/x86/vp9dsp_init_10bpp.o\nCC\tlibavcodec/x86/vp9dsp_init_12bpp.o\nSTRIP\tlibavcodec/x86/vorbisdsp.o\nCC\tlibavcodec/x86/vp9dsp_init_16bpp.o\nX86ASM\tlibavcodec/x86/vp9intrapred.o\nSTRIP\tlibavcodec/x86/vp6dsp.o\nX86ASM\tlibavcodec/x86/vp9intrapred_16bpp.o\nSTRIP\tlibavcodec/x86/v210enc.o\nX86ASM\tlibavcodec/x86/vp9itxfm.o\nSTRIP\tlibavcodec/x86/imdct36.o\nX86ASM\tlibavcodec/x86/vp9itxfm_16bpp.o\nX86ASM\tlibavcodec/x86/vp9itxfm_16bpp_avx512.o\nSTRIP\tlibavcodec/x86/me_cmp.o\nX86ASM\tlibavcodec/x86/vp9itxfm_avx512.o\nX86ASM\tlibavcodec/x86/vp9lpf.o\nSTRIP\tlibavcodec/x86/vc1dsp_mc.o\nX86ASM\tlibavcodec/x86/vp9lpf_16bpp.o\nX86ASM\tlibavcodec/x86/vp9mc.o\nX86ASM\tlibavcodec/x86/vp9mc_16bpp.o\nX86ASM\tlibavcodec/x86/vvc/alf.o\nX86ASM\tlibavcodec/x86/vvc/dmvr.o\nSTRIP\tlibavcodec/x86/videodsp.o\nCC\tlibavcodec/x86/vvc/dsp_init.o\nSTRIP\tlibavcodec/x86/vp3dsp.o\nX86ASM\tlibavcodec/x86/vvc/mc.o\nSTRIP\tlibavcodec/x86/vc1dsp_loopfilter.o\nX86ASM\tlibavcodec/x86/vvc/of.o\nX86ASM\tlibavcodec/x86/vvc/sad.o\nSTRIP\tlibavcodec/x86/h264_intrapred.o\nX86ASM\tlibavcodec/x86/vvc/sao.o\nSTRIP\tlibavcodec/x86/h264_deblock.o\nX86ASM\tlibavcodec/x86/vvc/sao_10bit.o\nSTRIP\tlibavcodec/x86/vp8dsp.o\nSTRIP\tlibavcodec/x86/vp9mc_16bpp.o\nX86ASM\tlibavcodec/x86/xvididct.o\nCC\tlibavcodec/x86/xvididct_init.o\nSTRIP\tlibavcodec/x86/vvc/sad.o\nCC\tlibavcodec/xan.o\nCC\tlibavcodec/xbm_parser.o\nSTRIP\tlibavcodec/x86/qpeldsp.o\nCC\tlibavcodec/xbmdec.o\nCC\tlibavcodec/xbmenc.o\nCC\tlibavcodec/xface.o\nCC\tlibavcodec/xfacedec.o\nCC\tlibavcodec/xfaceenc.o\nCC\tlibavcodec/xiph.o\nCC\tlibavcodec/xl.o\nCC\tlibavcodec/xma_parser.o\nCC\tlibavcodec/xpmdec.o\nSTRIP\tlibavcodec/x86/vp9mc.o\nCC\tlibavcodec/xsubdec.o\nCC\tlibavcodec/xsubenc.o\nCC\tlibavcodec/xvididct.o\nCC\tlibavcodec/xwd_parser.o\nCC\tlibavcodec/xwddec.o\nCC\tlibavcodec/xwdenc.o\nCC\tlibavcodec/xxan.o\nCC\tlibavcodec/y41pdec.o\nCC\tlibavcodec/y41penc.o\nCC\tlibavcodec/ylc.o\nCC\tlibavcodec/yop.o\nCC\tlibavcodec/yuv4dec.o\nCC\tlibavcodec/yuv4enc.o\nGEN\tlibavcodec/libavcodec.pc\nCC\tlibswresample/audioconvert.o\nCC\tlibswresample/dither.o\nCC\tlibswresample/options.o\nCC\tlibswresample/rematrix.o\nCC\tlibswresample/resample.o\nCC\tlibswresample/resample_dsp.o\nCC\tlibswresample/swresample.o\nCC\tlibswresample/swresample_frame.o\nSTRIP\tlibavcodec/x86/xvididct.o\nCC\tlibswresample/version.o\nX86ASM\tlibswresample/x86/audio_convert.o\nCC\tlibswresample/x86/audio_convert_init.o\nX86ASM\tlibswresample/x86/rematrix.o\nCC\tlibswresample/x86/rematrix_init.o\nSTRIP\tlibavcodec/x86/vvc/sao.o\nX86ASM\tlibswresample/x86/resample.o\nCC\tlibswresample/x86/resample_init.o\nGEN\tlibswresample/libswresample.pc\nCC\tlibswscale/alphablend.o\nCC\tlibswscale/cms.o\nSTRIP\tlibavcodec/x86/vvc/mc.o\nCC\tlibswscale/csputils.o\nCC\tlibswscale/format.o\nSTRIP\tlibavcodec/x86/vp9itxfm_avx512.o\nCC\tlibswscale/gamma.o\nCC\tlibswscale/graph.o\nSTRIP\tlibavcodec/x86/vvc/dmvr.o\nCC\tlibswscale/hscale.o\nCC\tlibswscale/hscale_fast_bilinear.o\nCC\tlibswscale/input.o\nSTRIP\tlibavcodec/x86/hevc/sao_10bit.o\nCC\tlibswscale/lut3d.o\nSTRIP\tlibavcodec/x86/vp9itxfm_16bpp_avx512.o\nCC\tlibswscale/options.o\nCC\tlibswscale/output.o\nSTRIP\tlibswresample/x86/rematrix.o\nCC\tlibswscale/rgb2rgb.o\nCC\tlibswscale/slice.o\nCC\tlibswscale/swscale.o\nCC\tlibswscale/swscale_unscaled.o\nCC\tlibswscale/utils.o\nSTRIP\tlibavcodec/x86/vvc/alf.o\nCC\tlibswscale/version.o\nCC\tlibswscale/vscale.o\nCC\tlibswscale/x86/hscale_fast_bilinear_simd.o\nSTRIP\tlibavcodec/x86/vvc/of.o\nX86ASM\tlibswscale/x86/input.o\nX86ASM\tlibswscale/x86/output.o\nX86ASM\tlibswscale/x86/range_convert.o\nCC\tlibswscale/x86/rgb2rgb.o\nX86ASM\tlibswscale/x86/rgb_2_rgb.o\nX86ASM\tlibswscale/x86/scale.o\nX86ASM\tlibswscale/x86/scale_avx2.o\nSTRIP\tlibavcodec/x86/hevc/idct.o\nCC\tlibswscale/x86/swscale.o\nSTRIP\tlibswresample/x86/resample.o\nCC\tlibswscale/x86/yuv2rgb.o\nX86ASM\tlibswscale/x86/yuv2yuvX.o\nX86ASM\tlibswscale/x86/yuv_2_rgb.o\nSTRIP\tlibavcodec/x86/hevc/mc.o\nCC\tlibswscale/yuv2rgb.o\nSTRIP\tlibswscale/x86/scale_avx2.o\nGEN\tlibswscale/libswscale.pc\nCC\tlibavutil/adler32.o\nCC\tlibavutil/aes.o\nCC\tlibavutil/aes_ctr.o\nSTRIP\tlibswscale/x86/yuv2yuvX.o\nCC\tlibavutil/ambient_viewing_environment.o\nSTRIP\tlibavcodec/x86/simple_idct10.o\nCC\tlibavutil/audio_fifo.o\nSTRIP\tlibswscale/x86/range_convert.o\nCC\tlibavutil/avsscanf.o\nCC\tlibavutil/avstring.o\nCC\tlibavutil/base64.o\nCC\tlibavutil/blowfish.o\nCC\tlibavutil/bprint.o\nCC\tlibavutil/buffer.o\nCC\tlibavutil/camellia.o\nSTRIP\tlibavcodec/x86/vp9intrapred.o\nCC\tlibavutil/cast5.o\nCC\tlibavutil/channel_layout.o\nCC\tlibavutil/container_fifo.o\nSTRIP\tlibswscale/x86/rgb_2_rgb.o\nCC\tlibavutil/cpu.o\nCC\tlibavutil/crc.o\nSTRIP\tlibswscale/x86/yuv_2_rgb.o\nCC\tlibavutil/csp.o\nCC\tlibavutil/des.o\nCC\tlibavutil/detection_bbox.o\nCC\tlibavutil/dict.o\nSTRIP\tlibavcodec/x86/vp8dsp_loopfilter.o\nCC\tlibavutil/display.o\nCC\tlibavutil/dovi_meta.o\nCC\tlibavutil/downmix_info.o\nCC\tlibavutil/encryption_info.o\nCC\tlibavutil/error.o\nCC\tlibavutil/eval.o\nCC\tlibavutil/executor.o\nCC\tlibavutil/fifo.o\nCC\tlibavutil/file.o\nCC\tlibavutil/file_open.o\nCC\tlibavutil/film_grain_params.o\nCC\tlibavutil/fixed_dsp.o\nCC\tlibavutil/float_dsp.o\nCC\tlibavutil/float_scalarproduct.o\nCC\tlibavutil/frame.o\nCC\tlibavutil/hash.o\nCC\tlibavutil/hdr_dynamic_metadata.o\nCC\tlibavutil/hdr_dynamic_vivid_metadata.o\nCC\tlibavutil/hmac.o\nCC\tlibavutil/hwcontext.o\nCC\tlibavutil/hwcontext_stub.o\nCC\tlibavutil/iamf.o\nCC\tlibavutil/imgutils.o\nCC\tlibavutil/integer.o\nCC\tlibavutil/intmath.o\nCC\tlibavutil/lfg.o\nCC\tlibavutil/lls.o\nCC\tlibavutil/log.o\nCC\tlibavutil/log2_tab.o\nCC\tlibavutil/lzo.o\nCC\tlibavutil/mastering_display_metadata.o\nCC\tlibavutil/mathematics.o\nCC\tlibavutil/md5.o\nCC\tlibavutil/mem.o\nCC\tlibavutil/murmur3.o\nCC\tlibavutil/opt.o\nCC\tlibavutil/parseutils.o\nCC\tlibavutil/pixdesc.o\nCC\tlibavutil/pixelutils.o\nSTRIP\tlibavcodec/x86/vp9intrapred_16bpp.o\nCC\tlibavutil/random_seed.o\nCC\tlibavutil/rational.o\nCC\tlibavutil/rc4.o\nCC\tlibavutil/refstruct.o\nCC\tlibavutil/reverse.o\nCC\tlibavutil/ripemd.o\nCC\tlibavutil/samplefmt.o\nCC\tlibavutil/sha.o\nCC\tlibavutil/sha512.o\nCC\tlibavutil/side_data.o\nCC\tlibavutil/slicethread.o\nCC\tlibavutil/spherical.o\nCC\tlibavutil/stereo3d.o\nCC\tlibavutil/tdrdi.o\nCC\tlibavutil/tea.o\nCC\tlibavutil/threadmessage.o\nCC\tlibavutil/time.o\nCC\tlibavutil/timecode.o\nCC\tlibavutil/timecode_internal.o\nCC\tlibavutil/timestamp.o\nCC\tlibavutil/tree.o\nCC\tlibavutil/twofish.o\nCC\tlibavutil/tx.o\nCC\tlibavutil/tx_double.o\nCC\tlibavutil/tx_float.o\nCC\tlibavutil/tx_int32.o\nCC\tlibavutil/utils.o\nCC\tlibavutil/uuid.o\nCC\tlibavutil/version.o\nCC\tlibavutil/video_enc_params.o\nCC\tlibavutil/video_hint.o\nX86ASM\tlibavutil/x86/aes.o\nCC\tlibavutil/x86/aes_init.o\nCC\tlibavutil/x86/cpu.o\nX86ASM\tlibavutil/x86/cpuid.o\nX86ASM\tlibavutil/x86/fixed_dsp.o\nCC\tlibavutil/x86/fixed_dsp_init.o\nX86ASM\tlibavutil/x86/float_dsp.o\nCC\tlibavutil/x86/float_dsp_init.o\nX86ASM\tlibavutil/x86/imgutils.o\nCC\tlibavutil/x86/imgutils_init.o\nX86ASM\tlibavutil/x86/lls.o\nSTRIP\tlibavutil/x86/cpuid.o\nCC\tlibavutil/x86/lls_init.o\nX86ASM\tlibavutil/x86/pixelutils.o\nSTRIP\tlibavutil/x86/fixed_dsp.o\nSTRIP\tlibswresample/x86/audio_convert.o\nCC\tlibavutil/x86/pixelutils_init.o\nX86ASM\tlibavutil/x86/tx_float.o\nCC\tlibavutil/x86/tx_float_init.o\nSTRIP\tlibavutil/x86/imgutils.o\nCC\tlibavutil/xga_font_data.o\nCC\tlibavutil/xtea.o\nCC\tlibavutil/float2half.o\nSTRIP\tlibavutil/x86/aes.o\nCC\tlibavutil/half2float.o\nGEN\tlibavutil/libavutil.pc\nHOSTCC\tdoc/print_options.o\nCC\tfftools/ffmpeg_dec.o\nCC\tfftools/ffmpeg_demux.o\nCC\tfftools/ffmpeg_enc.o\nCC\tfftools/ffmpeg_filter.o\nCC\tfftools/ffmpeg_hw.o\nCC\tfftools/ffmpeg_mux.o\nCC\tfftools/ffmpeg_mux_init.o\nCC\tfftools/ffmpeg_opt.o\nSTRIP\tlibavutil/x86/lls.o\nCC\tfftools/ffmpeg_sched.o\nCC\tfftools/graph/graphprint.o\nCC\tfftools/sync_queue.o\nCC\tfftools/thread_queue.o\nCC\tfftools/textformat/avtextformat.o\nSTRIP\tlibavutil/x86/pixelutils.o\nCC\tfftools/textformat/tf_compact.o\nSTRIP\tlibavutil/x86/float_dsp.o\nCC\tfftools/textformat/tf_default.o\nCC\tfftools/textformat/tf_flat.o\nCC\tfftools/textformat/tf_ini.o\nCC\tfftools/textformat/tf_json.o\nCC\tfftools/textformat/tf_mermaid.o\nCC\tfftools/textformat/tf_xml.o\nCC\tfftools/textformat/tw_avio.o\nCC\tfftools/textformat/tw_buffer.o\nCC\tfftools/textformat/tw_stdout.o\nCC\tfftools/resources/resman.o\nHOSTCC\tffbuild/bin2c_host.o\nSED\tfftools/resources/graph.css.min\nCC\tfftools/cmdutils.o\nSTRIP\tlibavcodec/x86/hevc/deblock.o\nCC\tfftools/opt_common.o\nCC\tfftools/ffmpeg.o\nCC\tfftools/ffprobe.o\nAR\tlibavdevice/libavdevice.a\nAR\tlibavfilter/libavfilter.a\nAR\tlibavformat/libavformat.a\nAR\tlibswresample/libswresample.a\nHOSTLD\tdoc/print_options\nHOSTLD\tffbuild/bin2c\nGENTEXI\tdoc/avoptions_format.texi\nGENTEXI\tdoc/avoptions_codec.texi\nPOD\tdoc/ffprobe.pod\nPOD\tdoc/ffmpeg.pod\nPOD\tdoc/ffmpeg-all.pod\nPOD\tdoc/ffmpeg-utils.pod\nPOD\tdoc/ffprobe-all.pod\nPOD\tdoc/ffmpeg-scaler.pod\nPOD\tdoc/ffmpeg-resampler.pod\nSTRIP\tlibswscale/x86/scale.o\nPOD\tdoc/ffmpeg-codecs.pod\nPOD\tdoc/ffmpeg-bitstream-filters.pod\nPOD\tdoc/ffmpeg-formats.pod\nPOD\tdoc/ffmpeg-protocols.pod\nPOD\tdoc/ffmpeg-devices.pod\nPOD\tdoc/libavutil.pod\nPOD\tdoc/ffmpeg-filters.pod\nPOD\tdoc/libavcodec.pod\nPOD\tdoc/libswscale.pod\nPOD\tdoc/libavformat.pod\nPOD\tdoc/libavdevice.pod\nPOD\tdoc/libswresample.pod\nMAN\tdoc/ffmpeg.1\nPOD\tdoc/libavfilter.pod\nMAN\tdoc/ffprobe.1\nMAN\tdoc/ffmpeg-utils.1\nMAN\tdoc/ffmpeg-scaler.1\nMAN\tdoc/ffmpeg-resampler.1\nMAN\tdoc/ffmpeg-codecs.1\nMAN\tdoc/ffmpeg-bitstream-filters.1\nMAN\tdoc/ffmpeg-formats.1\nMAN\tdoc/ffmpeg-protocols.1\nMAN\tdoc/ffmpeg-devices.1\nMAN\tdoc/libavutil.3\nMAN\tdoc/libswscale.3\nMAN\tdoc/libswresample.3\nMAN\tdoc/libavcodec.3\nMAN\tdoc/libavformat.3\nMAN\tdoc/libavdevice.3\nMAN\tdoc/libavfilter.3\nBIN2C\tfftools/resources/graph.html.c\nBIN2C\tfftools/resources/graph.css.c\nMAN\tdoc/ffmpeg-all.1\nMAN\tdoc/ffprobe-all.1\nMAN\tdoc/ffmpeg-filters.1\nCC\tfftools/resources/graph.html.o\nCC\tfftools/resources/graph.css.o\nSTRIP\tlibswscale/x86/input.o\nSTRIP\tlibavcodec/x86/h26x/h2656_inter.o\nSTRIP\tlibavcodec/x86/vp9lpf_16bpp.o\nSTRIP\tlibavcodec/x86/vp9itxfm_16bpp.o\nSTRIP\tlibswscale/x86/output.o\nSTRIP\tlibavcodec/x86/vvc/sao_10bit.o\nSTRIP\tlibavcodec/x86/vp9lpf.o\nAR\tlibswscale/libswscale.a\nSTRIP\tlibavutil/x86/tx_float.o\nAR\tlibavutil/libavutil.a\nSTRIP\tlibavcodec/x86/vp9itxfm.o\nAR\tlibavcodec/libavcodec.a\nLD\tffmpeg_g\nLD\tffprobe_g\nSTRIP\tffprobe\nSTRIP\tffmpeg\nrm fftools/resources/graph.css.c fftools/resources/graph.css.min fftools/resources/graph.html.c\n\nlibavformat/dashenc.c: In function 'dash_init':\nlibavformat/dashenc.c:1575:65: warning: '-stream' directive output may be truncated writing 7 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n 1575 |                 snprintf(os->initfile, sizeof(os->initfile), \"%s-stream%d.%s\", basename, i, os->format_name);\n      |                                                                 ^~~~~~~\nlibavformat/dashenc.c:1575:62: note: directive argument in the range [0, 2147483647]\n 1575 |                 snprintf(os->initfile, sizeof(os->initfile), \"%s-stream%d.%s\", basename, i, os->format_name);\n      |                                                              ^~~~~~~~~~~~~~~~\nIn file included from /usr/include/stdio.h:894,\n                 from ./libavutil/common.h:38,\n                 from ./libavutil/avutil.h:300,\n                 from libavformat/dashenc.c:31:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output 10 or more bytes (assuming 1042) into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/dashenc.c:1579:49: warning: '%s' directive output may be truncated writing up to 1023 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n 1579 |         snprintf(filename, sizeof(filename), \"%s%s\", c->dirname, os->initfile);\n      |                                                 ^~\nIn file included from /usr/include/stdio.h:894,\n                 from ./libavutil/common.h:38,\n                 from ./libavutil/avutil.h:300,\n                 from libavformat/dashenc.c:31:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 1 and 2047 bytes into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/dashenc.c: In function 'flush_init_segment':\nlibavformat/dashenc.c:592:49: warning: '%s' directive output may be truncated writing up to 1023 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n  592 |         snprintf(filename, sizeof(filename), \"%s%s\", c->dirname, os->initfile);\n      |                                                 ^~\nIn file included from /usr/include/stdio.h:894,\n                 from ./libavutil/common.h:38,\n                 from ./libavutil/avutil.h:300,\n                 from libavformat/dashenc.c:31:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 1 and 2047 bytes into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/dashenc.c: In function 'write_hls_media_playlist':\nlibavformat/dashenc.c:486:49: warning: 'media_' directive output may be truncated writing 6 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n  486 |         snprintf(playlist_name, string_size, \"%smedia_%d.m3u8\", base_url, id);\n      |                                                 ^~~~~~\nIn file included from /usr/include/stdio.h:894,\n                 from ./libavutil/common.h:38,\n                 from ./libavutil/avutil.h:300,\n                 from libavformat/dashenc.c:31:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 13 and 1046 bytes into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/img2enc.c: In function 'write_packet':\nlibavformat/img2enc.c:183:55: warning: '.tmp' directive output may be truncated writing 4 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n  183 |         snprintf(img->tmp[i], sizeof(img->tmp[i]), \"%s.tmp\", filename);\n      |                                                       ^~~~\nIn file included from /usr/include/stdio.h:894,\n                 from ./libavutil/common.h:38,\n                 from ./libavutil/avutil.h:300,\n                 from ./libavutil/opt.h:31,\n                 from libavformat/img2enc.c:31:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 5 and 1028 bytes into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/dashenc.c: In function 'dash_flush':\nlibavformat/dashenc.c:1954:63: warning: '%s' directive output may be truncated writing up to 1023 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n 1954 |             snprintf(os->full_path, sizeof(os->full_path), \"%s%s\", c->dirname, os->initfile);\n      |                                                               ^~\nIn file included from /usr/include/stdio.h:894,\n                 from ./libavutil/common.h:38,\n                 from ./libavutil/avutil.h:300,\n                 from libavformat/dashenc.c:31:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 1 and 2047 bytes into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/dashenc.c: In function 'dash_write_trailer':\nlibavformat/dashenc.c:486:49: warning: 'media_' directive output may be truncated writing 6 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n  486 |         snprintf(playlist_name, string_size, \"%smedia_%d.m3u8\", base_url, id);\n      |                                                 ^~~~~~\nlibavformat/dashenc.c:486:46: note: directive argument in the range [0, 2147483647]\n  486 |         snprintf(playlist_name, string_size, \"%smedia_%d.m3u8\", base_url, id);\n      |                                              ^~~~~~~~~~~~~~~~~\nIn file included from /usr/include/stdio.h:894,\n                 from ./libavutil/common.h:38,\n                 from ./libavutil/avutil.h:300,\n                 from libavformat/dashenc.c:31:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 13 and 1045 bytes into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/dashenc.c: In function 'dash_write_packet':\nlibavformat/dashenc.c:2251:59: warning: '%s' directive output may be truncated writing up to 1023 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n 2251 |         snprintf(os->full_path, sizeof(os->full_path), \"%s%s\", c->dirname,\n      |                                                           ^~\nIn file included from /usr/include/stdio.h:894,\n                 from ./libavutil/common.h:38,\n                 from ./libavutil/avutil.h:300,\n                 from libavformat/dashenc.c:31:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 1 and 2047 bytes into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/matroskaenc.c: In function 'mkv_write_trailer':\nlibavformat/matroskaenc.c:3346:79: warning: '%012.9f' directive output may be truncated writing between 12 and 320 bytes into a region of size between 8 and 14 [-Wformat-truncation=]\n 3346 |                 snprintf(duration_string, sizeof(duration_string), \"%02d:%02d:%012.9f\",\n      |                                                                               ^~~~~~~\nIn file included from /usr/include/stdio.h:894,\n                 from libavformat/avio.h:30,\n                 from libavformat/av1.h:26,\n                 from libavformat/matroskaenc.c:26:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 19 and 333 bytes into a destination of size 20\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/smoothstreamingenc.c: In function 'ism_flush':\nlibavformat/smoothstreamingenc.c:512:49: warning: '/temp' directive output may be truncated writing 5 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n  512 |         snprintf(filename, sizeof(filename), \"%s/temp\", os->dirname);\n      |                                                 ^~~~~\nIn file included from /usr/include/stdio.h:894,\n                 from libavformat/avformat.h:310,\n                 from libavformat/smoothstreamingenc.c:27:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 6 and 1029 bytes into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/smoothstreamingenc.c:540:53: warning: '/temp' directive output may be truncated writing 5 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n  540 |             snprintf(filename, sizeof(filename), \"%s/temp\", os->dirname);\n      |                                                     ^~~~~\nIn file included from /usr/include/stdio.h:894,\n                 from libavformat/avformat.h:310,\n                 from libavformat/smoothstreamingenc.c:27:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 6 and 1029 bytes into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/smoothstreamingenc.c:547:63: warning: '/FragmentInfo(' directive output may be truncated writing 14 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n  547 |         snprintf(header_filename, sizeof(header_filename), \"%s/FragmentInfo(%s=%\"PRIu64\")\", os->dirname, os->stream_type_tag, start_ts);\n      |                                                               ^~~~~~~~~~~~~~\nIn file included from /usr/include/stdio.h:894,\n                 from libavformat/avformat.h:310,\n                 from libavformat/smoothstreamingenc.c:27:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output 18 or more bytes (assuming 1041) into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/smoothstreamingenc.c:548:63: warning: '/Fragments(' directive output may be truncated writing 11 bytes into a region of size between 1 and 1024 [-Wformat-truncation=]\n  548 |         snprintf(target_filename, sizeof(target_filename), \"%s/Fragments(%s=%\"PRIu64\")\", os->dirname, os->stream_type_tag, start_ts);\n      |                                                               ^~~~~~~~~~~\nIn file included from /usr/include/stdio.h:894,\n                 from libavformat/avformat.h:310,\n                 from libavformat/smoothstreamingenc.c:27:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output 15 or more bytes (assuming 1038) into a destination of size 1024\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/vorbiscomment.c: In function 'ff_vorbiscomment_write':\nlibavformat/vorbiscomment.c:103:63: warning: '%03d' directive output may be truncated writing between 3 and 10 bytes into a region of size 4 [-Wformat-truncation=]\n  103 |             snprintf(chapter_number, sizeof(chapter_number), \"%03d\", i);\n      |                                                               ^~~~\nlibavformat/vorbiscomment.c:103:62: note: directive argument in the range [0, 2147483647]\n  103 |             snprintf(chapter_number, sizeof(chapter_number), \"%03d\", i);\n      |                                                              ^~~~~~\nIn file included from /usr/include/stdio.h:894,\n                 from libavformat/avio.h:30,\n                 from libavformat/vorbiscomment.c:22:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 4 and 11 bytes into a destination of size 4\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/vorbiscomment.c:104:69: warning: '%02d' directive output may be truncated writing between 2 and 3 bytes into a region of size between 1 and 7 [-Wformat-truncation=]\n  104 |             snprintf(chapter_time, sizeof(chapter_time), \"%02d:%02d:%02d.%03d\", h, m, s, ms);\n      |                                                                     ^~~~\nlibavformat/vorbiscomment.c:104:58: note: directive argument in the range [-59, 59]\n  104 |             snprintf(chapter_time, sizeof(chapter_time), \"%02d:%02d:%02d.%03d\", h, m, s, ms);\n      |                                                          ^~~~~~~~~~~~~~~~~~~~~\nlibavformat/vorbiscomment.c:104:58: note: directive argument in the range [-999, 999]\nIn file included from /usr/include/stdio.h:894,\n                 from libavformat/avio.h:30,\n                 from libavformat/vorbiscomment.c:22:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 13 and 21 bytes into a destination of size 13\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavformat/rtsp.c: In function 'ff_rtsp_make_setup_request':\nlibavformat/rtsp.c:1626:30: warning: '%s' directive output may be truncated writing up to 4095 bytes into a region of size 4085 [-Wformat-truncation=]\n 1626 |                  \"Transport: %s\\r\\n\",\n      |                              ^~\n 1627 |                  transport);\n      |                  ~~~~~~~~~    \nIn file included from /usr/include/stdio.h:894,\n                 from ./libavutil/common.h:38,\n                 from ./libavutil/avutil.h:300,\n                 from ./libavutil/opt.h:31,\n                 from libavformat/rtsp.c:34:\n/usr/include/x86_64-linux-gnu/bits/stdio2.h:71:10: note: '__builtin___snprintf_chk' output between 14 and 4109 bytes into a destination of size 4096\n   71 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,\n      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   72 |                                    __glibc_objsize (__s), __fmt,\n      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   73 |                                    __va_arg_pack ());\n      |                                    ~~~~~~~~~~~~~~~~~\nlibavcodec/ffv1.c:222:99: warning: argument 4 of type 'int *' declared as a pointer [-Warray-parameter=]\n  222 | void ff_ffv1_compute_bits_per_plane(const FFV1Context *f, FFV1SliceContext *sc, int bits[4], int *offset, int mask[4], int bits_per_raw_sample)\n      |                                                                                              ~~~~~^~~~~~\nIn file included from libavcodec/ffv1.c:33:\nlibavcodec/ffv1.h:204:98: note: previously declared as an array 'int[1]'\n  204 | void ff_ffv1_compute_bits_per_plane(const FFV1Context *f, FFV1SliceContext *sc, int bits[4], int offset[1], int mask[4], int bits_per_raw_sample);\n      |                                                                                              ~~~~^~~~~~~~~\nIn function 'print_buildconf',\n    inlined from 'show_buildconf' at fftools/opt_common.c:257:5:\nfftools/opt_common.c:223:49: warning: writing 1 byte into a region of size 0 [-Wstringop-overflow=]\n  223 |         remove_tilde[sizeof(\"pkg-config~\") - 2] = ' ';\n      |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~\nfftools/opt_common.c: In function 'show_buildconf':\nfftools/opt_common.c:211:10: note: at offset [10, 11] into destination object 'str' of size 1\n  211 |     char str[] = { FFMPEG_CONFIGURATION };\n      |          ^~~\n\nroot@c0mpi1er-c0nta1ner:/work# ", 61.62273836135864]]