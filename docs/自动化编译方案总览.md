## 1. 方案需求描述
现在我需要设计一个自动化编译方案，用以自动化编译c/c++的开源项目，为二进制安全领域提供高质量的数据集，方案计划利用多个提升大模型能力的agent，进行全自动的项目编译及错误处理。方案整体分为两部分，编译部分和错误处理部分。

### 1.1 编译部分
该部分主要进行项目结构的分析，利用ccscanner工具结合智能文档解析提取项目缺失的依赖并安装，从项目中分析提取编译指令组，并在docker中通过SSH持久连接执行编译指令对项目进行编译。

### 1.2 错误处理部分
该部分主要作为编译报错时，主控智能体无法利用自身能力解决问题时的错误处理机制。主要是利用错误处理智能体结合github issues和google search等工具获取最佳的解决方案返回给主控智能体。

### 1.3 整体流程
提供要编译的项目的txt文件，每一行为一个项目的github地址，按行读取处理每一个项目，如果检测本地已有该项目则直接进行编译。首先利用项目信息填充提示词给主控智能体，由主控智能体负责全程的过程监控和调度，然后主控智能体给项目分析智能体发送指令让其对项目进行分析，项目分析智能体综合运用ccscanner结构化扫描和智能文档解析技术，分析项目的结构以及一些文档文件（Install，Readme，build，Notes等），提取项目所需的依赖（利用ccscanner工具提取依赖以及对文档文件分析提取依赖进行交叉验证）以及项目编译的指令组（对文档文件进行分析获取编译指令组，如果存在针对多个系统的文件或者编译指令组，我们取在类unix平台的编译指令组），然后对提取的依赖进行分析，判断是属于哪种安装方式（apt，pip，以及github源码获取并编译等），将所需依赖及安装方式以及编译指令组返回给主控智能体。主控智能体分析所需的依赖并安装，如果是需要从源码编译，则需要重新走整体的编译流程对需要的依赖进行编译（在原项目容器中编译，不创建新容器），安装完依赖后，则通过SSH持久连接在docker中执行提取的编译指令进行编译，同时实时监控编译流程，如果出现错误，则首先利用主控智能体自身的大模型能力尝试解决（最多3次），如果解决，则重新编译，如果无法解决，则向错误处理智能体发配任务，对当前错误进行处理。错误处理智能体尝试解决（最多3次），获取最佳的解决方案并返回给主控智能体，主控智能体执行解决方案并重新编译，我们只对项目进行编译，不安装，例如只执行到make，但不会执行make install，因此，由主控智能体根据执行make后的返回结果和实时输出监控来判断是否编译成功。编译成功后，将生成的二进制文件复制到宿主机指定目录进行保存。

### 1.4 智能体架构设计

#### **智能体层次划分**
参考AutoCompiler的成功实践，采用分层的多智能体协作架构：

**🎯 大智能体（核心决策层）**：
1. **主控智能体（MasterAgent）**：整体编译流程的总指挥和调度者
2. **项目分析智能体（ProjectAnalyzer）**：项目结构和依赖的专业分析师
3. **错误处理智能体（ErrorSolver）**：编译错误的专业诊断和解决专家

**🔧 工具类专家智能体（功能执行层）**：
1. **GoogleSearchAgent**：专门负责Google搜索和结果分析（参考AutoCompiler的GoogleSearch.py实现）
2. **GitHubIssuesAgent**：专门负责GitHub Issues搜索（参考GoogleSearchAgent的实现方式）

**🛠️ 工具类（非智能体）**：
1. **DocumentAnalyzer**：直接LLM文档分析工具（项目分析智能体调用，参考AutoCompiler实现）
2. **DEBATEValidator**：文件验证工具（项目分析智能体调用）
3. **DependencyScanner**：ccscanner依赖扫描工具（项目分析智能体调用）
4. **ErrorAnalyzer**：错误模式分析工具（错误处理智能体调用）
5. **SolutionValidator**：解决方案验证工具（错误处理智能体调用）

**实现参考策略**：
- GoogleSearchAgent和GitHubIssuesAgent是独立的ReAct智能体
- 其他都是普通的工具类，不是智能体，由对应的大智能体调用

#### **Tools调用方式**
严格参考AutoCompiler的Tools调用模式：
```python
# 智能体实例化
ProjectAnalyzer_Agent = ProjectAnalyzer(local_path=project_path, project_name=project_name)
ErrorSolver_Agent = ErrorSolver(project_name=project_name)

# Tools定义
tools = [
    Tool(
        name="Shell",
        description=shell.execute_command.__doc__,
        func=shell.execute_command
    ),
    Tool(
        name="ProjectAnalyzer",
        description=ProjectAnalyzer_Agent.analyze_comprehensive.__doc__,
        func=ProjectAnalyzer_Agent.analyze_comprehensive  # 返回answer['output']
    ),
    Tool(
        name="ErrorSolver",
        description=ErrorSolver_Agent.solve_with_search.__doc__,
        func=ErrorSolver_Agent.solve_with_search  # 返回answer['output']
    )
]
```

### 1.5 智能体调用大模型api
url："https://api.chatanywhere.tech/v1"
api-key："sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

#### **智能体提示词配置**
三个主要智能体的提示词模板统一配置在config.py中：
```python
# config.py中的提示词配置
MASTER_AGENT_PROMPT = """主控智能体的专属提示词模板..."""
PROJECT_ANALYZER_PROMPT = """项目分析智能体的专属提示词模板..."""
ERROR_SOLVER_PROMPT = """错误处理智能体的专属提示词模板..."""
```

**差异化LLM配置策略**：
根据不同智能体的任务特点，采用差异化的模型参数配置：

```python
LLM_CONFIGS = {
    "master_agent": {
        "model": "o3",
        "temperature": 0.7,      # 平衡决策稳定性与灵活性
        "timeout": 180,          # 考虑o3模型响应较慢
        "max_tokens": 4000,
        "max_iterations": 25     # 主控智能体需要更多迭代空间
    },
    "project_analyzer": {
        "model": "claude-sonnet-4-20250514-thinking", 
        "temperature": 0.3,      # 分析任务需要更高准确性
        "timeout": 120,
        "max_tokens": 6000,      # 文档分析需要更长输出
        "max_iterations": 15
    },
    "error_solver": {
        "model": "claude-opus-4-20250514-thinking",
        "temperature": 0.8,      # 错误解决需要创造性思维
        "timeout": 150,
        "max_tokens": 3000,
        "max_iterations": 10     # 专注问题解决，减少迭代
    }
}
```

**参数设计原理**：
- **Temperature差异化**：分析任务（0.3）< 决策任务（0.7）< 创造性任务（0.8）
- **Timeout适配**：根据模型响应速度和任务复杂度调整
- **Token分配**：文档分析 > 综合决策 > 专项问题解决
- **迭代控制**：主控智能体承担更多协调任务，需要更多迭代机会

### 1.6 循环依赖和递归编译处理策略
为避免循环依赖和控制系统复杂度，制定以下策略：

**基本策略**：
- **最大递归深度限制**：依赖编译最多进行2层递归，超过2层的依赖将被忽略
- **容器复用策略**：依赖编译在原项目容器中进行，不创建新的Docker容器，确保编译后的依赖可直接供原项目使用
- **循环依赖检测方案**：采用简单路径追踪算法，维护当前编译路径列表，在编译新依赖前检查是否已存在于路径中

**详细算法实现**：
```python
class DependencyTracker:
    def __init__(self, max_depth=2):
        self.compilation_path = []      # 当前编译路径 [project_A, dep_B, dep_C]
        self.max_depth = max_depth      # 最大递归深度
        self.compiled_projects = set()  # 已编译项目缓存
    
    def check_dependency_validity(self, new_dependency: str) -> tuple[bool, str]:
        """
        检查新依赖是否可以编译
        返回: (是否可编译, 原因)
        """
        # 1. 检查循环依赖
        if new_dependency in self.compilation_path:
            cycle_path = self.compilation_path + [new_dependency]
            return False, f"Circular dependency detected: {' -> '.join(cycle_path)}"
        
        # 2. 检查递归深度
        if len(self.compilation_path) >= self.max_depth:
            return False, f"Maximum recursion depth ({self.max_depth}) exceeded"
        
        # 3. 检查是否已编译过
        if new_dependency in self.compiled_projects:
            return True, f"Dependency {new_dependency} already compiled, skipping"
        
        return True, "Dependency is valid for compilation"
    
    def enter_dependency(self, dependency: str):
        """进入依赖编译"""
        self.compilation_path.append(dependency)
        logging.info(f"Entering dependency compilation: {' -> '.join(self.compilation_path)}")
    
    def exit_dependency(self, dependency: str, success: bool):
        """退出依赖编译"""
        if self.compilation_path and self.compilation_path[-1] == dependency:
            self.compilation_path.pop()
            
        if success:
            self.compiled_projects.add(dependency)
            logging.info(f"Successfully compiled dependency: {dependency}")
        else:
            logging.warning(f"Failed to compile dependency: {dependency}")
    
    def get_current_depth(self) -> int:
        """获取当前递归深度"""
        return len(self.compilation_path)
    
    def get_compilation_path(self) -> list:
        """获取当前编译路径"""
        return self.compilation_path.copy()
```

**检测时机和处理流程**：
1. **检测时机**：主控智能体在安装源码依赖前调用`check_dependency_validity()`
2. **进入编译**：验证通过后调用`enter_dependency()`记录路径
3. **退出编译**：编译完成后调用`exit_dependency()`更新状态
4. **冲突处理**：遇到循环依赖或深度超限时记录警告并跳过该依赖
5. **缓存机制**：已编译的依赖直接跳过，避免重复编译工作

### 1.6 编译成功判断标准
完全由主控智能体决定，参考AutoCompiler的实现方式：

**判断策略**：
- **主控智能体智能判断**：主控智能体分析编译输出和执行结果
- **基于完整上下文**：综合考虑编译输出、错误信息和文件系统状态
- **最终决策权**：主控智能体拥有编译成功与否的最终决策权
- **返回标准格式**：COMPILATION-SUCCESS、COMPILATION-FAIL或COMPILATION-UNCERTAIN
- **避免复杂逻辑**：不使用预设的关键词匹配规则，完全依靠智能体推理

**详细算法实现**：
```python
class CompilationStatusAnalyzer:
    def __init__(self):
        # 成功关键词（优先级递减）
        self.success_keywords = [
            # 强成功信号
            ["build successful", "compilation successful", "build complete"],
            # Make成功信号
            ["make[1]: Leaving directory", "make: Nothing to be done"],
            # CMake成功信号
            ["Built target", "Build finished"],
            # 通用成功信号
            ["finished", "done", "success"]
        ]
        
        # 失败关键词（优先级递减）
        self.failure_keywords = [
            # 强失败信号
            ["error:", "failed:", "fatal error", "compilation terminated"],
            # Make失败信号
            ["make: *** [", "make[1]: *** [", "No rule to make target"],
            # 链接失败信号
            ["undefined reference", "cannot find -l", "ld: error"],
            # 编译器错误
            ["no such file", "permission denied", "command not found"]
        ]
        
        # 警告关键词（不影响成功判断）
        self.warning_keywords = [
            "warning:", "note:", "deprecated", "unused variable"
        ]
    
    def analyze_compilation_output(self, terminal_output: str, project_name: str) -> dict:
        """
        分析编译输出，判断编译状态
        返回包含状态、置信度、分析详情的字典
        """
        output_lower = terminal_output.lower()
        
        # 1. 关键词预分析
        failure_score = self._calculate_keyword_score(output_lower, self.failure_keywords)
        success_score = self._calculate_keyword_score(output_lower, self.success_keywords)
        warning_count = self._count_warnings(output_lower)
        
        # 2. LLM智能分析
        llm_analysis = self._llm_analyze_output(terminal_output, project_name)
        
        # 3. 综合判断
        if failure_score > 0.7:  # 明显失败
            status = "COMPILATION-FAIL"
            confidence = min(0.95, failure_score)
        elif success_score > 0.5 and failure_score < 0.3:  # 明显成功
            status = "COMPILATION-SUCCESS"
            confidence = min(0.90, success_score)
        else:  # 不确定，依赖LLM分析
            status = llm_analysis["status"]
            confidence = llm_analysis["confidence"] * 0.8  # LLM分析降权
        
        return {
            "status": status,
            "confidence": confidence,
            "failure_score": failure_score,
            "success_score": success_score,
            "warning_count": warning_count,
            "llm_analysis": llm_analysis,
            "reasoning": self._generate_reasoning(status, confidence, failure_score, success_score)
        }
    
    def _calculate_keyword_score(self, text: str, keyword_groups: list) -> float:
        """计算关键词匹配分数，归一化到0-1"""
        total_score = 0.0
        for i, keywords in enumerate(keyword_groups):
            weight = 1.0 / (i + 1)  # 优先级权重
            for keyword in keywords:
                count = text.count(keyword)
                total_score += count * weight
        
        return min(1.0, total_score / 10.0)  # 归一化到0-1
    
    def _llm_analyze_output(self, terminal_output: str, project_name: str) -> dict:
        """LLM分析编译输出的具体实现"""
        prompt = f'''分析以下C/C++项目'{project_name}'的编译输出，判断编译是否成功。

编译输出:
{terminal_output[-2000:]}

规则:
1. 忽略警告信息 (warning:, note:, deprecated)
2. 关注最终编译结果
3. 寻找成功指标 (build complete, finished successfully)
4. 寻找失败指标 (error:, failed:, fatal error)

输出JSON格式:
{{
    "status": "COMPILATION-SUCCESS|COMPILATION-FAIL|COMPILATION-UNCERTAIN",
    "confidence": 0.0-1.0,
    "key_indicators": ["indicator1", "indicator2"],
    "reasoning": "简要解释"
}}'''
        
        try:
            llm_response = self.llm.invoke(prompt)
            return json.loads(llm_response.content)
        except Exception as e:
            return {
                "status": "COMPILATION-UNCERTAIN",
                "confidence": 0.1,
                "key_indicators": [],
                "reasoning": f"LLM analysis failed: {str(e)}"
            }
```

**判断流程**：
1. **输出预处理**：转换为小写，提取关键信息
2. **关键词评分**：计算成功/失败信号强度
3. **LLM补充分析**：处理复杂情况和边界场景
4. **综合决策**：基于规则优先级确定最终状态
5. **置信度计算**：提供判断可靠性评估

### 1.7 依赖安装方式优先级策略
建立智能化的依赖安装方式选择机制：
- **优先级顺序**：系统级包管理器(apt/yum) > 语言级包管理器(pip/npm) > 源码编译
- **版本冲突处理**：发生依赖版本冲突时，由主控智能体调度重新安装冲突依赖的对应版本
- **平台兼容性**：当前阶段暂不考虑系统平台差异，统一按类Unix平台处理

### 1.8 依赖分析冲突解决机制
当ccscanner扫描结果与文档分析结果存在冲突时：
- **优先级原则**：文档分析结果优先于ccscanner扫描结果
- **智能判定**：由项目分析智能体进行最终的智能判定和合并
- **交叉验证**：利用两种方式的结果进行交叉验证，提高依赖识别准确性

### 1.9 Docker基础镜像配置
参考AutoCompiler的成熟配置方案，构建包含完整编译环境的Docker基础镜像：
- **基础镜像**：gcc:13.2.0，提供完整的GCC编译工具链
- **系统工具包**：openssh-server（SSH服务）、tree（目录结构查看）、cmake（构建工具）、vim（文本编辑）、git（版本控制）
- **网络工具**：proxychains4（代理链工具），支持网络代理环境下的依赖下载
- **SSH配置**：启用root用户登录（用户名：root，密码：root），支持密码认证
- **编译器包装**：集成编译器包装脚本，统一管理编译参数和优化选项
- **工作目录**：/work，项目源码统一挂载至此目录

### 1.10 编译产物处理机制
建立完整的编译产物管理和保存机制：
- **产物识别**：编译成功后自动识别生成的二进制文件（可执行文件、静态库、动态库等）
- **文件复制**：将识别到的编译产物复制到宿主机指定目录（/host/binaries/{project_name}/）
- **目录结构**：按项目名称创建独立目录，避免不同项目的产物混淆
- **元数据记录**：记录编译时间、编译参数、产物类型等元信息到metadata.json文件

### 1.11 项目副本管理策略
参考AutoCompiler的成熟实现，建立完善的项目副本管理机制：

**基本策略**：
- **原始项目保护**：下载后的原始项目保存在固定目录，不直接用于编译
- **副本创建机制**：每次编译前使用UUID创建独立副本（格式：{绝对路径}-{UUID}）
- **权限管理**：使用`chmod -R 777`确保副本文件具有正确的读写权限
- **并行安全**：多个项目可同时编译，每个都有独立副本，避免相互干扰
- **清理策略**：编译完成后可选择保留或删除编译副本，原始项目始终保留

**详细实现方案**：
```python
import uuid
import os
import subprocess
import logging

def copy_project(local_path):
    """
    创建项目编译副本，使用UUID确保唯一性
    参考AutoCompiler的成熟实现
    """
    UUID = uuid.uuid4()  # 使用uuid4生成随机UUID
    local_path = os.path.abspath(local_path)
    new_path = f"{local_path}-{UUID}"
    
    # 复制前后都设置777权限，确保容器内可写
    cmd = f"chmod -R 777 {local_path} && cp -r {local_path} {new_path} && chmod -R 777 {new_path}"
    logging.info(f"[-] Copy project from {local_path} to {new_path}")
    
    ret = subprocess.run(cmd, shell=True, capture_output=True)
    if ret.returncode != 0:
        raise Exception(f"Failed to copy project from {local_path} to {new_path} {ret.stderr}")
    
    return new_path

def cleanup_project_copy(copy_path, keep_original=True):
    """
    清理项目副本，保留原始项目
    安全检查确保只删除带UUID后缀的副本
    """
    if "-" in copy_path and keep_original:  # 确保只删除副本
        subprocess.run(f"rm -rf {copy_path}", shell=True)
        logging.info(f"[+] Cleaned up project copy: {copy_path}")
    else:
        logging.warning(f"[!] Skipped cleanup for potential original project: {copy_path}")

class ProjectCopyManager:
    """项目副本生命周期管理器"""
    
    def __init__(self, base_path: str):
        self.base_path = base_path
        self.active_copies = {}  # {project_name: copy_path}
    
    def create_copy(self, project_name: str, original_path: str) -> str:
        """创建项目副本并记录"""
        copy_path = copy_project(original_path)
        self.active_copies[project_name] = copy_path
        logging.info(f"Created copy for project {project_name}: {copy_path}")
        return copy_path
    
    def cleanup_copy(self, project_name: str, force=False):
        """清理指定项目的副本"""
        if project_name in self.active_copies:
            copy_path = self.active_copies[project_name]
            if force or self._is_safe_to_delete(copy_path):
                cleanup_project_copy(copy_path)
                del self.active_copies[project_name]
            else:
                logging.warning(f"Unsafe to delete {copy_path}, skipping cleanup")
    
    def cleanup_all(self):
        """清理所有活跃副本"""
        for project_name in list(self.active_copies.keys()):
            self.cleanup_copy(project_name)
    
    def _is_safe_to_delete(self, path: str) -> bool:
        """安全检查：确保路径包含UUID后缀"""
        return "-" in os.path.basename(path) and len(os.path.basename(path).split("-")[-1]) == 36
```

**UUID技术规格**：
- **UUID版本**：采用`uuid.uuid4()`生成完全随机UUID
- **唯一性保证**：36位字符串提供足够的唯一性（概率碰撞极低）
- **格式标准**：遵循RFC 4122标准格式
- **并发安全**：多进程同时生成UUID无冲突风险

**权限管理策略**：
- **777权限设置**：确保Docker容器内root用户可读写执行
- **双重权限处理**：复制前后都执行chmod，确保权限正确传递
- **容器兼容性**：适配Docker容器内的权限环境

**清理策略细节**：
- **安全检查**：验证路径包含UUID后缀再删除
- **选择性清理**：支持保留特定项目副本用于调试
- **异常处理**：删除失败时记录警告，不中断主流程
- **原始保护**：严格保护原始项目不被误删

### 1.12 SSH连接异常处理策略
建立完善的SSH连接异常处理和恢复机制：
- **双重超时机制**：命令无输出超时（120秒）和命令总执行超时（3600秒）
- **自动重连策略**：连接断开时自动重连，最多尝试3次，重连间隔5秒
- **智能中断处理**：检测到长时间无响应时发送Ctrl+C中断信号
- **容器恢复策略**：容器意外停止时尝试重启并重新建立SSH连接
- **状态保持机制**：维护编译环境变量和工作目录状态，支持编译过程的连续性
- **异常日志记录**：详细记录所有SSH异常和恢复过程，便于问题诊断

### 1.13 错误信息标准化处理
建立统一的错误信息传递和处理格式：
```json
{
  "error_type": "dependency_missing|compile_error|permission_denied|timeout_error",
  "error_message": "具体的错误信息内容",
  "error_context": "错误发生时的上下文信息（命令、文件、行号等）",
  "compilation_stage": "dependency_install|source_compile|main_compile",
  "suggested_solutions": [
    "解决方案1：具体的修复步骤",
    "解决方案2：备选修复方案"
  ],
  "retry_count": 1,
  "timestamp": "2024-01-01T10:00:00Z"
}
```
- **错误分类标准**：依赖缺失、编译错误、权限问题、超时错误等标准分类
- **上下文保留**：记录错误发生的具体环境和执行命令
- **解决方案格式**：结构化的修复建议，便于智能体执行
- **传递保障**：JSON格式确保跨智能体传递的准确性和完整性

### 1.14 编译产物智能识别方案
基于Git差异检测的编译产物识别策略（借鉴ghcc成熟技术）：
- **Git差异检测**：使用`git ls-files --others`命令识别编译后新增的文件
  ```bash
  # 编译后执行Git差异检测
  git ls-files --others | grep -E '\.(so|a|out|exe)$|^[^.]*$'
  ```
- **产物筛选规则**：
  - 可执行文件：无扩展名且具有执行权限的文件
  - 静态库：.a 扩展名文件
  - 动态库：.so 扩展名文件
  - 目标文件：.o 扩展名文件（可选保存）
- **智能分类**：按文件类型和大小自动分类编译产物
- **临时文件过滤**：自动过滤.tmp、.log、.cache等临时文件
- **核心产物识别**：优先保存项目主体产物，过滤测试程序和调试文件
- **元数据附加**：为每个识别的产物记录编译参数、源文件映射等信息



## 2. 智能体介绍
该方案利用三个专业化智能体协作完成自动化编译任务，每个智能体内部都采用ReAct框架，通过Tool调用方式实现功能模块化。

### 2.1 主控智能体（MasterAgent）
**架构设计**：采用ReAct框架的单一智能体，通过Tool调用方式协调其他智能体和系统组件。

**核心职责**：
- 整体编译流程的调度和决策
- 项目副本管理和Docker环境控制
- 依赖安装和编译执行的监控
- 编译成功/失败的智能判断
- 系统级兼容性错误的容器版本切换

**工具集设计**：
```python
# 从tools.py导入工具类
from tools import InteractiveDockerShell, GitHubManager, DependencyScanner
from ProjectAnalyzer import ProjectAnalyzer
from ErrorSolver import ErrorSolver

# 初始化工具实例（参考AutoCompiler的实现方式）
docker_shell = InteractiveDockerShell(local_path=project_path)
github_manager = GitHubManager(proxy=PROXY)
dependency_scanner = DependencyScanner()

# 初始化智能体实例（参考AutoCompiler的智能体通信方式）
project_analyzer = ProjectAnalyzer(project_name=project_name, project_path=project_path)
error_solver = ErrorSolver(project_name=project_name)

# 构建工具集
tools = [
    Tool(
        name="Shell",
        description=docker_shell.execute_command.__doc__,
        func=docker_shell.execute_command
    ),
    Tool(
        name="GitSnapshot",
        description=docker_shell.create_git_snapshot.__doc__,
        func=docker_shell.create_git_snapshot
    ),
    Tool(
        name="ArtifactDetector",
        description=docker_shell.detect_compilation_artifacts.__doc__,
        func=docker_shell.detect_compilation_artifacts
    ),
    Tool(
        name="ContainerSwitcher",
        description=docker_shell.switch_ubuntu_version.__doc__,
        func=docker_shell.switch_ubuntu_version
    ),
    Tool(
        name="ProjectAnalyzer",
        description=project_analyzer.analyze.__doc__,
        func=project_analyzer.analyze
    ),
    Tool(
        name="ErrorSolver",
        description=error_solver.solve.__doc__,
        func=error_solver.solve
    ),
    Tool(
        name="GitHubCloner",
        description=github_manager.clone_project.__doc__,
        func=github_manager.clone_project
    ),
    Tool(
        name="ProjectCopyManager",
        description=github_manager.create_project_copy.__doc__,
        func=github_manager.create_project_copy
    )
]
```

**工作流程**：
1. **项目初始化**：创建项目编译副本（UUID标识），构建Docker容器，建立SSH持久连接
2. **项目分析**：调用ProjectAnalyzer工具分析项目结构、依赖和编译指令
3. **依赖安装**：通过DependencyInstaller工具安装系统级、语言级和源码依赖
4. **编译执行**：使用CompilationExecutor工具执行编译指令，实时监控编译过程
5. **错误处理**：遇到编译错误时，先自主分析解决（最多3次），无法解决时调用ErrorSolver工具
6. **成功判断**：基于编译输出和文件系统变化智能判断编译是否成功
7. **产物管理**：识别和复制编译产物到宿主机指定目录

**系统级兼容性错误处理**：
当检测到依赖包版本与系统版本冲突、GCC编译器版本不兼容、系统库版本冲突等无法在当前环境解决的问题时：
1. 基于错误输出智能分析所需的Ubuntu版本（18.04/20.04/22.04）
2. 完全销毁当前容器并创建指定版本的新容器
3. 保持项目副本一致性，在新环境中重新开始完整的编译流程
4. 记录容器切换历史，防止无限循环切换

### 2.2 项目分析智能体（ProjectAnalyzer）
**架构设计**：采用ReAct框架的独立智能体，内部集成多种分析工具，通过智能决策选择最优的分析策略。

**核心职责**（参考AutoCompiler的实现方式）：
- 综合运用ccscanner和直接LLM文档分析技术深度分析项目结构
- 提取项目依赖信息并判断安装方式（apt、pip、源码编译等）
- 从文档中直接用LLM提取编译指令组
- 解决ccscanner与文档分析结果的冲突，优先采用文档分析结果

**内部工具集设计**：
```python
# 项目分析智能体的实现
class ProjectAnalyzer:
    def __init__(self, project_path: str, project_name: str):
        self.project_path = project_path
        self.project_name = project_name

        # LLM配置（完全参考AutoCompiler的方式）
        from config import LLM2_BASE_URL, LLM2_MODEL, LLM2_API_KEY
        self.llm = ChatOpenAI(
            base_url=LLM2_BASE_URL,
            model=LLM2_MODEL,
            api_key=LLM2_API_KEY,
            temperature=0.3,  # 项目分析需要更精确和一致的结果
            timeout=120       # 项目分析相对简单，不需要太长时间
        )

        # 从tools.py导入工具类
        from tools import DependencyScanner, DocumentAnalyzer

        # 初始化工具实例
        self.dependency_scanner = DependencyScanner()
        self.document_analyzer = DocumentAnalyzer(project_path, project_name)

    def analyze_comprehensive(self, *args):
        """
        Comprehensive analysis of C/C++ project structure, dependencies and build instructions.
        Combines ccscanner structured scanning with direct LLM document analysis (following AutoCompiler approach).

        @param: this function takes no parameter, analyzes the project at self.project_path
        @return: Structured string containing dependencies and build commands
        """
        # 构建内部工具集（参考AutoCompiler的工具集）
        tools = [
            Tool(
                name="DependencyScanner",
                description=self.dependency_scanner.scan_dependencies.__doc__,
                func=self.dependency_scanner.scan_dependencies
            ),
            Tool(
                name="DEBATEValidator",
                description=self.document_analyzer.debate_validate.__doc__,
                func=self.document_analyzer.debate_validate
            ),
            Tool(
                name="DocumentAnalyzer",
                description=self.document_analyzer.analyze_documents.__doc__,
                func=self.document_analyzer.analyze_documents
            ),
            Tool(
                name="BuildSystemDetector",
                description=self.document_analyzer.detect_build_system.__doc__,
                func=self.document_analyzer.detect_build_system
            ),
            Tool(
                name="DependencyResolver",
                description=self.resolve_dependency_conflicts.__doc__,
                func=self.resolve_dependency_conflicts
            )
        ]

        # 创建ReAct智能体
        agent = create_react_agent(llm=self.llm, tools=tools, prompt=PROJECT_ANALYZER_TEMPLATE)
        agent_executor = AgentExecutor(
            agent=agent,
            tools=tools,
            max_iterations=15,
            verbose=True,
            handle_parsing_errors=True
        )

        # 执行分析任务（完全参考AutoCompiler的调用方式）
        analysis_prompt = f"Analyze C/C++ project at {self.project_path}, extract dependencies and build instructions"
        answer = agent_executor.invoke({"input": analysis_prompt})

        # 记录日志（参考AutoCompiler的日志格式）
        self.logger.append([
            "analyze_comprehensive",
            self.project_name,
            answer['output']
        ])

        return answer['output']  # 统一返回answer['output']

    def resolve_dependency_conflicts(self, ccscanner_deps: str, doc_deps: str) -> str:
        """
        Resolve conflicts between ccscanner results and document analysis results.
        Priority: Document analysis > ccscanner results.

        @param ccscanner_deps: JSON string of ccscanner dependency results
        @param doc_deps: JSON string of document analysis dependency results
        @return: JSON string with resolved final dependency list
        """
        try:
            cc_data = json.loads(ccscanner_deps) if ccscanner_deps else {"dependencies": []}
            doc_data = json.loads(doc_deps) if doc_deps else {"dependencies": []}

            final_deps = []

            # 优先采用文档分析结果
            for doc_dep in doc_data.get("dependencies", []):
                final_deps.append({
                    **doc_dep,
                    "source": "document_analysis",
                    "priority": "high"
                })

            # 添加ccscanner中文档分析未包含的依赖
            doc_names = {dep['name'] for dep in doc_data.get("dependencies", [])}
            for cc_dep in cc_data.get("dependencies", []):
                if cc_dep['name'] not in doc_names:
                    final_deps.append({
                        **cc_dep,
                        "source": "ccscanner_supplementary",
                        "priority": "medium"
                    })

            return json.dumps({
                "dependencies": final_deps,
                "resolution_status": "success",
                "total_count": len(final_deps)
            })

        except Exception as e:
            return json.dumps({
                "dependencies": [],
                "resolution_status": "failed",
                "error": str(e)
            })
```

**工作流程**：
1. **结构化扫描**：使用CCScanner工具进行依赖扫描，过滤低置信度结果
2. **文档智能解析**：使用DocumentAnalyzer工具分析README、BUILD、INSTALL等文档
3. **冲突解决**：通过DependencyResolver工具解决ccscanner与文档分析的冲突
4. **构建系统检测**：当文档分析失败时，使用BuildSystemDetector作为备选方案
5. **结果整合**：输出最终的依赖列表（含安装方式）和编译指令组

**输出格式标准化**：
```json
{
  "dependencies": [
    {
      "name": "libssl-dev",
      "install_method": "apt",
      "version": "1.1.1f",
      "source_url": null,
      "confidence": 0.95
    },
    {
      "name": "boost",
      "install_method": "source",
      "version": "latest",
      "source_url": "https://github.com/boostorg/boost.git",
      "confidence": 0.85
    }
  ],
  "build_commands": [
    "mkdir -p build && cd build",
    "cmake ..",
    "make -j$(nproc)"
  ],
  "build_system": "CMake",
  "analysis_notes": "项目使用CMake构建系统，依赖OpenSSL和Boost库",
  "confidence_score": 0.92
}
```

### 2.3 错误处理智能体（ErrorSolver）
**架构设计**：采用ReAct框架的专业化错误诊断智能体，内部集成多种错误分析和解决方案搜索工具。

**核心职责**：
- 深度分析编译错误的根本原因
- 通过GitHub Issues和Google Search获取解决方案
- 提供具体可执行的修复指令
- 评估解决方案的置信度和可行性

**内部工具集设计**：
```python
# 错误处理智能体的实现
class ErrorSolver:
    def __init__(self, project_name: str):
        self.project_name = project_name

        # LLM配置（完全参考AutoCompiler的方式）
        from config import LLM3_BASE_URL, LLM3_MODEL, LLM3_API_KEY
        self.llm = ChatOpenAI(
            base_url=LLM3_BASE_URL,
            model=LLM3_MODEL,
            api_key=LLM3_API_KEY,
            temperature=0.8,  # 错误处理需要更多创造性思维
            timeout=150       # 错误处理可能需要更多时间分析
        )

        # 从tools.py导入工具类
        from tools import GitHubManager, GoogleSearchAgent

        # 初始化工具实例
        self.github_manager = GitHubManager(proxy=PROXY, github_token=GITHUB_TOKEN)
        self.google_search = GoogleSearchAgent(serper_api_key=SERPER_API_KEY, proxy=PROXY)

    def solve(self, error_context: str) -> str:
        """
        Comprehensive analysis and resolution of C/C++ compilation errors.
        Uses GitHub Issues search and Google search to find similar problems and solutions.

        @param error_context: JSON string containing error details, project context, and compilation stage
        @return: JSON string with analyzed solutions and confidence ratings
        """
        # 构建内部工具集
        tools = [
            Tool(
                name="GitHubIssuesSearcher",
                description=self.github_manager.search_issues.__doc__,
                func=self.github_manager.search_issues
            ),
            Tool(
                name="GoogleSearcher",
                description=self.google_search.search_compilation_solutions.__doc__,
                func=self.google_search.search_compilation_solutions
            ),
            Tool(
                name="QueryConstructor",
                description=self.google_search.construct_search_query.__doc__,
                func=self.google_search.construct_search_query
            ),
            Tool(
                name="SolutionExtractor",
                description=self.google_search.extract_solutions_from_results.__doc__,
                func=self.google_search.extract_solutions_from_results
            ),
            Tool(
                name="ErrorAnalyzer",
                description=self.analyze_error_pattern.__doc__,
                func=self.analyze_error_pattern
            ),
            Tool(
                name="SolutionValidator",
                description=self.validate_solution.__doc__,
                func=self.validate_solution
            )
        ]

        # 创建ReAct智能体
        agent = create_react_agent(llm=self.llm, tools=tools, prompt=ERROR_SOLVER_TEMPLATE)
        agent_executor = AgentExecutor(
            agent=agent,
            tools=tools,
            max_iterations=10,
            verbose=True,
            handle_parsing_errors=True
        )

        # 执行错误解决任务
        result = agent_executor.invoke({
            "input": error_context,
            "project_name": self.project_name,
            "compilation_stage": "unknown",  # 从error_context中提取
            "error_details": error_context
        })

        return result["output"]

    def analyze_error_pattern(self, error_message: str, compilation_stage: str = "unknown") -> str:
        """
        Analyze error patterns to categorize the type of compilation problem.
        Identifies common error categories: dependency missing, version conflicts, syntax errors, etc.

        @param error_message: Raw error message from compilation output
        @param compilation_stage: Stage where error occurred (dependency_install/source_compile/main_compile)
        @return: JSON string with error categorization and preliminary analysis
        """
        try:
            # 错误类型分类
            error_categories = {
                "dependency_missing": ["no such file", "cannot find", "not found", "missing"],
                "compile_error": ["error:", "compilation terminated", "syntax error"],
                "linker_error": ["undefined reference", "cannot find -l", "ld: error"],
                "permission_denied": ["permission denied", "access denied"],
                "timeout_error": ["timeout", "killed", "terminated"]
            }

            error_lower = error_message.lower()
            detected_category = "unknown"
            confidence = 0.0

            for category, keywords in error_categories.items():
                for keyword in keywords:
                    if keyword in error_lower:
                        detected_category = category
                        confidence = 0.8
                        break
                if detected_category != "unknown":
                    break

            # 提取关键错误信息
            key_terms = []
            import re
            # 提取可能的库名、文件名等
            patterns = [
                r'lib\w+',  # 库名
                r'\w+\.h',  # 头文件
                r'\w+\.so', # 动态库
                r'\w+\.a'   # 静态库
            ]

            for pattern in patterns:
                matches = re.findall(pattern, error_message)
                key_terms.extend(matches)

            return json.dumps({
                "error_category": detected_category,
                "confidence": confidence,
                "key_terms": key_terms[:5],  # 限制数量
                "compilation_stage": compilation_stage,
                "analysis_status": "success"
            })

        except Exception as e:
            return json.dumps({
                "error_category": "unknown",
                "confidence": 0.0,
                "key_terms": [],
                "analysis_status": "failed",
                "error": str(e)
            })

    def validate_solution(self, proposed_solution: str, error_context: str) -> str:
        """
        Validate the feasibility and safety of proposed solutions.
        Checks for potential side effects and compatibility with the target environment.

        @param proposed_solution: Solution commands or instructions to validate
        @param error_context: Original error context for validation reference
        @return: JSON string with validation results and risk assessment
        """
        try:
            solution_data = json.loads(proposed_solution) if isinstance(proposed_solution, str) else proposed_solution

            # 基本安全检查
            risk_level = "low"
            warnings = []

            commands = solution_data.get("commands", [])
            for command in commands:
                if isinstance(command, str):
                    # 检查危险命令
                    dangerous_patterns = ["rm -rf", "sudo rm", "format", "mkfs"]
                    for pattern in dangerous_patterns:
                        if pattern in command.lower():
                            risk_level = "high"
                            warnings.append(f"Dangerous command detected: {pattern}")

                    # 检查系统级修改
                    system_patterns = ["sudo", "chmod 777", "chown"]
                    for pattern in system_patterns:
                        if pattern in command.lower():
                            if risk_level == "low":
                                risk_level = "medium"
                            warnings.append(f"System-level modification: {pattern}")

            return json.dumps({
                "validation_status": "success",
                "risk_level": risk_level,
                "warnings": warnings,
                "is_safe": risk_level != "high",
                "recommendations": self._generate_safety_recommendations(risk_level, warnings)
            })

        except Exception as e:
            return json.dumps({
                "validation_status": "failed",
                "risk_level": "unknown",
                "warnings": [],
                "is_safe": False,
                "error": str(e)
            })

    def _generate_safety_recommendations(self, risk_level: str, warnings: list) -> list:
        """生成安全建议"""
        recommendations = []

        if risk_level == "high":
            recommendations.append("Review commands carefully before execution")
            recommendations.append("Consider running in isolated environment")
        elif risk_level == "medium":
            recommendations.append("Monitor system changes during execution")
            recommendations.append("Have rollback plan ready")

        if warnings:
            recommendations.append("Address specific warnings before proceeding")

        return recommendations
```

**工作流程**：
1. **错误分析**：使用ErrorAnalyzer工具分析错误类型和根本原因
2. **解决方案搜索**：并行使用GitHubIssuesSearcher和GoogleSearcher工具搜索相关解决方案
3. **方案验证**：通过SolutionValidator工具验证解决方案的可行性和安全性
4. **结果整合**：综合分析结果，提供排序后的解决方案列表

**输出格式标准化**：
```json
{
  "error_analysis": "缺少开发库文件，需要安装libssl-dev包",
  "error_category": "dependency_missing",
  "solutions": [
    {
      "description": "安装OpenSSL开发库",
      "commands": ["apt update", "apt install -y libssl-dev"],
      "confidence": 0.9,
      "source": "github_issues",
      "risk_level": "low"
    },
    {
      "description": "从源码编译OpenSSL",
      "commands": ["git clone https://github.com/openssl/openssl.git", "cd openssl && ./config && make"],
      "confidence": 0.7,
      "source": "google_search",
      "risk_level": "medium"
    }
  ],
  "recommended_solution": 0,
  "overall_confidence": 0.85,
  "search_results_count": 15
}
```

**编译错误处理策略**：
- 当主控智能体尝试3次无法解决编译错误后，错误处理智能体将尝试最多3次来解决问题
- 如果仍无法解决则认为该编译错误解决失败，记录详细的失败原因和尝试过的解决方案
- 采用单一智能体架构，通过GitHub Issues搜索和Google搜索获取解决方案
- 不使用多智能体讨论机制，确保解决方案的一致性和高效性

**版本切换决策机制**：
- 版本切换完全由主控智能体通过ReAct框架智能决策
- 在主控智能体提示词中说明版本切换能力和策略
- 主控智能体分析编译错误，思考是否为版本兼容性问题
- 如果判断需要切换版本，主控智能体调用VersionSwitcher工具
- 不使用预设的错误模式匹配，而是依靠智能体的推理能力

**错误处理触发机制**（参考AutoCompiler）：
- 主控智能体自主判断是否"遇到无法解决的问题"
- 不是固定的3次重试后调用，而是智能体自主决策何时调用ErrorSolver
- 主控智能体可以在任何时候调用错误处理智能体
- 提示词指导："Unless you encounter a problem that you cannot solve, there is no need to call the ErrorSolver tool"

## 3. 工具类设计（tools.py）
参考AutoCompiler的实现方式，所有工具类都集中在`tools.py`文件中，每个工具类都是独立的、可实例化的类，具有清晰的docstring描述和标准化的接口设计。

### 3.1 依赖扫描工具类（DependencyScanner）
**功能描述**：封装ccscanner为智能体可调用的工具，提供结构化的依赖扫描能力。

**类设计**：
```python
# tools.py
class DependencyScanner:
    def __init__(self, confidence_threshold=['High', 'Medium']):
        """
        Initialize dependency scanner with confidence filtering.

        @param confidence_threshold: List of acceptable confidence levels
        """
        self.confidence_threshold = confidence_threshold
        self.logger = []

    def scan_dependencies(self, project_path: str) -> str:
        """
        Execute ccscanner tool for structured dependency scanning of C/C++ projects.
        Filters results by confidence level (High/Medium only) and processes version conflicts.

        @param project_path: Absolute path to the project directory
        @return: JSON string containing filtered and processed dependency information
        """
        try:
            # 调用ccscanner并处理结果
            scan_results = self._run_ccscanner(project_path)
            processed_deps = self._process_ccscanner_results(scan_results)

            self.logger.append([
                "scan_dependencies",
                project_path,
                f"Found {len(processed_deps)} dependencies"
            ])

            return json.dumps({
                "dependencies": processed_deps,
                "scan_status": "success",
                "total_count": len(processed_deps)
            })
        except Exception as e:
            return json.dumps({
                "dependencies": [],
                "scan_status": "failed",
                "error": str(e)
            })

    def _run_ccscanner(self, project_path: str) -> dict:
        """Execute ccscanner command and return raw results"""
        try:
            import tempfile
            import json

            # 创建临时文件保存ccscanner结果
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.json', delete=False) as temp_file:
                temp_path = temp_file.name

            # 执行ccscanner命令
            cmd = f"ccscanner_print -d {project_path} -t {temp_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)

            if result.returncode != 0:
                self.logger.append(["_run_ccscanner", project_path, f"ccscanner failed: {result.stderr}"])
                return {}

            # 读取ccscanner结果
            try:
                with open(temp_path, 'r', encoding='utf-8') as f:
                    scan_results = json.load(f)
                os.unlink(temp_path)  # 清理临时文件
                return scan_results
            except (json.JSONDecodeError, FileNotFoundError) as e:
                self.logger.append(["_run_ccscanner", project_path, f"Failed to parse ccscanner output: {str(e)}"])
                return {}

        except Exception as e:
            self.logger.append(["_run_ccscanner", project_path, f"ccscanner execution error: {str(e)}"])
            return {}

    def _process_ccscanner_results(self, scan_results: dict) -> list:
        """
        Process ccscanner raw results: filter by confidence, handle version conflicts.
        Takes union of all extractor results and resolves duplicate dependencies.
        """
        # 置信度筛选和版本冲突处理
        pass
```

**使用方式**：
```python
# 在智能体中使用
dependency_scanner = DependencyScanner()
tools = [
    Tool(
        name="DependencyScanner",
        description=dependency_scanner.scan_dependencies.__doc__,
        func=dependency_scanner.scan_dependencies
    )
]
```

### 3.2 Docker管理工具类（InteractiveDockerShell）
**功能描述**：完全采用AutoCompiler的InteractiveDockerShell实现，经过实际验证的成熟方案。

**类设计**：
```python
# tools.py - 完全参考AutoCompiler的实现
import paramiko
import subprocess
import time
import json
import chardet
import re

class InteractiveDockerShell:
    HOSTNAME = 'c0mpi1er-c0nta1ner'  # 使用AutoCompiler的主机名

    # 多版本Docker镜像支持
    AVAILABLE_IMAGES = {
        "18.04": "autocompiler:ubuntu18.04",
        "20.04": "autocompiler:ubuntu20.04",
        "22.04": "autocompiler:ubuntu22.04"
    }
    DEFAULT_VERSION = "20.04"

    def __init__(self, local_path, ubuntu_version=None, use_proxy=False,
                 stuck_timeout=120, cmd_timeout=3600, pre_exec=True):
        """
        完全参考AutoCompiler的初始化实现，增加多版本Ubuntu支持
        """
        # 确定使用的Ubuntu版本和镜像
        self.ubuntu_version = ubuntu_version or self.DEFAULT_VERSION
        if self.ubuntu_version not in self.AVAILABLE_IMAGES:
            raise Exception(f"Unsupported Ubuntu version: {self.ubuntu_version}")

        image_name = self.AVAILABLE_IMAGES[self.ubuntu_version]
        self.local_path = local_path

        try:
            # 一行命令创建容器（完全参考AutoCompiler）
            container_id = subprocess.run(
                f"docker run --network bridge --hostname {self.HOSTNAME} -v {local_path}:/work/ -itd {image_name} /bin/bash",
                shell=True, capture_output=True
            ).stdout.decode().strip()

            assert len(container_id)==64, "Failed to create the container."
            logging.info(f"[+] Container {container_id} created.")

            # 获取容器IP地址
            json_str = subprocess.run(f"docker inspect {container_id}", shell=True, capture_output=True).stdout.decode()
            ipaddr = json.loads(json_str)[0]['NetworkSettings']['IPAddress']

            # 启动SSH服务
            retcode = subprocess.run(f"docker exec {container_id} /bin/bash -c 'service ssh start'",
                                   shell=True, capture_output=True).returncode
            assert retcode==0, "Failed to start the ssh service."

        except Exception as e:
            raise Exception(f"Failed to create the container, error: {str(e)}")

        # 建立SSH连接（完全参考AutoCompiler）
        self.client = paramiko.SSHClient()
        self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.client.connect(ipaddr, username='root', password='root', port=22)
        self.session = self.client.invoke_shell()
        self.session.settimeout(stuck_timeout)
        time.sleep(1)
        self.session.recv(1024)  # skip the welcome message

        self.stuck_timeout = stuck_timeout
        self.cmd_timeout = cmd_timeout
        self.container_id = container_id
        self.last_line = "root@c0mpi1er-c0nta1ner:/work# "
        self.logger = []

        if pre_exec:
            self.execute_command("proxychains -q apt update")
        if use_proxy:
            self.execute_command("proxychains -q /bin/bash")

    def execute_command(self, command: str) -> str:
        """
        Execute a command in a interactive shell. 完全参考AutoCompiler的实现。
        @param command: The command to execute.
        """
        if self.session is None:
            raise Exception("No session available.")

        # 完全参考AutoCompiler的命令预处理
        command = command.strip()
        for wrap in ["`", "\"", "**", "```"]:
            if command.startswith(wrap) and command.endswith(wrap):
                command = command[len(wrap):-len(wrap)]

        # 智能代理处理
        if "git " in command and "proxychains git " not in command:
            command = command.replace("git ", "proxychains -q git ")
        if "curl " in command and "proxychains curl " not in command:
            command = command.replace("curl ", "proxychains -q curl ")
        if "wget " in command and "proxychains wget " not in command:
            command = command.replace("wget ", "proxychains -q wget ")

        # 自动添加-y参数
        if "apt install " in command:
            command = command.replace("apt install ", "apt install -y ")
        if "apt-get install " in command:
            command = command.replace("apt-get install ", "apt-get install -y ")

        # 特殊命令处理
        if command.strip() == "^C":
            command = '\x03'
        if "make install" in command:
            return "Tips: Do not install the project, just compile it!"
        if "make" == command.strip():
            command = "make -j32"  # 自动并行编译

        self.session.send(command + '\n')

        # 完全参考AutoCompiler的超时和输出处理逻辑
        cmd_start_time = time.time()
        start_time = time.time()
        output = ""

        while True:
            if time.time() - cmd_start_time > self.cmd_timeout or \
                time.time() - start_time > self.stuck_timeout:
                self.session.send('\x03')  # 发送Ctrl+C
                flag = "\nCommand execution timeout!\n"
                while self.HOSTNAME not in output:
                    if time.time() - start_time > self.stuck_timeout * 2:
                        raise Exception(f"Command timeout and cannot be stopped, cmd={command}")
                    try:
                        recv = self.session.recv(1024)
                    except:
                        flag = "\nShell has stuck by waiting input. You can still input something needed to handle this stuck, just input what needed in raw with SHELL tool.\n"
                        break
                    output += recv.decode(encoding="utf-8", errors="ignore")
                output += flag
                return self.omit(command, output, time.time()-cmd_start_time)

            if self.HOSTNAME in output:  # 返回条件
                return self.omit(command, output, time.time()-cmd_start_time)

            # 读取输出
            if self.session.recv_ready():
                while self.session.recv_ready():
                    recv = self.session.recv(1024)
                    output += recv.decode(encoding="utf-8", errors="ignore")
                time.sleep(0.5)
                start_time = time.time()  # 重置开始时间
            else:
                time.sleep(0.5)

    def omit(self, command, output, duration) -> str:
        """
        完全参考AutoCompiler的智能输出省略策略
        """
        output = re.sub(r'\x1B[@-_][0-?]*[ -/]*[@-~]', '', output)  # 移除ANSI转义字符
        output = self.last_line + output
        self.logger.append([command, output, duration])

        # 获取输出的最后一行
        self.last_line = output.split("\n")[-1]

        # 智能输出省略
        if command.startswith("make"):
            return "\n".join(output.split("\n")[-50:])  # make命令只保留最后50行
        elif command.startswith("configure") or command.startswith("./configure"):
            return "\n".join(output.split("\n")[-30:])  # configure命令只保留最后30行
        elif command.startswith("cmake"):
            return "\n".join(output.split("\n")[-30:])  # cmake命令只保留最后30行
        else:
            if len(output) > 8000:
                output = output[:4000] + "\n......\n" + output[-4000:]  # 中间省略策略
            return output

    def close(self):
        """完全参考AutoCompiler的资源清理"""
        logging.info("[-] Stopping docker container, please wait a second...")
        if self.client:
            self.client.close()
        if self.container_id:
            subprocess.run(f"docker stop {self.container_id}", shell=True)
            subprocess.run(f"docker rm {self.container_id}", shell=True)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def switch_ubuntu_version(self, target_version: str) -> str:
        """
        Switch to different Ubuntu version container for system compatibility issues.
        由主控智能体调用的版本切换策略

        @param target_version: Target Ubuntu version (18.04/20.04/22.04)
        @return: Switch result message
        """
        if target_version not in self.AVAILABLE_IMAGES:
            return f"Error: Unsupported Ubuntu version: {target_version}"

        if target_version == self.ubuntu_version:
            return f"Already using Ubuntu {target_version}"

        try:
            # 销毁当前容器
            logging.info(f"[-] Switching from Ubuntu {self.ubuntu_version} to {target_version}")
            self.close()

            # 重新初始化为新版本容器
            self.__init__(
                local_path=self.local_path,
                ubuntu_version=target_version,
                use_proxy=False,
                stuck_timeout=self.stuck_timeout,
                cmd_timeout=self.cmd_timeout,
                pre_exec=True
            )

            logging.info(f"[+] Successfully switched to Ubuntu {target_version}")
            return f"Successfully switched to Ubuntu {target_version}"

        except Exception as e:
            return f"Failed to switch to Ubuntu {target_version}: {str(e)}"
```

**使用方式**：
```python
# 完全参考AutoCompiler的使用方式
with InteractiveDockerShell(local_path=project_path) as shell:
    tools = [
        Tool(
            name="Shell",
            description=shell.execute_command.__doc__,
            func=shell.execute_command
        )
    ]
```

### 3.3 项目下载和副本创建（简化实现）
**功能描述**：简化的项目管理功能，下载项目后调用一次副本创建即可，无需复杂的副本管理。

```python
# tools.py - 简化的项目管理功能
import uuid
import os
import subprocess
import logging

def download_project(url: str, local_path: str, download_proxy: str = None) -> bool:
    """
    Download project from GitHub repository. 完全采用AutoCompiler的实现。
    """
    logging.info(f"[-] Downloading project from {url} to {local_path}")

    if download_proxy is not None:
        cmd = f"git clone {url} {local_path} --config http.proxy={download_proxy}"
    else:
        cmd = f"git clone {url} {local_path}"

    logging.info(f"[-] Running command: {cmd}")
    ret = subprocess.run(cmd, shell=True, capture_output=True)

    if ret.returncode != 0:
        raise Exception(f"Failed to download project from {url}\n\n{ret.stderr.decode()}")

    return True

def copy_project(local_path: str) -> str:
    """
    Create project copy with UUID. 参考AutoCompiler但简化为单次调用。
    下载项目后调用一次即可，无需复杂的副本管理。
    """
    UUID = uuid.uuid4()
    local_path = os.path.abspath(local_path)
    new_path = f"{local_path}-{UUID}"

    cmd = f"chmod -R 777 {local_path} && cp -r {local_path} {new_path} && chmod -R 777 {new_path}"
    logging.info(f"[-] Copy project from {local_path} to {new_path}")

    ret = subprocess.run(cmd, shell=True, capture_output=True)
    if ret.returncode != 0:
        raise Exception(f"Failed to copy project: {ret.stderr}")

    return new_path
```

### 3.4 智能文档解析工具类（DocumentAnalyzer）
**功能描述**：集成RAG技术的智能文档解析工具，参考AutoCompiler的文档处理方式，能够自动发现、分析和提取编译相关信息。

**类设计**：
```python
# tools.py
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from bs4 import BeautifulSoup
import requests

class DocumentAnalyzer:
    def __init__(self, project_path: str, project_name: str):
        """
        Initialize document analyzer with RAG configuration.

        @param project_path: Path to the project directory
        @param project_name: Name of the project being analyzed
        """
        self.project_path = project_path
        self.project_name = project_name
        self.logger = []

        # 直接LLM分析配置（参考AutoCompiler的实现方式）
        # 不使用RAG，直接读取文件内容用LLM分析

        # 文档发现策略（改进的模糊匹配，参考AutoCompiler但更全面）
        self.compilation_keywords = [
            "readme", "build", "install", "contributing", "how-to", "compile",
            "compilation", "make", "cmake", "configure", "setup", "getting-started",
            "notes", "doc", "guide", "manual", "instruction", "building"
        ]
        self.document_extensions = ['.md', '.txt', '.rst', '.markdown']

        # 支持的构建系统
        self.build_system_files = {
            'CMake': ['CMakeLists.txt'],
            'Make': ['Makefile', 'GNUmakefile', 'makefile'],
            'Autotools': ['configure', 'configure.in', 'configure.ac'],
            'Ninja': ['build.ninja'],
            'Meson': ['meson.build'],
            'Bazel': ['BUILD', 'BUILD.bazel'],
            'Xmake': ['xmake.lua'],
            'Build2': ['manifest'],
            'Python': ['setup.py'],
            'Vcpkg': ['vcpkg.json'],
            'Shell': ['build.sh'],
            'Scons': ['SConstruct', 'SConscript'],
            'Premake5': ['premake5.lua']
        }

    def discover_compilation_documents(self, project_path: str) -> list:
        """
        Discover compilation-related documents using improved fuzzy matching.
        只处理根目录下的文档，避免文档数量激增。

        @param project_path: Path to the project directory
        @return: List of discovered document paths in root directory only
        """
        compilation_docs = []

        try:
            # 只扫描根目录，不递归子目录
            root_files = os.listdir(project_path)

            for file in root_files:
                file_path = os.path.join(project_path, file)

                # 跳过目录，只处理文件
                if not os.path.isfile(file_path):
                    continue

                file_lower = file.lower()

                # 模糊匹配：检查文件名是否包含编译相关关键词
                for keyword in self.compilation_keywords:
                    if keyword in file_lower:
                        compilation_docs.append(file_path)
                        break
                else:
                    # 特定后缀匹配（如果关键词匹配失败）
                    if any(file_lower.endswith(ext) for ext in self.document_extensions):
                        compilation_docs.append(file_path)

            # 去重（虽然在根目录下不太可能重复，但保险起见）
            compilation_docs = list(set(compilation_docs))

            self.logger.append([
                "discover_compilation_documents",
                project_path,
                f"Found {len(compilation_docs)} documents in root directory"
            ])

            return compilation_docs

        except Exception as e:
            self.logger.append([
                "discover_compilation_documents",
                project_path,
                f"Error: {str(e)}"
            ])
            return []

    def extract_build_instructions(self, documents_list: list) -> str:
        """
        Extract build instructions from discovered documents using RAG technology.
        完全采用AutoCompiler的实现策略：固定查询+简洁提示词+直接返回。

        @param documents_list: List of document paths to analyze
        @return: Extracted build instructions or error message
        """
        try:
            if not documents_list:
                return "No documents found for analysis"

            # 完全参考AutoCompiler的RAG实现
            docs = self._load_documents(documents_list)
            if not docs:
                return "Failed to load documents"

            # 设置向量存储（使用AutoCompiler的固定参数）
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.chunk_size,  # 3000
                chunk_overlap=self.chunk_overlap,  # 200
                add_start_index=True
            )

            all_splits = text_splitter.split_documents(docs)

            # 嵌入模型配置（完全参考AutoCompiler的方式）
            from config import EMBEDDING_BASE_URL, EMBEDDING_MODEL, EMBEDDING_API_KEY
            embeddings = OpenAIEmbeddings(
                model=EMBEDDING_MODEL,  # text-embedding-3-large
                openai_api_base=EMBEDDING_BASE_URL,
                openai_api_key=EMBEDDING_API_KEY
            )
            vectorstore = Chroma.from_documents(all_splits, embeddings)

            # 固定查询策略（完全参考AutoCompiler）
            query = "How to compile/build the project?"

            # 获取相关文档（完全参考AutoCompiler的get_relevant_docs）
            docs_and_scores = vectorstore.similarity_search_with_score(query)
            relevant_docs = [
                Document(page_content=doc.page_content, metadata=doc.metadata)
                for doc, score in docs_and_scores
                if score >= self.similarity_threshold
            ]

            if not relevant_docs:
                return "No compilation commands found."

            # 完全采用AutoCompiler的提示词模板
            template = """You are an experienced software development engineer and specialized in extracting compilation commands. The documents from a project repository will be provided and you need to carefully identify relevant compilation/building instructions. If no compilation commands are found, respond with "No compilation commands found." If found, list the compilation commands concisely, clearly, and professionally without any additional explanations.
            Documents: {text}
            Answer: """

            context = "\n".join(doc.page_content for doc in relevant_docs)

            # 完全参考AutoCompiler的LLM调用方式
            from langchain_openai import ChatOpenAI
            from langchain.prompts import PromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            from langchain_core.runnables import RunnableLambda

            llm = ChatOpenAI(temperature=1)
            prompt = PromptTemplate.from_template(template=template)
            rag_chain = (
                {"text": RunnableLambda(lambda x: context)}
                | prompt
                | llm
                | StrOutputParser()
            )

            answer = rag_chain.invoke({})

            # 记录日志（参考AutoCompiler的格式）
            self.logger.append([
                template.format(text=context),
                answer,
                [[doc.metadata, doc.page_content] for doc in relevant_docs],
                {"ori_content_len": len(context), "answer_len": len(answer)}
            ])

            return answer

        except Exception as e:
            return f"Search failed due to unknown reason."

    def debate(self, file_path: str) -> str:
        """
        A tool for checking the file which may exist compilation instructions.
        完全参考AutoCompiler的DEBATE工具实现。

        @param file_path: the file path to be checked.
        @return: Analysis result of whether the file contains compilation instructions
        """
        try:
            from langchain_openai import ChatOpenAI
            from langchain.prompts import PromptTemplate
            from langchain_core.output_parsers import StrOutputParser

            llm = ChatOpenAI(temperature=1)

            # 完全参考AutoCompiler的DEBATE提示词模板
            template = """You are an experienced project developer, and you are good at searching project compilation instructions. Project structure and files which may exist compilation instructions are provided. Please analyze the structure and output the files which may exist commpilation instructions.

Project structure: {project_structure}

File: {file_path}

Output: <files which have been revised>"""

            prompt = PromptTemplate.from_template(template=template)
            chain = (prompt | llm | StrOutputParser())

            # 获取项目结构（简化版本）
            project_structure = self._get_project_structure()

            answer = chain.invoke({
                "project_structure": project_structure,
                "file_path": file_path
            })

            self.logger.append([
                "debate",
                file_path,
                answer
            ])

            return answer

        except Exception as e:
            return file_path  # 如果分析失败，返回原文件路径

    def _get_project_structure(self) -> str:
        """获取项目结构的简化表示，只显示根目录和一级子目录"""
        try:
            import os
            structure_lines = []

            # 根目录文件
            root_files = []
            root_dirs = []

            for item in os.listdir(self.project_path):
                item_path = os.path.join(self.project_path, item)
                if os.path.isfile(item_path):
                    root_files.append(item)
                elif os.path.isdir(item_path):
                    root_dirs.append(item)

            # 显示根目录结构
            structure_lines.append(f"{os.path.basename(self.project_path)}/")

            # 显示根目录下的目录（限制数量）
            for dir_name in root_dirs[:10]:
                structure_lines.append(f"  {dir_name}/")
            if len(root_dirs) > 10:
                structure_lines.append(f"  ... ({len(root_dirs)-10} more directories)")

            # 显示根目录下的文件（限制数量）
            for file_name in root_files[:15]:
                structure_lines.append(f"  {file_name}")
            if len(root_files) > 15:
                structure_lines.append(f"  ... ({len(root_files)-15} more files)")

            return '\n'.join(structure_lines)
        except:
            return "Project structure unavailable"

    def fetch_external_content(self, url: str) -> str:
        """
        Fetch and analyze content from external URLs referenced in documentation.
        Handles online build guides and external documentation links.

        @param url: URL to fetch content from
        @return: Extracted text content from the URL
        """
        try:
            headers = {'User-Agent': 'Mozilla/5.0 (compatible; AutoCompiler/1.0)'}
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            # 使用BeautifulSoup解析HTML内容
            soup = BeautifulSoup(response.text, "html.parser")
            texts = soup.get_text(separator="\n", strip=True)

            self.logger.append([
                "fetch_external_content",
                url,
                f"Fetched {len(texts)} characters"
            ])

            return texts

        except Exception as e:
            return f"Failed to fetch content from {url}: {str(e)}"

    def detect_build_system_fallback(self, project_path: str) -> str:
        """
        Fallback build system detection when document analysis fails.
        Identifies build system type from root directory files and infers build commands.

        @param project_path: Path to the project directory
        @return: JSON string with detected build system and inferred commands
        """
        try:
            detected_systems = {}

            # 只检查根目录，不递归子目录
            root_files = os.listdir(project_path)

            for build_system, build_files in self.build_system_files.items():
                for build_file in build_files:
                    if build_file in root_files:
                        detected_systems[build_system] = build_file
                        break

            if not detected_systems:
                return json.dumps({
                    "build_commands": [],
                    "build_system": "unknown",
                    "detection_status": "no_build_system_found"
                })

            # 选择构建系统（优先级或LLM决策）
            chosen_system = self._choose_build_system(detected_systems)
            inferred_commands = self._infer_build_commands(chosen_system)

            return json.dumps({
                "build_commands": inferred_commands,
                "build_system": chosen_system,
                "available_systems": list(detected_systems.keys()),
                "detection_status": "success",
                "confidence": 0.75
            })

        except Exception as e:
            return json.dumps({
                "build_commands": [],
                "build_system": "unknown",
                "detection_status": "failed",
                "error": str(e)
            })
```

**使用方式**：
```python
# 在项目分析智能体中使用
doc_analyzer = DocumentAnalyzer(project_path, project_name)
tools = [
    Tool(
        name="DocumentDiscoverer",
        description=doc_analyzer.discover_compilation_documents.__doc__,
        func=doc_analyzer.discover_compilation_documents
    ),
    Tool(
        name="BuildInstructionExtractor",
        description=doc_analyzer.extract_build_instructions.__doc__,
        func=doc_analyzer.extract_build_instructions
    ),
    Tool(
        name="ExternalContentFetcher",
        description=doc_analyzer.fetch_external_content.__doc__,
        func=doc_analyzer.fetch_external_content
    ),
    Tool(
        name="BuildSystemDetector",
        description=doc_analyzer.detect_build_system_fallback.__doc__,
        func=doc_analyzer.detect_build_system_fallback
    )
]
```

### 3.4 GitHub工具类（GitHubManager）
**功能描述**：参考AutoCompiler的简洁实现方式，提供GitHub项目克隆和Issues搜索功能。

**类设计**：
```python
# tools.py
import uuid
import subprocess
import requests
from github import Github

class GitHubManager:
    def __init__(self, proxy=None, github_token=None):
        """
        Initialize GitHub manager with optional proxy and authentication.

        @param proxy: Proxy configuration for network requests
        @param github_token: GitHub API token for authenticated requests
        """
        self.proxy = proxy
        self.github_token = github_token
        self.logger = []

        if github_token:
            self.github_client = Github(github_token)
        else:
            self.github_client = Github()

    def download_project(self, url: str, local_path: str, download_proxy: str = None, timeout: int = 120) -> bool:
        """
        Download project from GitHub repository. 完全采用AutoCompiler的实现。

        @param url: GitHub repository URL
        @param local_path: Local directory path for cloning
        @param download_proxy: Proxy URL for git clone
        @param timeout: Timeout in seconds
        @return: True if successful, raises Exception if failed
        """
        logging.info(f"[-] Downloading project from {url} to {local_path}")

        # 完全参考AutoCompiler的实现
        if download_proxy is not None:
            cmd = f"git clone {url} {local_path} --config http.proxy={download_proxy}"
        else:
            cmd = f"git clone {url} {local_path}"

        logging.info(f"[-] Running command: {cmd}")
        ret = subprocess.run(cmd, shell=True, capture_output=True)

        if ret.returncode != 0:
            raise Exception(f"Failed to download project from {url}\n\n{ret.stderr.decode()}")

        return True

    def copy_project(self, local_path: str) -> str:
        """
        Create project copy with UUID. 完全采用AutoCompiler的简洁实现。

        @param local_path: Path to the original project directory
        @return: Path to the new project copy
        """
        # 完全参考AutoCompiler的copy_project实现
        UUID = uuid.uuid4()
        local_path = os.path.abspath(local_path)
        new_path = f"{local_path}-{UUID}"

        cmd = f"chmod -R 777 {local_path} && cp -r {local_path} {new_path} && chmod -R 777 {new_path}"
        logging.info(f"[-] Copy project from {local_path} to {new_path}")

        ret = subprocess.run(cmd, shell=True, capture_output=True)
        if ret.returncode != 0:
            raise Exception(f"Failed to copy project from {local_path} to {new_path} {ret.stderr}")

        return new_path

    def search_issues(self, project_name: str, error_keywords: str) -> str:
        """
        Search GitHub Issues for compilation problems and solutions.
        简化返回格式，直接返回相关问题的描述。

        @param project_name: Name of the project to search issues for
        @param error_keywords: Key error terms extracted from compilation output
        @return: Relevant issues and solutions description
        """
        try:
            # 构建搜索查询
            search_query = f"repo:{project_name} {error_keywords} is:issue"

            # 搜索Issues
            issues = self.github_client.search_issues(query=search_query, sort="relevance", order="desc")

            relevant_solutions = []
            count = 0

            for issue in issues:
                if count >= 5:  # 限制结果数量
                    break

                # 简化解决方案提取
                if issue.state == "closed" and issue.body:
                    solution_text = f"Issue: {issue.title}\nSolution: {issue.body[:300]}..."
                    relevant_solutions.append(solution_text)

                count += 1

            if relevant_solutions:
                self.logger.append(["search_issues", f"{project_name} - {error_keywords}", f"Found {len(relevant_solutions)} solutions"])
                return "\n\n".join(relevant_solutions)
            else:
                return f"No relevant solutions found for {error_keywords} in {project_name}"

        except Exception as e:
            return f"GitHub search failed: {str(e)}"
```

**使用方式**：
```python
# 在主控智能体和错误处理智能体中使用
github_manager = GitHubManager(proxy=PROXY, github_token=GITHUB_TOKEN)
tools = [
    Tool(
        name="GitHubCloner",
        description=github_manager.clone_project.__doc__,
        func=github_manager.clone_project
    ),
    Tool(
        name="GitHubIssuesSearcher",
        description=github_manager.search_issues.__doc__,
        func=github_manager.search_issues
    ),
    Tool(
        name="ProjectCopyManager",
        description=github_manager.create_project_copy.__doc__,
        func=github_manager.create_project_copy
    )
]
```

### 3.5 Google搜索工具类（GoogleSearchAgent）
**功能描述**：参考AutoCompiler的GoogleSearch实现，提供智能化的网络搜索功能，为错误处理智能体获取编译问题解决方案。

**类设计**：
```python
# tools.py
from langchain_community.utilities import GoogleSerperAPIWrapper
import re

class GoogleSearchAgent:
    def __init__(self, serper_api_key: str, proxy=None):
        """
        Initialize Google search agent with Serper API.

        @param serper_api_key: API key for Google Serper service
        @param proxy: Proxy configuration for network requests
        """
        self.serper_api_key = serper_api_key
        self.proxy = proxy
        self.logger = []

        # 初始化搜索工具
        self.search_tool = GoogleSerperAPIWrapper(
            serper_api_key=serper_api_key,
            k=10  # 返回最多10个结果
        )

    def search_compilation_solutions(self, search_query: str, max_results: int = 5) -> str:
        """
        Search Google for compilation error solutions and troubleshooting guides.
        Uses intelligent query construction based on error type and project context.

        @param search_query: Constructed search query based on error analysis
        @param max_results: Maximum number of search results to return
        @return: JSON string with search results, extracted solutions, and relevance scores
        """
        try:
            # 执行搜索
            search_results = self.search_tool.run(search_query)

            # 解析搜索结果
            parsed_results = self._parse_search_results(search_results, max_results)

            # 提取解决方案
            solutions = []
            for result in parsed_results:
                extracted_solutions = self._extract_solutions_from_snippet(result)
                solutions.extend(extracted_solutions)

            # 按相关性排序
            solutions.sort(key=lambda x: x["relevance_score"], reverse=True)

            self.logger.append([
                "search_compilation_solutions",
                search_query,
                f"Found {len(solutions)} solutions from {len(parsed_results)} results"
            ])

            return json.dumps({
                "search_results": parsed_results,
                "extracted_solutions": solutions[:max_results],
                "total_results": len(parsed_results),
                "search_query": search_query,
                "search_status": "success"
            })

        except Exception as e:
            return json.dumps({
                "search_results": [],
                "extracted_solutions": [],
                "search_status": "failed",
                "error": str(e)
            })

    def construct_search_query(self, error_type: str, error_message: str, project_context: str) -> str:
        """
        Intelligently construct search queries based on error characteristics.
        Optimizes query terms for better solution relevance and accuracy.

        @param error_type: Categorized error type (dependency_missing, compile_error, etc.)
        @param error_message: Raw error message from compilation
        @param project_context: Project name and build system context
        @return: Optimized search query string
        """
        try:
            # 提取关键错误信息
            key_terms = self._extract_key_terms(error_message)

            # 根据错误类型构建查询
            query_templates = {
                "dependency_missing": f"{' '.join(key_terms)} missing dependency ubuntu install",
                "compile_error": f"{' '.join(key_terms)} compilation error C++ fix",
                "permission_denied": f"{' '.join(key_terms)} permission denied compilation fix",
                "timeout_error": f"{' '.join(key_terms)} compilation timeout fix",
                "linker_error": f"{' '.join(key_terms)} linker error undefined reference fix"
            }

            base_query = query_templates.get(error_type, f"{' '.join(key_terms)} compilation error fix")

            # 添加项目上下文
            if project_context:
                base_query += f" {project_context}"

            # 限制查询长度
            if len(base_query) > 100:
                base_query = base_query[:100]

            self.logger.append([
                "construct_search_query",
                f"{error_type} - {error_message[:50]}",
                f"Generated query: {base_query}"
            ])

            return base_query

        except Exception as e:
            # 回退到简单查询
            return f"compilation error fix {error_message[:50]}"

    def extract_solutions_from_results(self, search_results: str) -> str:
        """
        Extract actionable solutions from Google search results.
        Filters and ranks solutions based on relevance and feasibility.

        @param search_results: JSON string of search results
        @return: JSON string with extracted and ranked solutions
        """
        try:
            results_data = json.loads(search_results)
            search_results_list = results_data.get("search_results", [])

            all_solutions = []

            for result in search_results_list:
                # 从标题和摘要中提取解决方案
                title_solutions = self._extract_solutions_from_text(result.get("title", ""))
                snippet_solutions = self._extract_solutions_from_text(result.get("snippet", ""))

                # 合并解决方案
                result_solutions = title_solutions + snippet_solutions

                for solution in result_solutions:
                    solution["source_url"] = result.get("link", "")
                    solution["source_title"] = result.get("title", "")
                    all_solutions.append(solution)

            # 去重和排序
            unique_solutions = self._deduplicate_solutions(all_solutions)
            unique_solutions.sort(key=lambda x: x["confidence"], reverse=True)

            return json.dumps({
                "solutions": unique_solutions,
                "total_count": len(unique_solutions),
                "extraction_status": "success"
            })

        except Exception as e:
            return json.dumps({
                "solutions": [],
                "extraction_status": "failed",
                "error": str(e)
            })

    def _parse_search_results(self, raw_results: str, max_results: int) -> list:
        """解析原始搜索结果"""
        try:
            # 简单的结果解析（实际实现需要根据Serper API的返回格式调整）
            results = []
            lines = raw_results.split('\n')

            current_result = {}
            for line in lines:
                if line.startswith('Title:'):
                    if current_result:
                        results.append(current_result)
                    current_result = {"title": line[6:].strip()}
                elif line.startswith('Link:'):
                    current_result["link"] = line[5:].strip()
                elif line.startswith('Snippet:'):
                    current_result["snippet"] = line[8:].strip()

            if current_result:
                results.append(current_result)

            return results[:max_results]

        except:
            return []

    def _extract_key_terms(self, error_message: str) -> list:
        """从错误消息中提取关键词"""
        # 移除常见的噪音词
        noise_words = {'error', 'failed', 'cannot', 'unable', 'not', 'found', 'missing'}

        # 提取重要的技术词汇
        words = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', error_message.lower())
        key_terms = [word for word in words if len(word) > 2 and word not in noise_words]

        # 返回前5个最重要的词
        return key_terms[:5]

    def _extract_solutions_from_snippet(self, result: dict) -> list:
        """从搜索结果摘要中提取解决方案"""
        solutions = []
        snippet = result.get("snippet", "")

        # 查找包含解决方案关键词的句子
        solution_keywords = ["install", "fix", "solve", "solution", "resolved", "apt-get", "sudo"]

        for keyword in solution_keywords:
            if keyword in snippet.lower():
                solutions.append({
                    "description": snippet[:200],
                    "confidence": 0.6,
                    "relevance_score": 0.7,
                    "source": "google_search"
                })
                break

        return solutions

    def _extract_solutions_from_text(self, text: str) -> list:
        """从文本中提取解决方案"""
        solutions = []

        # 查找命令行指令
        command_patterns = [
            r'sudo\s+apt-get\s+install\s+[\w\-]+',
            r'apt\s+install\s+[\w\-]+',
            r'pip\s+install\s+[\w\-]+',
            r'make\s+[\w\-]*',
            r'cmake\s+[\w\-\.\/]*'
        ]

        for pattern in command_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                solutions.append({
                    "description": f"Execute command: {match}",
                    "commands": [match],
                    "confidence": 0.8,
                    "source": "command_extraction"
                })

        return solutions

    def _deduplicate_solutions(self, solutions: list) -> list:
        """去除重复的解决方案"""
        seen = set()
        unique_solutions = []

        for solution in solutions:
            solution_key = solution.get("description", "")[:50]
            if solution_key not in seen:
                seen.add(solution_key)
                unique_solutions.append(solution)

        return unique_solutions
```

**使用方式**：
```python
# 在错误处理智能体中使用
google_search = GoogleSearchAgent(serper_api_key=SERPER_API_KEY, proxy=PROXY)
tools = [
    Tool(
        name="GoogleSearcher",
        description=google_search.search_compilation_solutions.__doc__,
        func=google_search.search_compilation_solutions
    ),
    Tool(
        name="QueryConstructor",
        description=google_search.construct_search_query.__doc__,
        func=google_search.construct_search_query
    ),
    Tool(
        name="SolutionExtractor",
        description=google_search.extract_solutions_from_results.__doc__,
        func=google_search.extract_solutions_from_results
    )
]
```

### 3.6 tools.py整体设计总结
**设计原则**：参考AutoCompiler的tools.py实现方式，将所有工具类集中管理，确保代码的模块化和可维护性。

**文件结构**：
```python
# tools.py
import os
import json
import uuid
import time
import subprocess
import paramiko
import requests
from langchain.agents import Tool
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings
from langchain_community.utilities import GoogleSerperAPIWrapper
from github import Github
from bs4 import BeautifulSoup

# 工具类定义
class DependencyScanner:
    """ccscanner依赖扫描工具"""
    pass

class InteractiveDockerShell:
    """Docker SSH持久连接管理工具"""
    pass

class DocumentAnalyzer:
    """RAG智能文档解析工具"""
    pass

class GitHubManager:
    """GitHub项目管理和Issues搜索工具"""
    pass

class GoogleSearchAgent:
    """Google搜索和解决方案提取工具"""
    pass

# 工具实例化辅助函数
def create_tools_for_master_agent(project_path: str, project_name: str) -> list:
    """为主控智能体创建工具集"""
    docker_shell = InteractiveDockerShell(local_path=project_path)
    github_manager = GitHubManager(proxy=PROXY)

    return [
        Tool(name="Shell", description=docker_shell.execute_command.__doc__, func=docker_shell.execute_command),
        Tool(name="GitSnapshot", description=docker_shell.create_git_snapshot.__doc__, func=docker_shell.create_git_snapshot),
        # ... 其他工具
    ]

def create_tools_for_project_analyzer(project_path: str, project_name: str) -> list:
    """为项目分析智能体创建工具集"""
    dependency_scanner = DependencyScanner()
    document_analyzer = DocumentAnalyzer(project_path, project_name)

    return [
        Tool(name="DependencyScanner", description=dependency_scanner.scan_dependencies.__doc__, func=dependency_scanner.scan_dependencies),
        Tool(name="DocumentDiscoverer", description=document_analyzer.discover_compilation_documents.__doc__, func=document_analyzer.discover_compilation_documents),
        # ... 其他工具
    ]

def create_tools_for_error_solver(project_name: str) -> list:
    """为错误处理智能体创建工具集"""
    github_manager = GitHubManager(proxy=PROXY, github_token=GITHUB_TOKEN)
    google_search = GoogleSearchAgent(serper_api_key=SERPER_API_KEY, proxy=PROXY)

    return [
        Tool(name="GitHubIssuesSearcher", description=github_manager.search_issues.__doc__, func=github_manager.search_issues),
        Tool(name="GoogleSearcher", description=google_search.search_compilation_solutions.__doc__, func=google_search.search_compilation_solutions),
        # ... 其他工具
    ]
```

**设计优势**：
1. **集中管理**：所有工具类都在tools.py中，便于维护和更新
2. **标准化接口**：每个工具类都有清晰的docstring和统一的返回格式
3. **实例化隔离**：每个智能体使用独立的工具实例，避免状态冲突
4. **配置灵活**：支持代理、API密钥等配置参数的灵活传递
5. **日志记录**：每个工具类都内置日志记录功能，便于调试和监控
6. **异常处理**：统一的异常处理机制，确保工具调用的稳定性

**与AutoCompiler的对比**：
- **相同点**：采用相同的文件组织方式和工具类设计模式
- **增强点**：增加了RAG文档分析、ccscanner集成、多版本Docker支持等高级功能
- **优化点**：更完善的错误处理、日志记录和配置管理机制

## 4. 智能体工具调用关系
为确保方案实施的清晰性，各智能体的工具调用关系如下：

### 4.1 主控智能体 (o3模型)
调用工具：
- docker管理类：容器管理、SSH连接、命令执行、实时监控、二进制文件复制、文件系统快照
- github类：下载需要从源码编译的依赖项目 (clone功能)、项目副本管理

### 4.2 项目分析智能体 (claude-sonnet-4-20250514-thinking模型)
调用工具（参考AutoCompiler的实现方式）：
- DependencyScanner：ccscanner结构化依赖扫描（工具类）
- DocumentAnalyzer：直接LLM文档分析，提取编译指令和依赖（工具类）
- DEBATEValidator：文件验证，判断哪些文件值得分析（工具类）
- BuildSystemDetector：构建系统识别工具（工具类）

### 4.3 错误处理智能体 (claude-opus-4-20250514-thinking模型)
调用工具（参考AutoCompiler的工具分配）：
- GoogleSearchAgent：专门进行Google搜索获取编译问题解决方案（独立智能体）
- GitHubIssuesAgent：专门搜索GitHub Issues获取项目特定解决方案（独立智能体）
- ErrorAnalyzer：错误模式分析和分类工具（工具类）
- SolutionValidator：解决方案安全性和相关性验证工具（工具类）

**工具使用限制**：
- GoogleSearchAgent和GitHubIssuesAgent仅供错误处理智能体使用
- 主控智能体无法直接调用这些搜索工具
- 参考AutoCompiler：错误处理智能体内部调用Search_Agent（Google搜索智能体）

## 5. 智能体通信机制
采用基于LangChain Tool调用的同步通信机制，参考AutoCompiler的成熟实现方式：

### 5.1 通信架构
**基本架构**：主控智能体通过Tool方式调用其他智能体，采用同步执行模式

**数据格式标准**（完全参考AutoCompiler）：
区分两种LLM调用模式：

**1. 智能体调用（AgentExecutor）**：
- **调用方式**：`answer = agent_executor.invoke({"input": prompt})`
- **返回方式**：`return answer['output']`（从字典中取output键）
- **适用于**：主控智能体、项目分析智能体、错误处理智能体

**2. 工具类中的Chain调用**：
- **调用方式**：`chain = prompt | llm | StrOutputParser()`，`answer = chain.invoke(input_dict)`
- **返回方式**：`return answer`（直接返回字符串）
- **适用于**：DocumentAnalyzer、DEBATEValidator等工具类中的LLM调用

**优势**：统一接口、简化通信、易于调试、兼容性好

**调用示例**：
```python
# 1. 智能体调用（AgentExecutor模式）
analysis_result = self.project_analyzer.analyze_comprehensive()
# analysis_result 是字符串，来自answer['output']

solution_result = self.error_solver.solve_with_search(error_message)
# solution_result 是字符串，来自answer['output']

# 2. 工具类中的Chain调用（参考AutoCompiler）
class DocumentAnalyzer:
    def analyze_documents(self, file_paths):
        """参考AutoCompiler的search_instructions_from_files实现"""
        llm = ChatOpenAI(...)
        template = """You are an experienced software development engineer..."""
        prompt = PromptTemplate.from_template(template=template)
        chain = prompt | llm | StrOutputParser()
        answer = chain.invoke({"text": content, "file_path": file_path})
        return answer  # 直接返回字符串

    def debate_validate(self, file_path):
        """参考AutoCompiler的debate实现"""
        chain = prompt | llm | StrOutputParser()
        answer = chain.invoke({"project_structure": structure, "file": file_path})
        return answer  # 直接返回字符串

# 3. 主控智能体解析结果
if "ANALYSIS-COMPLETE" in analysis_result:
    dependencies = self._extract_dependencies(analysis_result)
    build_commands = self._extract_build_commands(analysis_result)
```

```python
# 智能体工具注册方式
tools = [
    Tool(
        name="ProjectAnalyzer",
        description=project_analyzer.analyze.__doc__,  # 使用函数文档字符串作为描述
        func=project_analyzer.analyze
    ),
    Tool(
        name="ErrorSolver",
        description=error_solver.solve.__doc__,
        func=error_solver.solve
    ),
    Tool(
        name="DockerManager",
        description=docker_manager.execute.__doc__,
        func=docker_manager.execute
    )
]

# LLM初始化（差异化配置）
def create_llm(agent_type: str):
    config = LLM_CONFIGS[agent_type]
    return ChatOpenAI(
        base_url="https://api.chatanywhere.tech/v1",
        api_key="sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU",
        model=config["model"],
        temperature=config["temperature"],
        timeout=config["timeout"],
        max_tokens=config["max_tokens"]
    )

# 智能体创建
agent = create_react_agent(llm=llm, tools=tools, prompt=prompt_template)
```

**自定义AgentExecutor实现**：
```python
from langchain.agents import AgentExecutor
from datetime import datetime
import json
import traceback

class AutoCompileAgentExecutor(AgentExecutor):
    """轻量级自定义，增强错误处理和日志记录"""
    
    def __init__(self, **kwargs):
        super().__init__(
            verbose=True,                        # 详细日志输出
            return_intermediate_steps=True,      # 返回中间步骤
            handle_parsing_errors=True,          # 自动处理解析错误
            early_stopping_method="generate",    # 遇到错误时生成解决方案
            **kwargs
        )
    
    def _handle_error(self, error: Exception, agent_action) -> dict:
        """统一错误处理，生成标准化错误格式"""
        return {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "error_context": f"Tool: {agent_action.tool}, Input: {agent_action.tool_input[:200]}",
            "timestamp": datetime.now().isoformat()
        }

# 智能体执行器创建
def create_agent_executor(agent, tools, agent_type):
    return AutoCompileAgentExecutor(
        agent=agent,
        tools=tools,
        max_iterations=LLM_CONFIGS[agent_type]["max_iterations"]
    )
```

### 5.2 数据传递格式
**标准化执行接口**：
```python
def execute_agent_task(agent_executor, input_data, task_type):
    """统一的智能体任务执行接口"""
    try:
        # 同步执行，便于错误处理和状态管理
        result = agent_executor.invoke({"input": input_data})
        
        # 标准化返回格式
        return {
            "success": True,
            "task_type": task_type,
            "final_answer": result.get("output", ""),
            "intermediate_steps": result.get("intermediate_steps", []),
            "execution_time": None,  # 可添加计时功能
            "error": None,
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        return {
            "success": False,
            "task_type": task_type,
            "final_answer": "",
            "intermediate_steps": [],
            "execution_time": None,
            "error": {
                "type": type(e).__name__,
                "message": str(e),
                "traceback": traceback.format_exc()
            },
            "timestamp": datetime.now().isoformat()
        }
```

**智能体间消息传递格式**：
```python
class AgentMessage:
    """标准化的智能体间消息格式"""
    
    def __init__(self, sender: str, receiver: str, message_type: str, data: dict):
        self.sender = sender
        self.receiver = receiver
        self.message_type = message_type  # "task_request", "task_response", "error_report"
        self.data = data
        self.timestamp = datetime.now().isoformat()
    
    def to_dict(self):
        return {
            "sender": self.sender,
            "receiver": self.receiver,
            "message_type": self.message_type,
            "data": self.data,
            "timestamp": self.timestamp
        }
```

**项目分析智能体返回格式**：
```json
{
  "dependencies": [
    {
      "name": "libssl-dev",
      "install_method": "apt",
      "version": "1.1.1f",
      "source_url": null
    },
    {
      "name": "boost",
      "install_method": "source",
      "version": "latest",
      "source_url": "https://github.com/boostorg/boost.git"
    }
  ],
  "build_commands": [
    "mkdir build && cd build",
    "cmake ..",
    "make -j8"
  ],
  "analysis_notes": "项目使用CMake构建系统，依赖OpenSSL和Boost库",
  "confidence_score": 0.95
}
```

**错误处理智能体返回格式**：
```json
{
  "error_analysis": "缺少开发库文件，需要安装libssl-dev包",
  "solutions": [
    {
      "description": "安装OpenSSL开发库",
      "commands": ["apt update", "apt install -y libssl-dev"],
      "confidence": 0.9
    },
    {
      "description": "从源码编译OpenSSL",
      "commands": ["git clone https://github.com/openssl/openssl.git", "cd openssl && ./config && make"],
      "confidence": 0.7
    }
  ],
  "recommended_solution": 0,
  "overall_confidence": 0.85
}
```

**数据传递特点**：
- **同步执行**：使用`invoke()`方法进行同步调用，便于错误处理和状态管理
- **结构化格式**：所有数据传递采用JSON格式，确保解析的准确性
- **完善异常处理**：统一的异常捕获和错误信息标准化
- **追踪信息**：包含时间戳、执行时间等追踪信息便于调试
- **置信度评估**：关键决策包含置信度评分，便于后续处理

### 5.3 调用流程
**标准化执行流程**：
1. **智能体初始化**：根据配置创建LLM和AgentExecutor
2. **任务分发**：主控智能体通过Tool调用方式分发任务
3. **同步执行**：使用`invoke()`方法同步执行，便于状态管理
4. **结果处理**：标准化处理返回结果，提取关键信息
5. **异常恢复**：统一的异常处理和错误传递机制
6. **状态追踪**：记录执行时间、中间步骤等调试信息

**具体执行示例**：
```python
# 主控智能体调用项目分析智能体
def call_project_analyzer(project_path: str, project_name: str):
    input_data = {
        "project_path": project_path,
        "project_name": project_name,
        "task": "analyze_dependencies_and_build_commands"
    }
    
    result = execute_agent_task(
        agent_executor=project_analyzer_executor,
        input_data=input_data,
        task_type="project_analysis"
    )
    
    if result["success"]:
        return json.loads(result["final_answer"])
    else:
        raise Exception(f"Project analysis failed: {result['error']}")

# 主控智能体调用错误处理智能体
def call_error_solver(error_context: dict):
    result = execute_agent_task(
        agent_executor=error_solver_executor,
        input_data=error_context,
        task_type="error_solving"
    )
    
    if result["success"]:
        return json.loads(result["final_answer"])
    else:
        # 错误处理智能体失败时，返回默认处理策略
        return {"error_analysis": "Error solver failed", "solutions": [], "overall_confidence": 0.1}
```

**错误处理和重试机制**：
- **Tool调用错误**：自动重新解析或使用默认错误工具
- **LLM响应超时**：根据timeout配置自动中断
- **JSON解析错误**：记录错误并返回标准化错误格式
- **网络异常**：支持重试机制，避免偶发网络问题
- **状态一致性**：确保每次调用后智能体状态正确更新

## 5.4 提示词模板设计
基于AutoCompiler的ReAct模板和工具调用机制，针对三智能体定制专门的提示词模板，每个智能体都采用标准的ReAct框架：

### **主控智能体提示词模板**：
```python
MASTER_AGENT_TEMPLATE = """You are an experienced C/C++ project compilation orchestrator. You coordinate the entire compilation process and make critical decisions.

Available tools:
{tools}

Your responsibilities:
1. Create project copy and manage Docker environment using DockerManager
2. Analyze project structure and dependencies using ProjectAnalyzer
3. Install dependencies and execute compilation commands
4. Monitor compilation progress in real-time via SSH connection
5. Handle compilation errors with retry logic (max 3 attempts)
6. Call ErrorSolver when self-resolution fails
7. **System version compatibility analysis and container switching**
8. Judge compilation success based on terminal output analysis
9. Manage compilation artifacts and copy to host machine

**Version Compatibility Error Analysis**:
When encountering compilation errors, analyze if they are system version compatibility issues:
- **GCC/G++ version conflicts**: Errors like "gcc: error: unrecognized command line option", "this GCC version doesn't support C++17"
- **System library version conflicts**: Errors like "GLIBC version not found", "libssl version mismatch"
- **Build tool version issues**: Errors like "cmake version too old", "autotools version incompatible"
- **Dependency package version conflicts**: Errors indicating system packages are too old/new

**Version Switching Strategy**:
- Default: Ubuntu 20.04 (GCC 9.x-11.x)
- If compatibility issues detected, analyze error and choose target version:
  - For older projects needing GCC 7.x-9.x → Switch to Ubuntu 18.04
  - For newer projects needing GCC 11.x-12.x → Switch to Ubuntu 22.04
- If uncertain, follow priority: 20.04 → 18.04 → 22.04
- Use ContainerVersionSwitcher tool to switch versions

Use the ReAct format:
Question: {input}
Thought: Analyze the current situation and plan next steps
Action: Choose from [{tool_names}]
Action Input: Specific input for the tool (use proper JSON format when required)
Observation: Result from the tool
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: COMPILATION-SUCCESS, COMPILATION-FAIL, or COMPILATION-UNCERTAIN

Current project: {project_name}
Project path: {project_path}
Docker environment: Ubuntu 20.04 (can switch to 18.04/22.04 if needed)

Begin!
Thought: {agent_scratchpad}"""
```

### **项目分析智能体提示词模板**：
```python
PROJECT_ANALYZER_TEMPLATE = """You are an expert C/C++ project dependency and build instruction analyzer.

Available tools:
{tools}

Your task: Analyze project {project_name} step by step using the provided tools.

Use the ReAct format:
Question: {input}
Thought: Plan the analysis approach and decide which tool to use
Action: Choose from [{tool_names}]
Action Input: Specific input for the tool
Observation: Result from the tool
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now have enough information to provide the final analysis
Final Answer: Complete analysis in JSON format

Analysis workflow:
1. Use CCScanner tool to extract structured dependencies
2. Use DocumentAnalyzer tool to parse README, BUILD, INSTALL files
3. Use DependencyResolver tool to resolve conflicts between results
4. Use BuildSystemDetector tool as fallback if document analysis fails

Analysis priority: Document analysis results > ccscanner results

Required JSON output format:
{{
    "dependencies": [
        {{"name": "lib_name", "install_method": "apt|pip|source", "version": "x.x.x", "source_url": "...", "confidence": 0.9}}
    ],
    "build_commands": ["cmd1", "cmd2", ...],
    "build_system": "CMake|Make|Autotools|...",
    "analysis_notes": "Key findings and recommendations",
    "confidence_score": 0.95
}}

Project path: {project_path}

Begin!
Thought: {agent_scratchpad}"""
```

### **错误处理智能体提示词模板**：
```python
ERROR_SOLVER_TEMPLATE = """You are a specialized C/C++ compilation error troubleshooting expert.

Available tools:
{tools}

Your task: Analyze and solve the compilation error step by step using the provided tools.

Use the ReAct format:
Question: {input}
Thought: Analyze the error and plan the troubleshooting approach
Action: Choose from [{tool_names}]
Action Input: Specific input for the tool
Observation: Result from the tool
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now have enough information to provide comprehensive solutions
Final Answer: Complete error analysis and solutions in JSON format

Error context:
Project: {project_name}
Compilation stage: {compilation_stage}
Error details: {error_details}

Troubleshooting workflow:
1. Use ErrorAnalyzer tool to categorize and analyze the error pattern
2. Use GitHubIssuesSearcher tool to find similar problems and solutions
3. Use GoogleSearcher tool to search for additional troubleshooting guides
4. Use SolutionValidator tool to validate proposed solutions

Required JSON output format:
{{
    "error_analysis": "Root cause explanation",
    "error_category": "dependency_missing|compile_error|permission_denied|...",
    "solutions": [
        {{"description": "Solution 1", "commands": ["cmd1", "cmd2"], "confidence": 0.9, "source": "github_issues", "risk_level": "low"}},
        {{"description": "Solution 2", "commands": ["cmd3"], "confidence": 0.7, "source": "google_search", "risk_level": "medium"}}
    ],
    "recommended_solution": 0,
    "overall_confidence": 0.85,
    "search_results_count": 15
}}

Begin!
Thought: {agent_scratchpad}"""
```

### **提示词设计原则**：
- **ReAct框架统一**：所有智能体都采用标准的思考-行动-观察循环格式
- **工具调用明确**：清晰的工具描述和使用指导，借鉴AutoCompiler的docstring机制
- **角色定位专业**：每个智能体的专业领域和职责清晰定义
- **输出格式标准化**：强制JSON输出，确保程序可解析和处理
- **上下文信息丰富**：包含项目名称、路径、错误详情等关键上下文信息
- **工作流程指导**：明确的步骤指导和工具使用顺序
- **优先级规则明确**：指定分析优先级和冲突解决规则
- **置信度评估要求**：要求智能体提供决策置信度和风险评估
- **错误处理友好**：支持解析错误自动恢复和标准化错误格式

## 5.5 借鉴AutoCompiler的优化实现细节

### **核心架构优化**
基于对AutoCompiler实现的深入分析，我们的方案在保持技术先进性的同时，借鉴其成熟的实现模式：

**1. 智能体内部ReAct框架统一**
- 每个智能体内部都采用标准的ReAct框架，而不是简单的函数调用
- 主控智能体通过Tool调用方式与其他智能体交互，保持架构清晰
- 项目分析智能体和错误处理智能体内部都有独立的工具集和决策逻辑

**2. 工具函数标准化设计**
```python
# 借鉴AutoCompiler的工具函数设计模式
def analyze_project(self, project_path: str) -> str:
    """
    Comprehensive analysis of C/C++ project structure, dependencies and build instructions.
    Combines ccscanner structured scanning with intelligent document parsing using RAG technology.

    @param project_path: Absolute path to the project directory
    @return: JSON string containing dependencies list with install methods and build commands
    """
    # 工具函数实现
    pass
```

**3. 提示词驱动的任务执行**
- 让LLM通过提示词理解任务并自主决策工具调用顺序
- 减少硬编码的流程控制，增加智能体的自主性和灵活性
- 采用AutoCompiler验证过的ReAct模板格式

**4. 错误处理和异常恢复**
- 集成AutoCompiler的AgentExecutor自定义机制
- 统一的异常处理和错误信息标准化
- 支持解析错误自动恢复和重试机制

### **技术实现优化**
**1. SSH持久连接管理**
- 借鉴AutoCompiler的InteractiveDockerShell实现
- 双重超时机制和智能中断处理
- 命令预处理和输出省略策略

**2. 容器环境管理**
- 参考AutoCompiler的容器创建和SSH连接建立流程
- 集成代理配置和权限管理
- 支持多版本Ubuntu容器的智能切换

**3. 工具调用机制**
- 采用LangChain Tool标准接口
- 清晰的工具描述和参数验证
- 支持JSON格式的复杂参数传递

### **保持技术优势**
在借鉴AutoCompiler成熟实现的同时，我们保持以下技术优势：
- **ccscanner集成**：结构化依赖扫描能力
- **直接LLM文档分析**：参考AutoCompiler，直接用LLM分析文档内容
- **循环依赖检测**：完善的递归深度限制和路径追踪
- **系统级错误处理**：容器版本自动切换机制
- **编译成功判断**：主控智能体智能决策，参考AutoCompiler实现
- **差异化LLM配置**：根据任务特点优化模型参数

## 6. 开发实施计划

### 6.1 开发阶段重新规划

基于模块化开发原则，将开发分为**功能开发阶段**和**集成测试阶段**，避免重复测试，提高开发效率。

#### **Phase 1: 基础工具模块开发**
**目标**: 开发所有基础工具类，不进行复杂项目测试
**完成标准**: 所有工具类单元测试通过，基础功能验证正常

**Docker多版本基础镜像策略（重量级镜像）**：

**镜像版本规划**：
- `autocompiler:ubuntu18.04` - Ubuntu 18.04 LTS，支持GCC 7.x-9.x，适用于较旧项目
- `autocompiler:ubuntu20.04` - Ubuntu 20.04 LTS，支持GCC 9.x-11.x，默认首选 ⭐
- `autocompiler:ubuntu22.04` - Ubuntu 22.04 LTS，支持GCC 11.x-12.x，最新标准

**版本切换策略**：
1. **默认启动**: Ubuntu 20.04容器开始编译
2. **错误检测**: 编译失败时主控智能体分析错误输出
3. **LLM智能判断**: 主控智能体使用LLM分析是否为系统版本兼容性问题
4. **目标版本选择**:
   - 优先根据错误信息LLM分析选择目标版本
   - 如果无法确定，按默认策略：20.04 → 18.04 → 22.04
5. **容器切换**: 销毁当前容器，创建目标版本容器，重新开始编译
6. **循环防护**: 记录切换历史，避免无限循环

**容器切换触发条件**：
- 依赖包版本与系统版本冲突（无法在当前环境解决）
- 项目要求的系统版本与当前版本不适配
- GCC编译器版本不兼容且无法通过安装解决
- 系统库版本冲突（glibc、libssl等）
- 特定构建工具版本依赖（cmake、autotools等）

**统一Dockerfile模板（以ubuntu20.04为例）**：
```dockerfile
FROM ubuntu:20.04

ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# 安装完整C/C++开发环境
RUN apt-get update && apt-get install -y \
    # 编译工具链
    build-essential gcc g++ clang \
    make cmake autoconf automake libtool \
    pkg-config ninja-build meson \
    # SSH和系统工具
    openssh-server tree vim git \
    # 构建依赖库
    zlib1g-dev libssl-dev libcurl4-openssl-dev \
    libncurses5-dev libreadline-dev libffi-dev \
    libbz2-dev libgdbm-dev libexpat1-dev \
    # 脚本语言
    python3 python3-pip python3-dev perl ruby \
    # 网络和下载工具
    wget curl unzip tar proxychains4 \
    # 汇编和调试工具
    yasm nasm gdb valgrind \
    # 文档和帮助工具
    texinfo help2man m4 bison flex patch \
    && rm -rf /var/lib/apt/lists/*

# SSH配置
RUN mkdir -p /var/run/sshd && \
    echo 'root:root' | chpasswd && \
    sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config && \
    sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config && \
    ssh-keygen -A

# Git全局配置
RUN git config --global user.email "<EMAIL>" && \
    git config --global user.name "AutoCompile" && \
    git config --global init.defaultBranch main && \
    git config --global safe.directory '*'

# Python基础包
RUN python3 -m pip install --no-cache-dir setuptools wheel

# 工作目录
WORKDIR /work
RUN echo "cd /work" >> ~/.bashrc

# 编译环境优化
ENV MAKEFLAGS="-j$(nproc)"
ENV CC=gcc
ENV CXX=g++

EXPOSE 22
CMD ["/bin/bash"]
```

**配置文件设计（参考AutoCompiler的config.py）**：
```python
# config.py - 系统配置文件（完全参考AutoCompiler的config.py）

# 主控智能体LLM配置
LLM1_BASE_URL = "https://api.chatanywhere.tech/v1"
LLM1_MODEL = "o3"
LLM1_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# 项目分析智能体LLM配置
LLM2_BASE_URL = "https://api.chatanywhere.tech/v1"
LLM2_MODEL = "claude-sonnet-4-20250514-thinking"
LLM2_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# 错误处理智能体LLM配置
LLM3_BASE_URL = "https://api.chatanywhere.tech/v1"
LLM3_MODEL = "claude-opus-4-20250514-thinking"
LLM3_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# 嵌入模型配置
EMBEDDING_BASE_URL = "https://api.chatanywhere.tech/v1"
EMBEDDING_MODEL = "text-embedding-3-large"
EMBEDDING_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# Docker镜像配置（为IDA Pro分析优化）
DOCKER_IMAGES = {
    "18.04": "autocompiler:ubuntu18.04",
    "20.04": "autocompiler:ubuntu20.04",
    "22.04": "autocompiler:ubuntu22.04"
}
DEFAULT_UBUNTU_VERSION = "20.04"

# 版本切换优先级策略
VERSION_SWITCH_PRIORITY = ["20.04", "18.04", "22.04"]

# IDA Pro分析优化配置
IDA_ANALYSIS_CONFIG = {
    "preserve_symbols": True,           # 保留符号信息
    "debug_info": "-g -gdwarf-4",      # 调试信息格式
    "strip_protection": True,          # 防止符号剥离
    "optimization_level": "-O1"        # 平衡优化和分析友好性
}

# 系统配置
DATASET_BASE_PATH = "/data/autocompile"
PROXY_CONFIG = None

# 智能体提示词模板配置
MASTER_AGENT_PROMPT = """You are an experienced C/C++ project compilation orchestrator.

Available tools:
{tools}

Your responsibilities:
1. Use ProjectAnalyzer to understand project dependencies and build system
2. Execute compilation commands using Shell
3. Handle errors using ErrorSolver when you encounter problems you cannot solve
4. Switch Ubuntu versions using VersionSwitcher if compatibility issues arise
5. Monitor compilation progress and judge success/failure

Version switching capability:
- Available Ubuntu versions: 20.04 (current), 18.04, 22.04
- Switch to 18.04 for older compatibility issues (GCC version conflicts, old libraries)
- Switch to 22.04 for newer features and dependencies
- You can switch versions when encountering version compatibility errors

Error handling strategy:
- Try to solve compilation problems yourself first
- Unless you encounter a problem that you cannot solve, there is no need to call the ErrorSolver tool
- Use ErrorSolver only when you are truly stuck and need external help

Compilation success judgment:
- You have the final decision authority on whether compilation succeeds or fails
- Analyze compilation output, error messages, and file system state comprehensively
- Check if compilation artifacts (executables, libraries) are generated
- Return COMPILATION-SUCCESS, COMPILATION-FAIL, or COMPILATION-UNCERTAIN based on your analysis

Use the ReAct format:
Question: {input}
Thought: I need to analyze the current situation and decide what to do next
Action: [one of {tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: COMPILATION-SUCCESS, COMPILATION-FAIL, or COMPILATION-UNCERTAIN

Project: {project_name}
Begin!

Question: {input}
Thought:{agent_scratchpad}"""

PROJECT_ANALYZER_PROMPT = """You are a C/C++ project analysis expert.

Available tools:
{tools}

Your task: Analyze project {project_name} comprehensively using the available tools.

Analysis workflow (following AutoCompiler approach):
1. Use DependencyScanner to extract structured dependencies with ccscanner
2. Use DEBATEValidator to identify which files may contain compilation instructions
3. Use DocumentAnalyzer to directly analyze validated files with LLM (no RAG)
4. Use BuildSystemDetector to identify build system if document analysis fails
5. Use DependencyResolver to resolve conflicts between different analysis results

Priority: Document analysis results > ccscanner results

Use the ReAct format:
Question: {input}
Thought: I need to understand this project's structure and dependencies
Action: [one of {tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now have enough information to provide comprehensive analysis
Final Answer: Complete project analysis with dependencies and build instructions

Project path: {project_path}
Begin!

Question: {input}
Thought:{agent_scratchpad}"""

ERROR_SOLVER_PROMPT = """You are a C/C++ compilation error resolution expert.

Available tools:
{tools}

Your task: Analyze compilation errors and find solutions using GitHub Issues and Google Search.

Core responsibilities:
1. Analyze compilation error patterns and categorize error types
2. Search GitHub Issues for project-specific solutions
3. Search Google for general compilation problem solutions
4. Validate proposed solutions for safety and relevance
5. Provide specific executable commands to resolve errors

Use the ReAct format:
Question: {input}
Thought: I need to analyze this error and search for solutions
Action: [one of {tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now have validated solutions to resolve this error
Final Answer: Recommended solutions with commands to execute

Error context:
Project: {project_name}
Error: {error_message}

Begin!

Question: {input}
Thought:{agent_scratchpad}"""
```

**Docker镜像构建**：
```bash
# 构建所有版本的Docker镜像
cd docker/
chmod +x build.sh
./build.sh

# 或者单独构建
docker build -f docker/Dockerfile.ubuntu20.04 -t autocompiler:ubuntu20.04 docker/
```

**SSH持久连接实现（基于AutoCompiler的InteractiveDockerShell）**：
```python
class DockerSSHManager:
    def __init__(self, local_path, image_name='autocompiler:gcc13-enhanced', 
                 stuck_timeout=120, cmd_timeout=3600):
        """
        参考AutoCompiler实现的SSH持久连接管理
        - stuck_timeout: 无输出超时（120秒）
        - cmd_timeout: 命令总执行超时（3600秒）
        """
        # 1. 创建容器
        container_id = subprocess.run(
            f"docker run --network bridge --hostname c0mpi1er-c0nta1ner "
            f"-v {local_path}:/work/ -itd {image_name} /bin/bash", 
            shell=True, capture_output=True
        ).stdout.decode().strip()
        
        # 2. 获取容器IP并启动SSH服务
        json_str = subprocess.run(f"docker inspect {container_id}", 
                                 shell=True, capture_output=True).stdout.decode()
        ipaddr = json.loads(json_str)[0]['NetworkSettings']['IPAddress']
        subprocess.run(f"docker exec {container_id} /bin/bash -c 'service ssh start'", 
                      shell=True, capture_output=True)
        
        # 3. 建立SSH连接
        self.client = paramiko.SSHClient()
        self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.client.connect(ipaddr, username='root', password='root', port=22)
        self.session = self.client.invoke_shell()
        self.session.settimeout(stuck_timeout)
        
        # 4. 初始化状态
        time.sleep(1)
        self.session.recv(1024)  # 跳过欢迎信息
        self.stuck_timeout = stuck_timeout
        self.cmd_timeout = cmd_timeout
        self.container_id = container_id
        self.last_line = "root@c0mpi1er-c0nta1ner:/work# "
        self.logger = []
        
        # 5. 预执行初始化（参考AutoCompiler）
        self.execute_command("proxychains -q apt update")
    
    def execute_command(self, command: str) -> str:
        """
        执行命令，完整实现双重超时和智能输出处理
        参考AutoCompiler的execute_command方法
        """
        # 命令预处理（集成AutoCompiler的网络代理逻辑）
        command = self._preprocess_command(command)
        
        self.session.send(command + '\n')
        
        cmd_start_time = time.time()
        start_time = time.time()
        output = ""
        
        while True:
            # 双重超时检测
            if (time.time() - cmd_start_time > self.cmd_timeout or 
                time.time() - start_time > self.stuck_timeout):
                self.session.send('\x03')  # 发送Ctrl+C
                # 等待中断响应...
                return self._handle_timeout(command, output, time.time()-cmd_start_time)
            
            # 检查是否完成
            if "c0mpi1er-c0nta1ner" in output:
                return self._process_output(command, output, time.time()-cmd_start_time)
            
            # 读取输出
            if self.session.recv_ready():
                while self.session.recv_ready():
                    recv = self.session.recv(1024)
                    output += recv.decode(encoding="utf-8", errors="ignore")
                time.sleep(0.5)
                start_time = time.time()  # 重置无输出计时
            else:
                time.sleep(0.5)
    
    def _preprocess_command(self, command: str) -> str:
        """命令预处理，集成代理和自动参数调整"""
        command = command.strip()
        
        # 网络命令自动添加代理
        if "git " in command and "proxychains git " not in command:
            command = command.replace("git ", "proxychains -q git ")
        if "curl " in command and "proxychains curl " not in command:
            command = command.replace("curl ", "proxychains -q curl ")
        if "wget " in command and "proxychains wget " not in command:
            command = command.replace("wget ", "proxychains -q wget ")
        
        # apt命令自动添加-y参数
        if "apt install " in command:
            command = command.replace("apt install ", "apt install -y ")
        
        # make命令优化
        if command.strip() == "make":
            command = "make -j$(nproc)"
        
        return command
    
    def _process_output(self, command: str, output: str, duration: float) -> str:
        """输出处理和省略策略（参考AutoCompiler）"""
        # 清理ANSI转义字符
        output = re.sub(r'\x1B[@-_][0-?]*[ -/]*[@-~]', '', output)
        output = self.last_line + output
        
        # 记录日志
        self.logger.append([command, output, duration])
        self.last_line = output.split("\n")[-1]
        
        # 智能输出省略
        if command.startswith("make"):
            return "\n".join(output.split("\n")[-50:])  # make输出只保留最后50行
        elif command.startswith(("configure", "./configure")):
            return "\n".join(output.split("\n")[-30:])  # configure输出保留30行
        elif command.startswith("cmake"):
            return "\n".join(output.split("\n")[-30:])
        else:
            if len(output) > 8000:
                output = output[:4000] + "\n......\n" + output[-4000:]
            return output
```

**文件系统快照实现（Git差异检测）**：
```python
def initialize_git_snapshot(self, project_path: str):
    """
    项目副本创建后立即初始化Git快照
    """
    commands = [
        "cd /work",
        "git init",
        "git add .",
        "git commit -m 'Initial snapshot before compilation'"
    ]
    
    for cmd in commands:
        result = self.execute_command(cmd)
        if "error" in result.lower() or "fatal" in result.lower():
            raise Exception(f"Git snapshot initialization failed: {result}")

def detect_compilation_artifacts(self) -> list:
    """
    使用Git差异检测编译产物（借鉴ghcc技术）
    """
    # 检测新增文件
    result = self.execute_command("git ls-files --others")
    new_files = [f.strip() for f in result.split('\n') if f.strip()]
    
    # 过滤编译产物
    artifacts = []
    for file in new_files:
        if self._is_compilation_artifact(file):
            artifacts.append(file)
    
    return artifacts

def _is_compilation_artifact(self, filename: str) -> bool:
    """判断文件是否为编译产物"""
    # 可执行文件：无扩展名且有执行权限
    if '.' not in os.path.basename(filename):
        return True
    
    # 库文件和目标文件
    extensions = ['.so', '.a', '.o', '.dylib', '.dll', '.exe']
    return any(filename.endswith(ext) for ext in extensions)
```

**测试项目配置**：
- **测试输入**：直接使用projects_url.txt（FFmpeg和OpenSSL）
- **验证目标**：基础环境搭建、SSH连接稳定性、Git快照功能
- **成功标准**：容器创建、SSH连接、命令执行、文件差异检测全部正常

**验收标准**：
- [ ] 所有工具类单元测试通过
- [ ] 使用简单的Hello World C项目验证基础功能
- [ ] Docker容器创建、切换、清理正常
- [ ] 不进行复杂项目的完整编译测试

#### **Phase 2: 智能体框架开发**
**目标**: 开发三个智能体的核心框架和通信机制
**完成标准**: 智能体框架搭建完成，工具调用机制正常

**ccscanner工具类集成策略**：
```python
class DependencyScannerTool:
    def __init__(self):
        self.confidence_threshold = ['High', 'Medium']  # 只保留高中置信度结果
    
    def process_ccscanner_results(self, scan_results):
        """
        ccscanner结果处理：取所有提取器结果的并集，处理版本冲突
        处理格式：{"projects": [{"dependencies": [...], "package_manager": {...}}, ...]}
        """
        all_deps = []
        
        # 1. 从所有项目文件中收集依赖（忽略提取器类型）
        for project in scan_results.get('projects', []):
            for dep in project.get('dependencies', []):
                # 置信度筛选：只保留High和Medium
                if dep.get('confidence', 'Low') in self.confidence_threshold:
                    # 只提取关键信息：依赖名、版本、置信度
                    simplified_dep = {
                        "name": dep.get('artifact_id', ''),
                        "version": dep.get('version', 'latest'),
                        "confidence": dep.get('confidence', 'Medium')
                    }
                    all_deps.append(simplified_dep)
        
        # 2. 处理重复依赖：取并集，版本冲突时保留较新版本
        deduplicated_deps = self._merge_dependencies_union(all_deps)
        
        # 如果所有结果都是Low置信度，返回空列表
        if not deduplicated_deps:
            logging.info("All ccscanner results are Low confidence, ignoring ccscanner output")
            return []
        
        return deduplicated_deps
    
    def _merge_dependencies_union(self, all_deps):
        """
        依赖并集处理：对于同名依赖，版本冲突时保留较新版本
        """
        dep_groups = {}
        
        # 按依赖名称分组
        for dep in all_deps:
            name = dep['name']
            if name not in dep_groups:
                dep_groups[name] = []
            dep_groups[name].append(dep)
        
        # 处理每个依赖组
        final_deps = []
        for name, deps in dep_groups.items():
            if len(deps) == 1:
                final_deps.append(deps[0])
            else:
                # 多个版本的同一依赖，选择较新版本
                best_dep = self._select_newer_version(deps)
                logging.info(f"Merged dependency '{name}': selected version {best_dep['version']}")
                final_deps.append(best_dep)
        
        return final_deps
    
    def _select_newer_version(self, deps):
        """
        选择较新版本的依赖
        版本比较策略：
        1. 有具体版本号的优于"latest"
        2. 具体版本号按语义版本排序
        3. 置信度高的优先
        """
        # 分离有版本号的和无版本号的
        versioned_deps = [d for d in deps if d['version'] and d['version'] != 'latest']
        latest_deps = [d for d in deps if not d['version'] or d['version'] == 'latest']
        
        # 优先选择有具体版本号的
        if versioned_deps:
            # 按置信度和版本号选择最佳
            return max(versioned_deps, key=lambda d: (
                1 if d['confidence'] == 'High' else 0,
                self._parse_version(d['version'])
            ))
        else:
            # 都是latest版本，选择置信度最高的
            return max(latest_deps, key=lambda d: 1 if d['confidence'] == 'High' else 0)
    
    def _parse_version(self, version_str):
        """
        简单的版本号解析，用于比较
        """
        try:
            # 提取数字版本号 (如 "1.2.3" -> (1, 2, 3))
            import re
            numbers = re.findall(r'\d+', version_str)
            return tuple(int(n) for n in numbers)
        except:
                         return (0,)  # 解析失败返回最低版本
```

**智能文档解析工具类（RAG技术集成）**：
```python
class DocumentAnalyzer:
    def __init__(self, project_path: str, project_name: str):
        self.project_path = project_path
        self.project_name = project_name
        self.logger = []

        # RAG参数配置（完全采用AutoCompiler的固定参数）
        self.chunk_size = 3000
        self.chunk_overlap = 200
        self.similarity_threshold = 0.80

        # 文档发现策略（改进的模糊匹配，参考AutoCompiler但更全面）
        self.compilation_keywords = [
            "readme", "build", "install", "contributing", "how-to", "compile",
            "compilation", "make", "cmake", "configure", "setup", "getting-started",
            "notes", "doc", "guide", "manual", "instruction", "building"
        ]
        self.document_extensions = ['.md', '.txt', '.rst', '.markdown']
    
    def discover_compilation_documents(self) -> list:
        """自动发现编译相关文档（模糊匹配）"""
        compilation_docs = []
        
        for root, dirs, files in os.walk(self.project_path):
            for file in files:
                file_lower = file.lower()
                
                # 高优先级文档
                for pattern in self.document_patterns["high_priority"]:
                    if file_lower.startswith(pattern):
                        compilation_docs.append((os.path.join(root, file), "high"))
                        break
                
                # 中优先级文档
                for pattern in self.document_patterns["medium_priority"]:
                    if file_lower.startswith(pattern):
                        compilation_docs.append((os.path.join(root, file), "medium"))
                        break
                
                # 低优先级文档
                for pattern in self.document_patterns["low_priority"]:
                    if file_lower.startswith(pattern):
                        compilation_docs.append((os.path.join(root, file), "low"))
                        break
        
        # 按优先级排序
        priority_order = {"high": 1, "medium": 2, "low": 3}
        compilation_docs.sort(key=lambda x: priority_order[x[1]])
        
        return [doc[0] for doc in compilation_docs]
    
    def extract_build_instructions(self, documents: list) -> dict:
        """
        使用RAG技术提取编译指令，完全采用AutoCompiler的实现策略
        """
        # 1. 使用DEBATE工具验证文档（参考AutoCompiler）
        verified_documents = []
        for doc_path in documents:
            debate_result = self.debate(doc_path)
            if doc_path in debate_result:  # DEBATE认为文档包含编译指令
                verified_documents.append(doc_path)

        if not verified_documents:
            return {"build_commands": [], "confidence": 0.0, "error": "No valid documents after DEBATE verification"}

        # 2. 设置向量存储（使用AutoCompiler的固定参数）
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=3000,  # AutoCompiler的固定参数
            chunk_overlap=200,  # AutoCompiler的固定参数
            add_start_index=True
        )

        # 3. 文档向量化和检索
        docs = self._load_documents(verified_documents)
        if not docs:
            return {"build_commands": [], "confidence": 0.0, "error": "Failed to load documents"}

        splits = text_splitter.split_documents(docs)

        # 嵌入模型配置（完全参考AutoCompiler的方式）
        from config import EMBEDDING_BASE_URL, EMBEDDING_MODEL, EMBEDDING_API_KEY
        embeddings = OpenAIEmbeddings(
            model=EMBEDDING_MODEL,  # text-embedding-3-large
            openai_api_base=EMBEDDING_BASE_URL,
            openai_api_key=EMBEDDING_API_KEY
        )
        vectorstore = Chroma.from_documents(splits, embeddings)

        # 4. 固定查询策略（完全参考AutoCompiler）
        query = "How to compile/build the project?"
        docs_and_scores = vectorstore.similarity_search_with_score(query)

        # 5. 过滤低相似度文档
        relevant_docs = [
            Document(page_content=doc.page_content, metadata=doc.metadata)
            for doc, score in docs_and_scores
            if score >= 0.80  # AutoCompiler的固定阈值
        ]

        if not relevant_docs:
            return {"build_commands": [], "confidence": 0.0, "error": "No relevant documents found"}

        # 6. 使用AutoCompiler的提示词模板提取指令
        context = "\n".join(doc.page_content for doc in relevant_docs)
        template = """You are an experienced software development engineer and specialized in extracting compilation commands. The documents from a project repository will be provided and you need to carefully identify relevant compilation/building instructions. If no compilation commands are found, respond with "No compilation commands found." If found, list the compilation commands concisely, clearly, and professionally without any additional explanations.
        Documents: {text}
        Answer: """

        # 7. LLM分析编译指令
        from config import LLM2_BASE_URL, LLM2_MODEL, LLM2_API_KEY
        llm = ChatOpenAI(
            base_url=LLM2_BASE_URL,
            model=LLM2_MODEL,
            api_key=LLM2_API_KEY,
            temperature=1  # 参考AutoCompiler的temperature设置
        )

        from langchain.prompts import PromptTemplate
        from langchain_core.output_parsers import StrOutputParser
        from langchain_core.runnables import RunnableLambda

        prompt = PromptTemplate.from_template(template=template)
        rag_chain = (
            {"text": RunnableLambda(lambda x: context)}
            | prompt
            | llm
            | StrOutputParser()
        )

        answer = rag_chain.invoke({})

        return {
            "build_commands": self._parse_build_commands(answer),
            "confidence": 0.85 if "No compilation commands found" not in answer else 0.0,
            "source": "rag_analysis",
            "raw_answer": answer
        }
```

**依赖安装方式AI判断逻辑（简化信息传递）**：
```python
def determine_install_method(self, dependency_name: str, dependency_desc: str, 
                           project_info: dict) -> dict:
    """
    AI智能判断依赖安装方式（简化输入信息）
    """
    prompt = f'''
    项目信息：{project_info['name']} - {project_info['description']}
    系统环境：Ubuntu 22.04 + gcc 13.2.0
    
    需要判断依赖的安装方式：
    依赖名称：{dependency_name}
    依赖描述：{dependency_desc}
    
    请选择最适合的安装方式：
    1. apt（系统包管理器）
    2. pip（Python包管理器） 
    3. source（源码编译）
    
    返回JSON格式：
    {{
        "install_method": "apt|pip|source",
        "install_command": "具体安装命令",
        "confidence": 0.0-1.0,
        "reasoning": "选择理由"
    }}
    '''
    
    return self._call_llm_analysis(prompt)
```

**冲突解决策略实现**：
```python
def resolve_dependency_conflicts(self, ccscanner_deps: list, doc_deps: list) -> list:
    """
    解决ccscanner和文档分析的依赖冲突
    优先级：文档分析 > ccscanner
    """
    final_deps = []
    
    # 1. 优先采用文档分析结果
    for doc_dep in doc_deps:
        final_deps.append({
            **doc_dep,
            "source": "document_analysis",
            "priority": "high"
        })
    
    # 2. 添加ccscanner中文档分析未包含的依赖
    doc_names = {dep['name'] for dep in doc_deps}
    for cc_dep in ccscanner_deps:
        if cc_dep['name'] not in doc_names:
            # 对于冲突情况，让AI基于依赖复杂度智能选择
            resolution = self._ai_resolve_conflict(cc_dep, doc_deps)
            if resolution['should_include']:
                final_deps.append({
                    **cc_dep,
                    "source": "ccscanner_supplementary",
                    "priority": "medium",
                    "install_method": resolution['recommended_method']
                })
    
    return final_deps
```

**构建系统智能识别备选方案（根目录+LLM决策）**：
```python
def fallback_build_system_detection(self, project_path: str, project_name: str) -> dict:
    """
    当文档分析失败时，使用智能构建系统识别推断编译方式
    策略：只关注根目录构建文件 + LLM智能决策
    """
    # 文档分析失败判定条件
    def should_use_fallback(build_commands, confidence):
        return (
            len(build_commands) == 0 or  # 没找到编译指令
            all(cmd.strip() == "" for cmd in build_commands) or  # 指令为空
            confidence < 0.6  # AI信心不足
        )
    
    if should_use_fallback(self.extracted_commands, self.ai_confidence):
        # 检测根目录下的构建文件
        root_build_systems = self._detect_root_build_systems(project_path)
        
        if not root_build_systems:
            return {"build_commands": [], "confidence": 0.0, "error": "No build system detected"}
        
        # 如果只有一种构建系统，直接使用
        if len(root_build_systems) == 1:
            chosen_system = list(root_build_systems.keys())[0]
        else:
            # 多种构建系统，让项目分析智能体决策
            chosen_system = self._llm_choose_build_system(root_build_systems, project_name)
        
        # 生成编译指令
        inferred_commands = self._infer_build_commands(chosen_system)
        
        return {
            "build_commands": inferred_commands,
            "source": "smart_build_system_inference",
            "build_system": chosen_system,
            "confidence": 0.75,  # 智能决策，较高置信度
            "available_systems": list(root_build_systems.keys())
        }
    
    return None

def _detect_root_build_systems(self, project_path: str) -> dict:
    """
    只检测根目录下的构建系统文件
    返回：{构建系统名称: 入口文件}
    """
    BUILD_SYSTEM_FILES = {
        'CMake': ['CMakeLists.txt'],
        'Make': ['Makefile', 'GNUmakefile', 'makefile'],
        'Autotools': ['configure', 'configure.in', 'configure.ac'],
        'Ninja': ['build.ninja'],
        'Meson': ['meson.build'],
        'Bazel': ['BUILD', 'BUILD.bazel'],
        'Xmake': ['xmake.lua'],
        'Build2': ['manifest'],
        'Python': ['setup.py'],
        'Vcpkg': ['vcpkg.json'],
        'Shell': ['build.sh'],
        'Scons': ['SConstruct', 'SConscript'],
        'Premake5': ['premake5.lua']
    }
    
    detected_systems = {}
    
    # 只检查根目录，不递归子目录
    root_files = os.listdir(project_path)
    
    for build_system, build_files in BUILD_SYSTEM_FILES.items():
        for build_file in build_files:
            if build_file in root_files:
                detected_systems[build_system] = build_file
                break  # 找到一个就够了
    
    return detected_systems

def _llm_choose_build_system(self, available_systems: dict, project_name: str) -> str:
    """
    让项目分析智能体选择最适合的构建系统
    """
    prompt = f'''
    项目 "{project_name}" 在根目录下发现了多种构建系统：
    {available_systems}
    
    请选择最适合用于编译整个项目的构建系统。考虑因素：
    1. 构建系统的主流程度和成熟度
    2. 对于C/C++项目的适用性
    3. 编译成功的可能性
    
    只需要返回构建系统名称（如：CMake, Make, Autotools等）
    '''
    
    # 调用项目分析智能体的LLM
    response = self._call_llm_analysis(prompt)
    
    # 从响应中提取构建系统名称
    for system in available_systems.keys():
        if system.lower() in response.lower():
            logging.info(f"LLM选择构建系统: {system} for project {project_name}")
            return system
    
    # 如果LLM选择失败，使用默认优先级
    priority_order = ['CMake', 'Make', 'Autotools', 'Meson', 'Ninja']
    for system in priority_order:
        if system in available_systems:
            logging.warning(f"LLM选择失败，使用默认优先级: {system}")
            return system
    
    # 最后的保底选择
    return list(available_systems.keys())[0]

def _infer_build_commands(self, build_system: str) -> list:
    """根据构建系统推断编译指令"""
    command_templates = {
        'CMake': [
            'mkdir -p build && cd build',
            'cmake ..',
            'make -j$(nproc)'
        ],
        'Make': ['make -j$(nproc)'],
        'Autotools': [
            './configure',
            'make -j$(nproc)'
        ],
        'Ninja': ['ninja'],
        'Meson': [
            'meson setup build',
            'cd build', 
            'ninja'
        ],
        'Bazel': ['bazel build //...'],
        'Xmake': ['xmake'],
        'Build2': ['b'],
        'Python': ['python setup.py build'],
        'Shell': ['bash build.sh'],
        'Scons': ['scons'],
        'Premake5': ['premake5 gmake2', 'make']
    }
    
    return command_templates.get(build_system, [f'echo "Unsupported build system: {build_system}"'])
```

**开发任务清单**：
1. **主控智能体框架**：
   - [ ] 实现MasterCompilationAgent类
   - [ ] 集成版本切换循环防护机制
   - [ ] ReAct框架和工具调用机制
   - [ ] 编译成功判断智能决策机制

2. **项目分析智能体框架**：
   - [ ] 实现ProjectAnalyzer类
   - [ ] 集成ccscanner、RAG文档分析、DEBATE工具
   - [ ] 依赖冲突解决和构建系统识别

3. **错误处理智能体框架**：
   - [ ] 实现ErrorSolver类
   - [ ] GitHub Issues和Google搜索集成
   - [ ] 多层次搜索策略

**验收标准**：
- [ ] 三个智能体类实现完成
- [ ] 智能体间通信机制正常（同进程内类实例调用）
- [ ] 使用模拟数据验证工具调用流程
- [ ] 不进行真实项目编译测试

#### **Phase 3: 高级功能模块开发**
**目标**: 开发循环依赖检测、版本切换等高级功能
**完成标准**: 所有高级功能模块开发完成

**主控智能体ReAct框架（粗粒度任务驱动）**：
```python
class MasterCompilationAgent:
    def __init__(self, project_name: str, project_path: str):
        self.project_name = project_name
        self.project_path = project_path
        self.max_iterations = 25  # 参考AutoCompiler的30次，略微减少

        # 版本切换循环防护机制
        self.version_switch_history = []  # 记录切换历史
        self.max_version_switches = 2     # 最多切换2次

        # LLM配置（完全参考AutoCompiler的方式）
        from config import LLM1_BASE_URL, LLM1_MODEL, LLM1_API_KEY
        self.llm = ChatOpenAI(
            base_url=LLM1_BASE_URL,
            model=LLM1_MODEL,
            api_key=LLM1_API_KEY,
            temperature=0.7,  # 主控智能体需要平衡决策稳定性与灵活性
            timeout=180       # 主控智能体可能需要更多思考时间
        )
    
    def create_master_agent(self):
        """创建主控智能体ReAct框架"""
        # 工具定义
        tools = [
            Tool(
                name="DockerManager",
                description=self.docker_manager.execute_command.__doc__,
                func=self.docker_manager.execute_command
            ),
            Tool(
                name="ProjectAnalyzer",
                description=self.project_analyzer.analyze.__doc__,
                func=self.project_analyzer.analyze
            ),
            Tool(
                name="ErrorSolver",
                description=self.error_solver.solve.__doc__,
                func=self.error_solver.solve
            ),
            Tool(
                name="ContainerVersionSwitcher",
                description="Switch Ubuntu version when encountering system compatibility issues. Input: target_version (18.04/20.04/22.04)",
                func=self.switch_container_version
            )
        ]
        
        # 主控智能体提示词（粗粒度任务）
        master_prompt = """你是一个经验丰富的C/C++项目编译专家。请帮我完成项目 {project_name} 的完整编译流程：

可用工具：
{tools}

完整任务流程：
1. 创建项目副本并初始化Docker环境和Git快照
2. 调用ProjectAnalyzer分析项目依赖和编译指令
3. 安装所有依赖（apt/pip/源码，遵循循环依赖检测和2层递归限制）
4. 执行编译指令并智能监控编译过程  
5. 遇到错误时先自主尝试解决（最多3次），失败后调用ErrorSolver
6. 使用编译前后文件差异识别编译产物
7. 最终判断编译状态，输出：COMPILATION-SUCCESS, COMPILATION-FAIL, 或 COMPILATION-UNCERTAIN

使用ReAct格式：
Question: {input}
Thought: 分析当前情况并规划下一步
Action: 选择工具 [{tool_names}]
Action Input: 具体的工具输入
Observation: 工具执行结果
... (重复思考-行动循环)
Final Answer: COMPILATION-SUCCESS/FAIL/UNCERTAIN

项目信息：
- 项目名称：{project_name}
- 项目路径：{project_path}
- 系统环境：Ubuntu 22.04 + gcc 13.2.0

Begin!
Thought: {agent_scratchpad}"""

        # 创建ReAct智能体
        agent = create_react_agent(
            llm=self.llm,
            tools=tools, 
            prompt=PromptTemplate.from_template(master_prompt)
        )
        
        # 自定义执行器
        agent_executor = CompilationAgentExecutor(
            agent=agent,
            tools=tools,
            verbose=True,
            return_intermediate_steps=True,
            max_iterations=self.max_iterations,
            handle_parsing_errors=True,
            early_stopping_method="generate"
        )
        
        return agent_executor

    def switch_container_version(self, target_version: str) -> str:
        """
        Switch Ubuntu version when encountering system compatibility issues.
        由主控智能体调用的版本切换策略，停止当前容器并创建新版本容器。
        包含循环防护机制，避免无限切换。

        @param target_version: Target Ubuntu version (18.04/20.04/22.04)
        @return: Switch result message
        """
        try:
            # 验证目标版本
            if target_version not in ["18.04", "20.04", "22.04"]:
                return f"Error: Unsupported Ubuntu version: {target_version}"

            # 循环防护：检查是否已经尝试过这个版本
            if target_version in self.version_switch_history:
                return f"Version {target_version} already tried, avoiding infinite loop. Tried versions: {self.version_switch_history}"

            # 循环防护：检查切换次数限制
            if len(self.version_switch_history) >= self.max_version_switches:
                return f"Maximum version switches ({self.max_version_switches}) reached. Tried versions: {self.version_switch_history}. Compilation failed due to system compatibility issues."

            current_version = getattr(self.docker_manager, 'ubuntu_version', '20.04')
            if target_version == current_version:
                return f"Already using Ubuntu {target_version}"

            # 记录切换历史
            self.version_switch_history.append(target_version)

            # 停止并删除当前容器
            self.docker_manager.close()

            # 创建新版本的Docker管理器
            from tools import InteractiveDockerShell
            self.docker_manager = InteractiveDockerShell(
                local_path=self.project_path,
                ubuntu_version=target_version,
                use_proxy=False,
                stuck_timeout=120,
                cmd_timeout=3600,
                pre_exec=True
            )

            return f"Successfully switched to Ubuntu {target_version}. Container recreated and ready for compilation. Switch history: {self.version_switch_history}"

        except Exception as e:
            return f"Failed to switch to Ubuntu {target_version}: {str(e)}"
```

**编译成功判断（双重验证机制）**：
```python
class CompilationSuccessJudge:
    """
    编译成功判断，采用关键词匹配+LLM智能分析双重验证
    参考AutoCompiler的简洁实现
    """

    def __init__(self):
        # 成功关键词（参考AutoCompiler的实践）
        self.success_keywords = [
            "successfully", "complete", "finished", "done", "built",
            "compilation successful", "build successful", "make: nothing to be done"
        ]

        # 失败关键词
        self.failure_keywords = [
            "error", "failed", "fatal", "cannot", "undefined reference",
            "no such file", "permission denied", "compilation terminated"
        ]

    def judge_compilation_success(self, command_output: str, command: str) -> dict:
        """
        双重验证判断编译是否成功
        """
        # 1. 关键词匹配判断
        keyword_result = self._keyword_analysis(command_output)

        # 2. LLM智能分析判断
        llm_result = self._llm_analysis(command_output, command)

        # 3. 双重验证决策
        final_result = self._combine_judgments(keyword_result, llm_result)

        return {
            "success": final_result["success"],
            "confidence": final_result["confidence"],
            "keyword_judgment": keyword_result,
            "llm_judgment": llm_result,
            "reasoning": final_result["reasoning"]
        }

    def _keyword_analysis(self, output: str) -> dict:
        """关键词匹配分析"""
        output_lower = output.lower()

        success_count = sum(1 for keyword in self.success_keywords if keyword in output_lower)
        failure_count = sum(1 for keyword in self.failure_keywords if keyword in output_lower)

        if failure_count > 0:
            return {"success": False, "confidence": 0.8, "method": "keyword"}
        elif success_count > 0:
            return {"success": True, "confidence": 0.7, "method": "keyword"}
        else:
            return {"success": None, "confidence": 0.0, "method": "keyword"}

    def _llm_analysis(self, output: str, command: str) -> dict:
        """LLM智能分析"""
        analysis_prompt = f'''
        分析以下编译命令的执行结果，判断编译是否成功：

        执行命令：{command}
        输出结果：
        {output[-1500:]}  # 最后1500字符

        请判断：
        1. 编译是否成功完成？
        2. 判断的置信度（0.0-1.0）
        3. 判断理由

        返回JSON格式：
        {{
            "success": true/false,
            "confidence": 0.0-1.0,
            "reasoning": "判断理由"
        }}
        '''

        from config import LLM1_BASE_URL, LLM1_MODEL, LLM1_API_KEY
        llm = ChatOpenAI(
            base_url=LLM1_BASE_URL,
            model=LLM1_MODEL,
            api_key=LLM1_API_KEY,
            temperature=0.3  # 判断任务需要更稳定的结果
        )

        # 调用LLM分析
        result = llm.invoke(analysis_prompt)
        try:
            import json
            llm_judgment = json.loads(result.content)
            llm_judgment["method"] = "llm"
            return llm_judgment
        except:
            return {"success": None, "confidence": 0.0, "method": "llm", "error": "LLM analysis failed"}

    def _combine_judgments(self, keyword_result: dict, llm_result: dict) -> dict:
        """组合两种判断结果"""
        # 如果关键词明确判断失败，优先采用
        if keyword_result["success"] is False:
            return {
                "success": False,
                "confidence": max(keyword_result["confidence"], llm_result.get("confidence", 0)),
                "reasoning": "Keyword analysis detected failure indicators"
            }

        # 如果LLM明确判断失败，且置信度高
        if llm_result.get("success") is False and llm_result.get("confidence", 0) > 0.7:
            return {
                "success": False,
                "confidence": llm_result["confidence"],
                "reasoning": f"LLM analysis: {llm_result.get('reasoning', 'Failed')}"
            }

        # 如果两者都判断成功
        if keyword_result["success"] is True and llm_result.get("success") is True:
            return {
                "success": True,
                "confidence": min(keyword_result["confidence"], llm_result.get("confidence", 0.5)),
                "reasoning": "Both keyword and LLM analysis indicate success"
            }

        # 如果只有一个判断成功
        if keyword_result["success"] is True:
            return {
                "success": True,
                "confidence": keyword_result["confidence"] * 0.8,  # 降低置信度
                "reasoning": "Keyword analysis indicates success"
            }

        if llm_result.get("success") is True:
            return {
                "success": True,
                "confidence": llm_result.get("confidence", 0.5) * 0.8,
                "reasoning": f"LLM analysis: {llm_result.get('reasoning', 'Success')}"
            }

        # 都无法确定，返回不确定
        return {
            "success": None,
            "confidence": 0.0,
            "reasoning": "Unable to determine compilation result"
        }
```

**工具调用频率控制策略**：
```python
class ToolCallStrategy:
    """
    明确的工具调用策略，避免频繁或不必要的调用
    """
    
    def __init__(self):
        self.retry_counts = {
            "self_resolution": 0,  # 主控智能体自主解决尝试次数
            "error_solver_calls": 0  # 错误处理智能体调用次数
        }
    
    def should_call_project_analyzer(self, context: dict) -> bool:
        """
        项目分析智能体调用策略：直接调用
        """
        # 项目分析是必需步骤，直接调用
        return True
    
    def should_call_error_solver(self, error_context: dict) -> bool:
        """
        错误处理智能体调用策略：先自主尝试3次
        """
        if self.retry_counts["self_resolution"] < 3:
            # 主控智能体先自主尝试解决
            self.retry_counts["self_resolution"] += 1
            return False
        else:
            # 自主尝试失败，调用错误处理智能体
            if self.retry_counts["error_solver_calls"] < 3:
                self.retry_counts["error_solver_calls"] += 1
                return True
            else:
                # 错误处理智能体也尝试了3次，放弃
                return False
    
    def get_call_guidance(self, situation: str) -> str:
        """为智能体提供工具调用指导"""
        guidance = {
            "dependency_analysis": "立即调用ProjectAnalyzer分析项目依赖和编译指令",
            "compilation_error": f"当前自主尝试次数：{self.retry_counts['self_resolution']}/3，"
                               f"错误处理智能体调用次数：{self.retry_counts['error_solver_calls']}/3",
            "build_success": "使用DockerManager的文件差异检测功能识别编译产物"
        }
        return guidance.get(situation, "根据情况选择合适的工具")
```

**循环依赖检测集成**：
```python
class MasterAgentDependencyManager:
    """
    在主控智能体中集成循环依赖检测机制
    """
    
    def __init__(self):
        self.dependency_tracker = DependencyTracker(max_depth=2)
    
    def handle_source_dependency(self, dep_name: str, dep_source: str) -> dict:
        """
        处理源码依赖的编译，集成循环依赖检测
        """
        # 检查依赖有效性
        is_valid, reason = self.dependency_tracker.check_dependency_validity(dep_name)
        
        if not is_valid:
            return {
                "action": "skip",
                "reason": reason,
                "success": False
            }
        
        # 进入依赖编译
        self.dependency_tracker.enter_dependency(dep_name)
        
        try:
            # 递归调用编译流程（在同一容器中）
            compilation_result = self._compile_dependency_in_container(dep_name, dep_source)
            
            # 退出依赖编译
            self.dependency_tracker.exit_dependency(dep_name, compilation_result.get("success", False))
            
            return compilation_result
            
        except Exception as e:
            self.dependency_tracker.exit_dependency(dep_name, False)
            return {
                "action": "error",
                "reason": f"Dependency compilation failed: {str(e)}",
                "success": False
            }
    
    def _compile_dependency_in_container(self, dep_name: str, dep_source: str) -> dict:
        """
        在原容器中编译依赖，不创建新容器
        """
        commands = [
            f"cd /work && git clone {dep_source} {dep_name}_dep",
            f"cd /work/{dep_name}_dep",
            "# 这里会递归调用编译流程，但限制在2层深度内"
        ]
        
        # 实际实现会调用相同的编译流程，但有深度限制
        return {"success": True, "install_path": f"/work/{dep_name}_dep"}
```

**编译成功判断AI集成**：
```python
class CompilationSuccessJudgment:
    """
    编译成功判断逻辑，集成到主控智能体的决策中
    """
    
    def __init__(self):
        self.status_analyzer = CompilationStatusAnalyzer()
    
    def provide_judgment_capability(self, master_agent):
        """
        为主控智能体提供编译成功判断能力
        """
        judgment_prompt = '''
        基于以下信息判断编译是否成功：
        
        1. 编译输出分析结果：{output_analysis}
        2. 编译产物检测结果：{artifacts_detected}
        3. 错误/警告统计：{error_summary}
        
        综合判断编译状态，输出以下之一：
        - COMPILATION-SUCCESS：编译明确成功
        - COMPILATION-FAIL：编译明确失败
        - COMPILATION-UNCERTAIN：状态不明确，需要人工确认
        
        请在Final Answer中明确输出上述状态之一。
        '''
        
        return judgment_prompt
```

**开发任务清单**：
1. **循环依赖检测模块**：
   - [ ] 实现DependencyTracker类
   - [ ] 最大递归深度限制（2层）
   - [ ] 路径追踪和循环检测算法

2. **版本切换机制**：
   - [ ] 容器版本切换逻辑
   - [ ] 循环防护机制
   - [ ] 版本兼容性错误识别

3. **编译成功判断模块**：
   - [ ] 关键词匹配算法
   - [ ] LLM智能分析
   - [ ] 双重验证决策逻辑

**验收标准**：
- [ ] 所有高级功能模块单元测试通过
- [ ] 使用模拟场景验证循环依赖检测
- [ ] 版本切换机制逻辑验证
- [ ] 不进行真实项目编译测试

#### **Phase 4: 系统集成开发**
**目标**: 将所有模块集成为完整系统
**完成标准**: 完整的编译系统搭建完成，准备进入测试阶段

**错误处理智能体核心架构**：
```python
class ErrorSolvingAgent:
    def __init__(self, project_name: str):
        self.project_name = project_name
        self.max_attempts = 3  # 错误处理智能体最多3次尝试
        
        # LLM配置（针对错误处理优化）
        self.llm = ChatOpenAI(
            model="claude-opus-4-20250514-thinking",
            temperature=0.8,      # 错误解决需要创造性思维
            timeout=150,
            max_tokens=3000,      # 专注问题解决，控制输出长度
            base_url="https://api.chatanywhere.tech/v1",
            api_key="sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"
        )
        
        # C/C++编译错误分类器
        self.error_classifier = CompilationErrorClassifier()
        self.github_searcher = GitHubIssuesSearcher()
        self.google_searcher = GoogleSearcher()
        self.solution_validator = SolutionValidator()
    
    def solve_compilation_error(self, error_context: dict) -> dict:
        """
        智能解决编译错误的主入口
        """
        # 1. 错误分类和关键信息提取
        error_analysis = self.error_classifier.analyze_error(error_context)
        
        # 2. 基于错误类型的多层次搜索策略
        search_results = self._execute_search_strategy(error_analysis)
        
        # 3. AI生成解决方案
        solution = self._generate_solution(error_analysis, search_results)
        
        # 4. 解决方案验证和安全检查
        validated_solution = self.solution_validator.validate(solution)
        
        return validated_solution
```

**智能错误分类和关键词提取**：
```python
class CompilationErrorClassifier:
    """
    针对C/C++编译场景的智能错误分析
    """
    
    def __init__(self):
        # C/C++编译错误模式库
        self.error_patterns = {
            "missing_dependency": [
                r"fatal error: (.+\.h): No such file",
                r"cannot find -l(\w+)",
                r"undefined reference to `(.+)'",
                r"(.+): command not found"
            ],
            "compiler_error": [
                r"error: (.+) was not declared",
                r"error: invalid use of",
                r"error: expected (.+) before"
            ],
            "linker_error": [
                r"undefined reference",
                r"cannot find -l",
                r"ld: error"
            ],
            "version_compatibility": [
                r"requires (.+) >= (.+)",
                r"unsupported (.+) version"
            ],
            "permission_error": [
                r"Permission denied",
                r"cannot create directory"
            ]
        }
    
    def analyze_error(self, error_context: dict) -> dict:
        """
        智能分析编译错误，提取关键信息
        """
        error_output = error_context.get("error_message", "")
        compilation_stage = error_context.get("compilation_stage", "unknown")
        
        # 1. 模式匹配错误类型
        error_type = self._classify_error_type(error_output)
        
        # 2. AI提取关键搜索词
        search_keywords = self._extract_search_keywords(error_output, error_type)
        
        # 3. 提取相关组件
        components = self._extract_components(error_output)
        
        return {
            "error_type": error_type,
            "search_keywords": search_keywords,
            "components": components,
            "severity": self._assess_severity(error_type),
            "context": {
                "stage": compilation_stage,
                "environment": "Ubuntu 22.04 + gcc 13.2.0"
            }
        }
    
    def _extract_search_keywords(self, error_output: str, error_type: str) -> list:
        """
        AI智能提取搜索关键词，针对C/C++编译优化
        """
        extraction_prompt = f'''
        从以下C/C++编译错误中提取最有价值的搜索关键词：
        
        错误类型：{error_type}
        错误输出：
        {error_output[-1000:]}  # 最后1000字符
        
        请提取：
        1. 核心错误信息（去除路径、行号等噪声）
        2. 相关库名或头文件名
        3. 编译器/工具名称
        4. 关键函数或符号名
        
        返回JSON格式：
        {{
            "primary_keywords": ["核心关键词1", "核心关键词2"],
            "library_names": ["库名1", "库名2"],
            "function_symbols": ["函数名1", "符号名1"],
            "search_query": "组合搜索查询"
        }}
        '''
        
        response = self.llm.invoke(extraction_prompt)
        try:
            return json.loads(response.content)
        except:
            # 回退到基础关键词提取
            return self._fallback_keyword_extraction(error_output)
```

**多层次搜索策略**：
```python
class MultiLayerSearchStrategy:
    """
    针对编译错误的多层次搜索策略
    优先级：GitHub Issues > 官方文档 > StackOverflow > 技术博客
    """
    
    def execute_search(self, error_analysis: dict) -> dict:
        """
        执行多层次搜索策略
        """
        search_results = {}
        
        # 第1层：GitHub Issues精准搜索
        github_results = self._search_github_issues(error_analysis)
        if github_results["relevance_score"] > 0.8:
            search_results["github"] = github_results
            return search_results  # 高相关度直接返回
        
        # 第2层：Google搜索官方文档和StackOverflow
        google_results = self._search_google_targeted(error_analysis)
        search_results["google"] = google_results
        
        # 第3层：如果仍无高质量结果，扩展搜索
        if google_results["max_relevance"] < 0.7:
            extended_results = self._search_extended(error_analysis)
            search_results["extended"] = extended_results
        
        return search_results
    
    def _search_github_issues(self, error_analysis: dict) -> dict:
        """
        GitHub Issues智能搜索
        """
        # 构造精准搜索查询
        project_name = self.project_name
        keywords = error_analysis["search_keywords"]
        error_type = error_analysis["error_type"]
        
        # 多重搜索策略
        search_queries = [
            # 精确匹配项目和错误
            f'repo:{project_name} "{keywords["primary_keywords"][0]}" is:closed',
            # 错误类型和库名
            f'"{keywords["library_names"][0]}" "{error_type}" language:C++',
            # 通用C++编译错误
            f'"{keywords["search_query"]}" "Ubuntu" "gcc" is:closed'
        ]
        
        all_results = []
        for query in search_queries:
            results = self.github_api.search_issues(query, per_page=10)
            all_results.extend(results)
        
        # AI评估相关性和质量
        filtered_results = self._filter_github_results(all_results, error_analysis)
        
        return {
            "results": filtered_results[:5],  # 最多5个最相关结果
            "relevance_score": max([r["relevance"] for r in filtered_results]) if filtered_results else 0.0
        }
    
    def _search_google_targeted(self, error_analysis: dict) -> dict:
        """
        Google搜索，针对技术文档优化
        """
        keywords = error_analysis["search_keywords"]
        
        # 构造针对性搜索查询
        search_queries = [
            # 官方文档优先
            f'{keywords["search_query"]} site:github.com OR site:cmake.org OR site:gnu.org',
            # StackOverflow专门搜索
            f'{keywords["search_query"]} "Ubuntu 22.04" site:stackoverflow.com',
            # 编译错误通用搜索
            f'"{keywords["primary_keywords"][0]}" "gcc 13" compile error solution'
        ]
        
        combined_results = []
        for query in search_queries:
            results = self.google_api.search(query, num=8)  # 每个查询8个结果
            combined_results.extend(results)
        
        # 按来源优先级排序
        prioritized_results = self._prioritize_by_source(combined_results)
        
        return {
            "results": prioritized_results[:10],  # 最多10个结果
            "max_relevance": max([r["relevance"] for r in prioritized_results]) if prioritized_results else 0.0
        }
    
    def _prioritize_by_source(self, results: list) -> list:
        """
        按来源可信度排序
        """
        source_priority = {
            "github.com": 0.9,
            "stackoverflow.com": 0.8,
            "cmake.org": 0.95,
            "gnu.org": 0.95,
            "gcc.gnu.org": 0.95,
            "ubuntu.com": 0.85,
            "archlinux.org": 0.8
        }
        
        for result in results:
            domain = self._extract_domain(result["url"])
            result["source_priority"] = source_priority.get(domain, 0.5)
        
        return sorted(results, key=lambda x: x["source_priority"], reverse=True)
```

**解决方案生成和安全验证**：
```python
class SolutionValidator:
    """
    解决方案验证和安全检查
    """
    
    def __init__(self):
        # 危险命令黑名单
        self.dangerous_commands = [
            r'rm\s+-rf\s+/',
            r'chmod\s+777\s+/',
            r'chown\s+.*\s+/',
            r'dd\s+if=.*of=/dev/',
            r'mkfs\.',
            r'fdisk',
            r'systemctl\s+disable',
            r'>/etc/',
            r'curl.*\|.*sh',
            r'wget.*\|.*sh'
        ]
        
        # 环境适用性检查
        self.environment_checks = {
            "ubuntu_version": "22.04",
            "gcc_version": "13.2.0",
            "architecture": "x86_64"
        }
    
    def validate(self, solution: dict) -> dict:
        """
        验证解决方案的安全性和适用性
        """
        commands = solution.get("commands", [])
        
        # 1. 安全性检查
        security_check = self._check_security(commands)
        if not security_check["safe"]:
            return {
                "valid": False,
                "reason": f"Security risk: {security_check['risk']}",
                "safe_alternative": self._generate_safe_alternative(commands)
            }
        
        # 2. 环境适用性检查
        compatibility_check = self._check_compatibility(solution)
        
        # 3. 命令语法验证
        syntax_check = self._validate_syntax(commands)
        
        return {
            "valid": security_check["safe"] and compatibility_check["compatible"] and syntax_check["valid"],
            "confidence": min(security_check["confidence"], compatibility_check["confidence"]),
            "validated_commands": self._sanitize_commands(commands),
            "execution_notes": compatibility_check.get("notes", [])
        }
    
    def _check_security(self, commands: list) -> dict:
        """
        安全性检查
        """
        for cmd in commands:
            for pattern in self.dangerous_commands:
                if re.search(pattern, cmd, re.IGNORECASE):
                    return {
                        "safe": False,
                        "risk": f"Dangerous pattern: {pattern}",
                        "confidence": 0.0
                    }
        
        # 检查权限提升
        if any("sudo" in cmd.lower() for cmd in commands):
            # sudo命令需要额外验证
            return {
                "safe": True,
                "risk": "Contains sudo commands - requires review",
                "confidence": 0.7
            }
        
        return {
            "safe": True,
            "risk": "None detected",
            "confidence": 0.9
        }
    
    def _check_compatibility(self, solution: dict) -> dict:
        """
        环境兼容性检查
        """
        description = solution.get("description", "").lower()
        commands = solution.get("commands", [])
        
        # 检查是否明确适用于我们的环境
        compatibility_indicators = [
            "ubuntu 22.04" in description,
            "gcc 13" in description,
            any("apt install" in cmd for cmd in commands),  # Ubuntu包管理器
            not any("yum install" in cmd for cmd in commands),  # 非RedHat系列
            not any("pacman -S" in cmd for cmd in commands)   # 非Arch系列
        ]
        
        compatibility_score = sum(compatibility_indicators) / len(compatibility_indicators)
        
        return {
            "compatible": compatibility_score > 0.6,
            "confidence": compatibility_score,
            "notes": self._generate_compatibility_notes(compatibility_indicators)
        }

class ErrorSolverAgent:
    """
    错误处理智能体主类
    """
    
    def solve(self, error_context: dict) -> dict:
        """
        错误处理智能体的主入口（作为Tool调用）
        """
        try:
            # 执行错误解决流程
            solution = self.solve_compilation_error(error_context)
            
            return {
                "success": solution["valid"],
                "error_analysis": solution.get("error_analysis", {}),
                "solutions": solution.get("validated_commands", []),
                "confidence": solution.get("confidence", 0.0),
                "execution_notes": solution.get("execution_notes", []),
                "search_sources": solution.get("search_sources", [])
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Error solver failed: {str(e)}",
                "fallback_suggestions": [
                    "检查项目官方文档",
                    "尝试更新系统包：apt update && apt upgrade", 
                    "检查是否有替代的编译方式"
                ]
            }
```

**开发任务清单**：
1. **主编译流程集成**：
   - [ ] 实现完整的compile_project方法
   - [ ] 集成项目下载→副本创建→Docker管理→智能体调度流程
   - [ ] 异常处理和资源清理机制

2. **智能体协作机制**：
   - [ ] 主控智能体与项目分析智能体的协作
   - [ ] 主控智能体与错误处理智能体的协作
   - [ ] 工具调用和结果传递机制

3. **配置和启动脚本**：
   - [ ] 完善config.py配置文件
   - [ ] 实现系统启动和初始化脚本
   - [ ] 日志系统和监控机制

**验收标准**：
- [ ] 完整系统可以启动和初始化
- [ ] 所有模块集成无冲突
- [ ] 使用简单项目验证端到端流程
- [ ] 准备进入正式测试阶段

#### **Phase 5: 综合测试和优化**
**目标**: 在真实项目上进行全面测试和性能优化
**完成标准**: 系统在FFmpeg和OpenSSL项目上达到70%成功率

**端到端集成测试策略**：
```python
class SystemIntegrationTester:
    """
    完整的系统集成测试，验证70%成功率目标
    """
    
    def __init__(self):
        self.test_projects = [
            {
                "name": "FFmpeg",
                "url": "https://github.com/FFmpeg/FFmpeg.git",
                "target_success_rate": 0.80,  # 复杂项目目标
                "complexity": "high",
                "expected_challenges": ["nasm依赖", "大量编解码库", "复杂配置"]
            },
            {
                "name": "OpenSSL", 
                "url": "https://github.com/openssl/openssl.git",
                "target_success_rate": 0.85,  # 标准项目目标
                "complexity": "medium-high",
                "expected_challenges": ["Perl配置脚本", "汇编代码", "平台优化"]
            }
        ]
        
        self.success_criteria = {
            "overall_success_rate": 0.70,  # 总体目标
            "max_compilation_time": 3600,  # 单项目最大编译时间
            "memory_limit": "8GB",         # 内存使用限制
            "stability_duration": 86400    # 24小时稳定性要求
        }
    
    def run_comprehensive_test(self) -> dict:
        """
        运行全面的集成测试
        """
        test_results = {}
        
        # 1. 单项目深度测试
        for project in self.test_projects:
            project_results = self._test_single_project_extensively(project)
            test_results[project["name"]] = project_results
        
        # 2. 并发编译测试
        concurrent_results = self._test_concurrent_compilation()
        test_results["concurrent"] = concurrent_results
        
        # 3. 长时间稳定性测试
        stability_results = self._test_long_term_stability()
        test_results["stability"] = stability_results
        
        # 4. 边界条件测试
        edge_case_results = self._test_edge_cases()
        test_results["edge_cases"] = edge_case_results
        
        # 5. 计算总体成功率
        overall_metrics = self._calculate_overall_metrics(test_results)
        test_results["overall"] = overall_metrics
        
        return test_results
    
    def _test_single_project_simply(self, project: dict) -> dict:
        """
        单项目简单测试，参考AutoCompiler的测试方式
        """
        start_time = time.time()

        try:
            # 执行完整编译流程（参考AutoCompiler的简单测试）
            compilation_result = self._execute_full_compilation_pipeline(project)

            end_time = time.time()
            duration = end_time - start_time

            success = compilation_result["final_status"] == "COMPILATION-SUCCESS"

            return {
                "project_name": project["name"],
                "success": success,
                "duration": duration,
                "error_type": compilation_result.get("error_type"),
                "retry_count": compilation_result.get("retry_count", 0),
                "version_switches": compilation_result.get("version_switches", 0),
                "final_ubuntu_version": compilation_result.get("ubuntu_version", "20.04"),
                "compilation_output": compilation_result.get("output", "")[-500:]  # 最后500字符
            }

        except Exception as e:
            return {
                "project_name": project["name"],
                "success": False,
                "duration": time.time() - start_time,
                "error": str(e),
                "error_type": "system_error"
            }

    def _calculate_success_rates(self, test_results: list) -> dict:
        """
        计算成功率，参考AutoCompiler的基准
        """
        total_projects = len(test_results)
        successful_projects = sum(1 for r in test_results if r["success"])

        # 按项目类型分类统计
        project_types = {}
        for result in test_results:
            project_name = result["project_name"]
            if "ffmpeg" in project_name.lower():
                project_types.setdefault("complex", []).append(result)
            elif "openssl" in project_name.lower():
                project_types.setdefault("standard", []).append(result)
            else:
                project_types.setdefault("general", []).append(result)

        type_success_rates = {}
        for ptype, results in project_types.items():
            success_count = sum(1 for r in results if r["success"])
            type_success_rates[ptype] = success_count / len(results) if results else 0.0

        return {
            "overall_success_rate": successful_projects / total_projects,
            "total_projects": total_projects,
            "successful_projects": successful_projects,
            "failed_projects": total_projects - successful_projects,
            "type_success_rates": type_success_rates,
            "target_met": successful_projects / total_projects >= 0.70,  # 参考AutoCompiler的70%基准
            "autocompiler_comparison": {
                "our_rate": successful_projects / total_projects,
                "autocompiler_baseline": 0.70,
                "performance": "above" if successful_projects / total_projects > 0.70 else "below"
            }
        }
```

**性能优化和监控系统**：
```python
class PerformanceOptimizer:
    """
    系统性能优化和资源监控
    """
    
    def __init__(self):
        self.performance_metrics = {
            "memory_optimization": True,
            "docker_resource_limits": True,
            "parallel_processing": False,  # 当前阶段专注单项目性能
            "caching_strategy": True
        }
    
    def optimize_system_performance(self):
        """
        系统性能优化策略
        """
        optimizations = {}
        
        # 1. 内存使用优化
        optimizations["memory"] = self._optimize_memory_usage()
        
        # 2. Docker资源控制
        optimizations["docker"] = self._optimize_docker_resources()
        
        # 3. 缓存策略优化
        optimizations["caching"] = self._optimize_caching_strategy()
        
        # 4. 网络和I/O优化
        optimizations["io"] = self._optimize_io_performance()
        
        return optimizations
    
    def _optimize_memory_usage(self) -> dict:
        """
        内存使用优化
        """
        return {
            "vector_store_management": "定期清理RAG向量存储",
            "llm_context_control": "限制LLM上下文长度，避免内存膨胀",
            "docker_log_rotation": "Docker日志轮转，防止日志文件过大",
            "dependency_cleanup": "依赖编译后及时清理临时文件",
            "memory_monitoring": "实时监控内存使用，超过7GB时告警"
        }
    
    def _optimize_docker_resources(self) -> dict:
        """
        Docker资源控制优化
        """
        return {
            "memory_limit": "8GB",  # 容器内存限制
            "cpu_limit": "4 cores", # CPU限制
            "storage_cleanup": "编译完成后清理编译产物和临时文件",
            "image_optimization": "使用多阶段构建优化镜像大小",
            "container_reuse": "单项目编译复用容器，减少创建开销"
        }
    
    def _optimize_caching_strategy(self) -> dict:
        """
        缓存策略优化
        """
        return {
            "dependency_analysis_cache": "缓存ccscanner和文档分析结果",
            "build_system_detection_cache": "缓存构建系统识别结果",
            "error_solution_cache": "缓存常见错误的解决方案",
            "vector_store_persistence": "RAG向量存储持久化，避免重复构建",
            "cache_invalidation": "基于项目最后修改时间的缓存失效策略"
        }

class ComprehensiveMonitoringSystem:
    """
    全面的监控和日志系统
    """
    
    def __init__(self):
        self.monitoring_levels = {
            "system_resources": True,
            "compilation_progress": True,
            "ai_performance": True,
            "error_tracking": True,
            "success_rate_tracking": True
        }
    
    def setup_monitoring(self):
        """
        建立完整的监控体系
        """
        monitoring_config = {
            # 系统级监控
            "system": {
                "memory_usage": "每30秒采样一次",
                "cpu_usage": "实时监控CPU使用率",
                "disk_space": "监控/work目录磁盘使用",
                "docker_status": "容器健康状态检查",
                "network_connectivity": "API调用成功率监控"
            },
            
            # 编译过程监控
            "compilation": {
                "stage_tracking": "依赖分析→安装→编译→产物识别各阶段耗时",
                "progress_indicators": "编译进度百分比估算",
                "error_frequency": "错误发生频率和类型统计",
                "retry_patterns": "重试次数和成功率统计",
                "ai_decision_tracking": "AI决策的准确性追踪"
            },
            
            # AI性能监控
            "ai_performance": {
                "response_time": "各模型响应时间统计",
                "token_usage": "Token使用量监控",
                "accuracy_metrics": "AI判断准确率统计",
                "confidence_correlation": "置信度与实际结果的相关性",
                "model_comparison": "不同模型在不同任务上的表现对比"
            },
            
            # 成功率监控
            "success_rate": {
                "real_time_tracking": "实时成功率计算",
                "project_level_metrics": "按项目类型的成功率分析",
                "trend_analysis": "成功率趋势分析",
                "failure_root_cause": "失败原因分类统计",
                "improvement_suggestions": "基于统计数据的改进建议"
            }
        }
        
        return monitoring_config
    
    def generate_performance_report(self) -> dict:
        """
        生成性能分析报告
        """
        return {
            "system_health": self._assess_system_health(),
            "compilation_statistics": self._compile_statistics(),
            "ai_performance_analysis": self._analyze_ai_performance(),
            "success_rate_analysis": self._analyze_success_rates(),
            "recommendations": self._generate_improvement_recommendations()
        }
```

**稳定性保障和异常恢复**：
```python
class StabilityAssurance:
    """
    系统稳定性保障和异常恢复机制
    """
    
    def __init__(self):
        self.stability_checks = {
            "memory_leak_detection": True,
            "docker_health_monitoring": True,
            "api_failure_recovery": True,
            "disk_space_management": True,
            "process_deadlock_detection": True
        }
    
    def implement_stability_measures(self):
        """
        实施稳定性保障措施
        """
        stability_measures = {
            # 内存泄漏检测
            "memory_management": {
                "periodic_gc": "定期触发Python垃圾回收",
                "memory_threshold_alerts": "内存使用超过阈值时告警",
                "llm_context_cleanup": "定期清理LLM上下文缓存",
                "vector_store_optimization": "优化向量存储内存使用"
            },
            
            # Docker健康监控
            "docker_stability": {
                "container_health_checks": "定期检查容器状态",
                "automatic_restart": "容器异常时自动重启",
                "ssh_connection_recovery": "SSH连接断开时自动重连",
                "resource_limit_enforcement": "强制执行资源限制"
            },
            
            # API故障恢复
            "api_resilience": {
                "retry_with_backoff": "API调用失败时指数退避重试",
                "model_fallback": "主模型不可用时切换备用模型",
                "timeout_handling": "API超时时的优雅降级",
                "rate_limit_management": "API速率限制的智能管理"
            },
            
            # 磁盘空间管理
            "storage_management": {
                "automatic_cleanup": "编译完成后自动清理临时文件",
                "disk_space_monitoring": "磁盘使用率超过80%时告警",
                "log_rotation": "日志文件自动轮转和压缩",
                "cache_size_control": "控制缓存文件总大小"
            }
        }
        
        return stability_measures
    
    def handle_system_exceptions(self, exception_type: str, context: dict) -> dict:
        """
        统一的系统异常处理
        """
        exception_handlers = {
            "docker_failure": self._handle_docker_failure,
            "api_timeout": self._handle_api_timeout,
            "memory_exhaustion": self._handle_memory_exhaustion,
            "disk_full": self._handle_disk_full,
            "network_failure": self._handle_network_failure
        }
        
        handler = exception_handlers.get(exception_type, self._handle_unknown_exception)
        return handler(context)

class DeploymentReadiness:
    """
    部署就绪性检查和文档生成
    """
    
    def generate_deployment_package(self):
        """
        生成完整的部署包
        """
        deployment_package = {
            "system_requirements": {
                "hardware": "16GB RAM, 4 CPU cores, 500GB storage",
                "software": "Ubuntu 22.04, Docker 20.10+, Python 3.9+",
                "network": "稳定网络连接，支持GitHub和API访问"
            },
            
            "installation_guide": {
                "environment_setup": "Docker环境和Python环境配置步骤",
                "dependency_installation": "系统依赖和Python包安装",
                "configuration_setup": "API密钥和配置文件设置",
                "initial_testing": "安装后的基础功能验证"
            },
            
            "operation_manual": {
                "basic_usage": "基本使用方法和命令行参数",
                "configuration_options": "详细配置选项说明",
                "monitoring_setup": "监控和日志配置",
                "troubleshooting": "常见问题和解决方案"
            },
            
            "maintenance_guide": {
                "performance_tuning": "性能调优建议",
                "log_management": "日志管理和清理",
                "update_procedures": "系统更新和升级流程",
                "backup_strategies": "数据备份和恢复策略"
            }
        }
        
        return deployment_package
```

**最终验证和质量保证**：
```python
class FinalQualityAssurance:
    """
    最终质量保证和验收测试
    """
    
    def run_acceptance_tests(self) -> dict:
        """
        运行完整的验收测试
        """
        acceptance_results = {
            # 功能验收测试
            "functional_tests": {
                "end_to_end_compilation": self._test_end_to_end_pipeline(),
                "error_handling_robustness": self._test_error_handling(),
                "ai_decision_accuracy": self._test_ai_accuracy(),
                "resource_management": self._test_resource_management()
            },
            
            # 性能验收测试
            "performance_tests": {
                "success_rate_achievement": self._verify_success_rate_target(),
                "compilation_time_efficiency": self._test_compilation_efficiency(),
                "memory_usage_compliance": self._test_memory_compliance(),
                "stability_verification": self._test_24h_stability()
            },
            
            # 安全性验收测试
            "security_tests": {
                "command_injection_protection": self._test_command_security(),
                "resource_limit_enforcement": self._test_resource_limits(),
                "data_privacy_compliance": self._test_data_privacy(),
                "access_control_verification": self._test_access_controls()
            }
        }
        
        # 计算总体验收分数
        acceptance_score = self._calculate_acceptance_score(acceptance_results)
        acceptance_results["overall_score"] = acceptance_score
        acceptance_results["acceptance_passed"] = acceptance_score >= 0.85
        
        return acceptance_results
    
    def _verify_success_rate_target(self) -> dict:
        """
        验证70%成功率目标达成
        """
        test_projects = ["FFmpeg", "OpenSSL"]  # 核心验证项目
        results = {}
        
        for project in test_projects:
            project_success_rate = self._measure_project_success_rate(project, iterations=20)
            results[project] = {
                "success_rate": project_success_rate,
                "target_met": project_success_rate >= 0.70,
                "target_value": 0.80 if project == "FFmpeg" else 0.85
            }
        
        overall_success_rate = sum(results[p]["success_rate"] for p in test_projects) / len(test_projects)
        
        return {
            "project_results": results,
            "overall_success_rate": overall_success_rate,
            "target_achievement": overall_success_rate >= 0.70,
            "quality_assessment": "优秀" if overall_success_rate >= 0.80 else "良好" if overall_success_rate >= 0.70 else "需改进"
        }
```

**测试任务清单**：
1. **功能测试**：
   - [ ] 使用FFmpeg项目进行复杂项目编译测试
   - [ ] 使用OpenSSL项目进行标准项目编译测试
   - [ ] 使用简单C项目进行基础功能测试
   - [ ] 版本切换机制测试（18.04/20.04/22.04）

2. **性能测试**：
   - [ ] 编译时间性能测试
   - [ ] 内存使用监控测试
   - [ ] 并发编译能力测试
   - [ ] 长时间稳定性测试

3. **错误处理测试**：
   - [ ] 各种编译错误场景测试
   - [ ] 网络异常恢复测试
   - [ ] 资源不足处理测试
   - [ ] 异常情况自动恢复测试

**验收标准**：
- [ ] FFmpeg项目编译成功率 ≥ 80%
- [ ] OpenSSL项目编译成功率 ≥ 85%
- [ ] 总体成功率 ≥ 70%（参考AutoCompiler基准）
- [ ] 系统稳定运行24小时无崩溃
- [ ] 性能指标达到预期要求

### 6.2 开发计划优势分析

#### **新开发计划的优势**：

1. **避免重复测试**：
   - **原方案问题**：每个Phase都要在FFmpeg/OpenSSL上验证，导致重复测试
   - **新方案优势**：所有模块开发完成后统一测试，避免重复工作

2. **提高开发效率**：
   - **模块化开发**：各模块可以并行开发，互不干扰
   - **单元测试驱动**：每个模块都有明确的单元测试标准
   - **集成测试分离**：系统集成和测试分离，问题定位更准确

3. **降低开发风险**：
   - **早期问题发现**：通过单元测试早期发现模块问题
   - **集成问题隔离**：系统集成问题与模块功能问题分离
   - **测试环境稳定**：最后阶段统一测试，环境更稳定

4. **资源利用优化**：
   - **开发资源**：开发人员可以专注于特定模块
   - **测试资源**：测试环境和数据集中使用，效率更高
   - **时间资源**：总体开发时间更可控

#### **开发时间估算**：
- **Phase 1**: 基础工具模块开发 - 2周
- **Phase 2**: 智能体框架开发 - 2周
- **Phase 3**: 高级功能模块开发 - 1.5周
- **Phase 4**: 系统集成开发 - 1周
- **Phase 5**: 综合测试和优化 - 1.5周
- **总计**: 8周（比原方案节省约2-3周）

### 6.3 测试项目基准

#### **主要测试项目**
基于项目输入文件，我们将使用以下具有代表性的大型开源项目：

**FFmpeg (https://github.com/FFmpeg/FFmpeg.git)**
- **项目特点**：多媒体处理框架，复杂的Autotools构建系统
- **编译复杂度**：高 - 大量外部依赖、多平台支持、复杂配置选项
- **测试价值**：验证复杂依赖解析、Autotools构建系统支持、大型项目编译能力
- **预期挑战**：需要nasm、大量编解码库依赖、配置参数复杂

**OpenSSL (https://github.com/openssl/openssl.git)**
- **项目特点**：加密库，Perl配置脚本+Make构建
- **编译复杂度**：中高 - 特殊配置系统、性能优化选项多
- **测试价值**：验证非标准构建系统识别、配置脚本处理能力
- **预期挑战**：Perl依赖、汇编代码编译、平台特定优化

#### **参考项目成功率基准**
根据CXXCrafter的验证结果，建立成功率期望：
- **总体目标成功率**：≥ 70% (参考CXXCrafter在100个项目上的表现)
- **FFmpeg成功率目标**：≥ 80% (CXXCrafter成功，已知缺少nasm依赖)
- **OpenSSL成功率目标**：≥ 85% (相对简单的依赖结构)

### 6.3 参考方法借鉴分析

通过深入分析reference_method目录下的项目，发现多个对我们方案友好的实现方式：

#### **AutoCompiler借鉴点** (已充分分析)
- ✅ **多智能体协作架构**：主控+专业化智能体的分工模式
- ✅ **SSH持久连接管理**：双重超时、自动重连、状态保持
- ✅ **LLM充分利用策略**：AI负责分析决策，代码负责工具调用

#### **ghcc借鉴点** (高度相关)
- **📊 Git差异检测编译产物**：
  ```python
  # 使用git ls-files --others找到编译产物
  output = run_command(["git", "ls-files", "--others"], cwd=directory)
  diff_files = output.decode().split("\n")
  ```
  **借鉴价值**：完美契合我们的"编译前后文件差异检测"方案

- **🏭 批量编译架构**：
  ```python
  # 支持大规模并行处理，容器复用策略
  docker_batch_compile(repo_binary_dir, repo_path, compile_timeout)
  ```
  **借鉴价值**：为我们的方案提供可扩展的并行处理能力

#### **CXXCrafter借鉴点** (策略相关)
- **🔄 迭代优化策略**：
  ```python
  # 编译失败后让LLM分析错误并生成修复方案，最多重试10次
  while not success and retry_count < 10:
      error_analysis = llm.analyze_error(error_message)
      fix_solution = llm.generate_fix(error_analysis)
      success = apply_fix_and_retry(fix_solution)
  ```
  **借鉴价值**：为我们的错误处理智能体提供迭代优化思路

- **🏗️ 构建系统智能识别（备选方案）**：
  ```python
  BUILD_FILES = {
      'Make': ['Makefile', 'GNUmakefile', 'makefile'],
      'CMake': 'CMakeLists.txt',
      'Autotools': ['configure', 'configure.in', 'configure.ac'],
      # ... 支持13种构建系统
  }
  ```
  **使用场景**：当项目文档分析无法提取有效编译指令组时，作为备选方案进行构建系统识别

- **📈 成功率优化经验**：
  ```python
  # CXXCrafter在100个真实项目上达到70%+成功率的关键：
  # 1. 准确的构建系统识别
  # 2. 依赖文档智能解析  
  # 3. 错误驱动的迭代优化
  # 4. 标准化的Dockerfile模板
  ```
  **借鉴价值**：为我们设定合理的成功率期望和优化方向

#### **ccscanner集成策略**
- **📦 依赖扫描结果处理**：
  ```python
  # ccscanner的输出格式已知，需要智能过滤和分类
  scanner_result = scanner(project_dir).extractors
  high_confidence_deps = [dep for dep in deps if dep['confidence'] == 'High']
  ```
  **集成策略**：与文档分析结果进行交叉验证，形成双重依赖验证机制

### 6.4 核心借鉴实现计划

#### **立即集成的技术**
1. **ghcc的Git差异检测** → 集成到Docker管理类的文件系统快照功能
2. **CXXCrafter的构建系统识别** → 作为智能文档解析工具类的备选方案

#### **策略层面借鉴**
1. **CXXCrafter的迭代优化模式** → 应用到错误处理智能体的重试策略
2. **ghcc的批量处理架构** → 为未来的性能优化做准备
3. **AutoCompiler的AI-代码分工** → 确保充分利用大模型能力

**编译错误处理策略最终确定**：
- **主控智能体自主处理**：最多3次重试，处理常见编译错误
- **错误处理智能体介入**：最多3次重试，处理复杂问题，使用GitHub Issues + Google Search
- **版本切换决策**：由主控智能体通过ReAct框架智能判断，不使用预设错误模式匹配
- **总计重试上限**：6次（平衡处理效率与成功率）
- **放弃标准**：6次重试后仍无法解决则标记为编译失败

**编译错误处理 vs 异常处理区分**：
- **编译错误处理**：针对编译过程中的逻辑错误（依赖缺失、语法错误等），使用智能体自主决策重试
- **异常处理**：针对系统级异常（网络超时、连接断开、资源不足等），参考AutoCompiler的处理方式

**异常处理策略**（参考AutoCompiler）：
- **简单异常捕获**：使用try-except包装主要执行流程
- **日志记录**：记录异常信息到日志文件，便于调试
- **继续执行**：单个项目异常不影响整体流程，继续处理下一个项目
- **外层重试**：通过外层循环提供重试机制（retry参数控制）
- **资源清理**：异常发生时确保Docker容器等资源正确清理

*注：虽然CXXCrafter使用10次重试获得70%成功率，但考虑到我们的双智能体分工架构和资源效率，保持6次重试策略。*

#### **成功率期望设定**
基于CXXCrafter的真实验证数据，设定我们的成功率目标：
- **总体目标成功率**：≥ 70% (作为开发和优化的指导基准)
- **FFmpeg成功率目标**：≥ 80% (复杂项目验证能力)
- **OpenSSL成功率目标**：≥ 85% (标准项目处理能力)

**成功率期望的作用**：
- 📊 **效果评估基准**：判断方案是否达到预期效果
- 🎯 **开发目标指导**：在开发过程中指导优化方向
- 📈 **对比验证依据**：与其他方案进行竞争力对比
- 🔄 **迭代优化目标**：提供明确的改进方向

### 6.5 进度追踪机制

#### **功能驱动开发模式**
- **开发触发**：不按固定时间计划，而是以功能完成为触发条件
- **阶段推进**：上一阶段的核心功能验证通过后，立即开始下一阶段
- **并行开发**：在确保依赖关系的前提下，允许部分功能并行开发

#### **每日进度更新**
- **当前阶段**: Phase 1 - 基础设施搭建
- **今日目标**: 分析完成参考方法，开始Docker管理类设计
- **完成情况**: 参考方法分析完成，发现多个高价值借鉴点
- **明日计划**: 开始Docker管理类核心架构设计，集成ghcc的编译器包装技术

#### **每周里程碑检查**
- **本周目标达成率**: 100% (参考方法分析完成)
- **关键功能验证状态**: 设计阶段，准备开始实现
- **技术难点和解决方案**: 发现ghcc和CXXCrafter提供了大量可借鉴的成熟技术
- **下周重点任务**: 完成Docker管理类基础实现，集成参考方法的优秀技术

#### **问题追踪和讨论**
当开发过程中遇到以下情况时，及时与您讨论：
- **技术方案不确定**: 多种实现方式需要选择时
- **AI能力边界**: 某些任务是否应该交给AI处理存在疑问
- **性能和复杂度权衡**: 实现复杂度与性能效果的平衡选择
- **用户需求澄清**: 方案细节需要进一步明确时

### 6.6 文档管理策略
- **文档目录**: 所有项目文档统一存放在 `/docs` 目录下
- **实时更新**: 开发进度和技术方案变更实时更新到方案文档
- **版本控制**: 文档变更与代码开发同步进行版本控制
- **结构化管理**: 按功能模块和开发阶段组织文档结构

### 6.7 技术细节完整性确认

**已明确定义的核心技术细节**：

#### **1. UUID生成和项目副本管理** ✅
- **UUID版本**：`uuid.uuid4()` 随机生成
- **路径格式**：`{绝对路径}-{UUID}`
- **权限管理**：双重777权限设置
- **清理策略**：安全检查 + 选择性清理
- **并发安全**：多进程UUID生成无冲突

#### **2. LLM配置和智能体参数** ✅
- **差异化配置**：不同智能体采用不同temperature和timeout
- **参数优化**：分析任务(0.3) < 决策任务(0.7) < 创造性任务(0.8)
- **迭代控制**：主控25次，分析15次，错误处理10次
- **Token分配**：文档分析6000 > 综合决策4000 > 问题解决3000

#### **3. AgentExecutor和通信机制** ✅
- **执行器选择**：轻量级自定义AgentExecutor
- **通信方式**：同步Tool调用，标准化返回格式
- **错误处理**：统一异常捕获和标准化错误信息
- **状态追踪**：时间戳、执行时间、中间步骤记录

#### **4. 循环依赖检测算法** ✅
- **检测方式**：简单路径追踪 + 深度限制
- **算法实现**：DependencyTracker类完整实现
- **缓存机制**：已编译项目集合，避免重复工作
- **处理策略**：检测-进入-退出三阶段管理

#### **5. 编译成功判断标准** ✅
- **主控智能体决策**：完全由主控智能体基于完整上下文判断
- **参考AutoCompiler**：返回COMPILATION-SUCCESS/FAIL/UNCERTAIN格式
- **智能分析**：综合编译输出、错误信息和文件系统状态
- **最终决策权**：避免复杂冲突处理，简化系统设计

#### **6. 提示词模板设计** ✅
- **三套模板**：主控、分析、错误处理专用模板
- **ReAct格式**：统一的思考-行动-观察循环
- **JSON约束**：强制结构化输出，确保可解析
- **上下文丰富**：项目信息、错误详情等关键上下文

#### **7. 数据传递和消息格式** ✅
- **执行接口**：统一的execute_agent_task函数
- **消息格式**：AgentMessage类标准化消息
- **返回格式**：包含成功状态、错误信息、置信度的完整格式
- **JSON规范**：依赖列表、解决方案等标准化JSON格式

**技术栈集成完整性**：
- ✅ **LangChain集成**：ReAct Agent + 自定义执行器
- ✅ **Docker管理**：SSH持久连接 + Git差异检测
- ✅ **ccscanner集成**：结构化依赖扫描 + 智能过滤
- ✅ **文档解析**：RAG技术 + 外部链接处理
- ✅ **错误处理**：GitHub Issues + Google Search
- ✅ **日志系统**：分级日志 + 状态追踪

**开发实施准备**：
所有关键技术细节均已明确定义，具备以下开发条件：
1. **API接口规范**：每个模块的输入输出格式完全确定
2. **参数配置明确**：所有可调参数都有具体数值
3. **算法实现完整**：核心算法都有详细的伪代码实现
4. **异常处理完善**：错误场景和处理策略全面覆盖
5. **测试标准清晰**：成功率期望和验证标准明确

**进入Phase 1开发条件**：**已满足** ✅

当前文档版本已包含完整的技术实现细节，可直接作为开发蓝图使用，确保开发出来的系统完全符合设计预期。



## 7. AutoCompiler借鉴优化实现

### 7.1 智能体通信机制实现
完全参考AutoCompiler的智能体通信方式，采用"同进程内类实例调用"模式：

```python
# MasterAgent.py - 主控智能体实现
from ProjectAnalyzer import ProjectAnalyzer
from ErrorSolver import ErrorSolver
from tools import InteractiveDockerShell, GitHubManager

class MasterAgent:
    def __init__(self, project_name: str, project_path: str):
        self.project_name = project_name
        self.project_path = project_path

        # 初始化工具实例
        self.docker_shell = InteractiveDockerShell(local_path=project_path)
        self.github_manager = GitHubManager(proxy=PROXY)

        # 初始化智能体实例（完全参考AutoCompiler）
        self.project_analyzer = ProjectAnalyzer(project_name=project_name, project_path=project_path)
        self.error_solver = ErrorSolver(project_name=project_name)

        # 构建工具集（参考AutoCompiler的tools注册方式）
        self.tools = [
            Tool(
                name="Shell",
                description=self.docker_shell.execute_command.__doc__,
                func=self.docker_shell.execute_command
            ),
            Tool(
                name="ProjectAnalyzer",
                description=self.project_analyzer.analyze.__doc__,
                func=self.project_analyzer.analyze  # 直接调用类方法
            ),
            Tool(
                name="ErrorSolver",
                description=self.error_solver.solve.__doc__,
                func=self.error_solver.solve  # 直接调用类方法
            ),
            Tool(
                name="GitHubCloner",
                description=self.github_manager.download_project.__doc__,
                func=self.github_manager.download_project
            ),
            Tool(
                name="ProjectCopyManager",
                description=self.github_manager.copy_project.__doc__,
                func=self.github_manager.copy_project
            )
        ]

    def compile_project(self, project_url: str) -> dict:
        """
        Main compilation workflow, 完全参考AutoCompiler的主流程实现
        """
        try:
            # 1. 下载项目（如果本地不存在）
            if not os.path.exists(self.project_path) or not os.listdir(self.project_path):
                from tools import download_project
                download_project(project_url, self.project_path)

            # 2. 创建项目副本（完全参考AutoCompiler的时机）
            from tools import copy_project
            local_path = copy_project(self.project_path)

            # 3. 使用副本路径创建Docker管理器
            from tools import InteractiveDockerShell
            self.docker_manager = InteractiveDockerShell(
                local_path=local_path,
                ubuntu_version="20.04",  # 默认版本
                use_proxy=False,
                stuck_timeout=120,
                cmd_timeout=3600,
                pre_exec=True
            )

            # 4. 创建ReAct智能体（参考AutoCompiler，使用config中的LLM配置）
            agent = create_react_agent(llm=self.llm, tools=self.tools, prompt=MASTER_AGENT_TEMPLATE)
            agent_executor = CustomCompilationAgentExecutor(
                agent=agent,
                tools=self.tools,
                verbose=True,
                return_intermediate_steps=True,
                max_iterations=25,
                handle_parsing_errors=True
            )

            # 5. 执行编译任务（参考AutoCompiler的执行方式）
            question = f"Compile the C/C++ project: {self.project_name}"
            result = agent_executor.invoke({"input": question})

            return {
                "status": "success",
                "output": result["output"],
                "intermediate_steps": result.get("intermediate_steps", []),
                "local_path": local_path  # 返回副本路径
            }

        except Exception as e:
            return {
                "status": "failed",
                "error": str(e)
            }
        finally:
            # 清理Docker容器
            if hasattr(self, 'docker_manager'):
                self.docker_manager.close()
```

### 7.2 自定义AgentExecutor实现
参考AutoCompiler的CustomAgentExecutor，实现智能的工具间数据流管理：

```python
# CustomCompilationAgentExecutor.py
from langchain.agents import AgentExecutor
from langchain_core.agents import AgentAction, AgentFinish, AgentStep
from typing import Dict, List, Optional, Union, Iterator, Tuple

class CustomCompilationAgentExecutor(AgentExecutor):
    """参考AutoCompiler的CustomAgentExecutor实现智能数据流管理"""

    def _perform_agent_action(self, name_to_tool_map, color_mapping, agent_action, run_manager, intermediate_steps=[]):
        """重写工具执行逻辑，实现智能参数传递"""

        # 参考AutoCompiler的智能参数传递机制
        if agent_action.tool == 'DependencyResolver':
            try:
                # 自动传递ccscanner和文档分析的结果
                ccscanner_result = self._find_step_result(intermediate_steps, 'DependencyScanner')
                doc_result = self._find_step_result(intermediate_steps, 'BuildInstructionExtractor')
                agent_action.tool_input = f"ccscanner_deps: {ccscanner_result}\ndoc_deps: {doc_result}"
            except Exception as e:
                pass

        if agent_action.tool == 'ErrorSolver':
            try:
                # 自动传递编译错误上下文
                agent_action.tool_input = intermediate_steps[-1][1]
            except Exception as e:
                pass

        if agent_action.tool == 'BuildInstructionExtractor':
            try:
                # 自动传递文档发现结果
                agent_action.tool_input = intermediate_steps[0][1]
            except Exception as e:
                pass

        # 调用父类方法执行工具
        return super()._perform_agent_action(name_to_tool_map, color_mapping, agent_action, run_manager)

    def _find_step_result(self, intermediate_steps, tool_name):
        """查找特定工具的执行结果"""
        for action, observation in intermediate_steps:
            if action.tool == tool_name:
                return observation
        return ""
```

### 7.2 简化JSON返回格式
参考AutoCompiler的简洁返回方式，简化工具函数的返回格式：

```python
# 原来的复杂返回格式
def scan_dependencies_old(self, project_path: str) -> str:
    return json.dumps({
        "dependencies": processed_deps,
        "scan_status": "success",
        "total_count": len(processed_deps),
        "confidence_scores": {...},
        "metadata": {...}
    })

# 简化后的返回格式（参考AutoCompiler）
def scan_dependencies_new(self, project_path: str) -> str:
    if processed_deps:
        deps_list = [f"{dep['name']}({dep['install_method']})" for dep in processed_deps]
        return f"Found {len(processed_deps)} dependencies: " + ", ".join(deps_list)
    else:
        return "No dependencies found"

# GitHub搜索简化返回
def search_issues_simplified(self, project_name: str, error_keywords: str) -> str:
    # 直接返回解决方案描述，而不是复杂的JSON结构
    if relevant_solutions:
        return "\n\n".join(relevant_solutions)
    else:
        return f"No relevant solutions found for {error_keywords} in {project_name}"
```

### 7.3 固定查询策略
参考AutoCompiler的RAG实现，使用固定的查询策略：

```python
# 固定查询常量（参考AutoCompiler）
class QueryConstants:
    COMPILATION_QUERY = "How to compile/build the project?"
    URL_QUERY = "From which URL can I find the compilation instructions?"
    DEPENDENCY_QUERY = "What dependencies are required for compilation?"
    ERROR_SOLUTION_QUERY = "How to solve this compilation error?"

# 在DocumentAnalyzer中使用固定查询
def extract_build_instructions(self, documents_list: str) -> str:
    # 使用固定查询而不是动态构建
    query = QueryConstants.COMPILATION_QUERY

    # 参考AutoCompiler的get_relevant_docs实现
    docs_and_scores = vectorstore.similarity_search_with_score(query)
    relevant_docs = [
        doc for doc, score in docs_and_scores
        if score >= self.similarity_threshold
    ]

    # 使用AutoCompiler的提示词模板
    template = """You are an experienced software development engineer and specialized in extracting compilation commands. The documents from a project repository will be provided and you need to carefully identify relevant compilation/building instructions. If no compilation commands found, respond with "No compilation commands found." If found, list the compilation commands concisely, clearly, and professionally without any additional explanations.
    Documents: {text}
    Answer: """
```

### 7.4 项目副本的简洁实现
完全参考AutoCompiler的copy_project实现：

```python
def create_project_copy(self, original_path: str) -> str:
    """完全参考AutoCompiler的简洁实现"""
    UUID = uuid.uuid4()
    original_path = os.path.abspath(original_path)
    new_path = f"{original_path}-{UUID}"

    # 一行命令解决（完全参考AutoCompiler）
    cmd = f"chmod -R 777 {original_path} && cp -r {original_path} {new_path} && chmod -R 777 {new_path}"
    ret = subprocess.run(cmd, shell=True, capture_output=True)

    if ret.returncode != 0:
        raise Exception(f"Failed to copy project from {original_path} to {new_path} {ret.stderr}")

    return new_path  # 直接返回路径，不需要复杂的JSON格式
```
