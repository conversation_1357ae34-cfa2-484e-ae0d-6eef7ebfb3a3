#!/usr/bin/env python3
# test_line_endings.py - 测试行尾符修复功能
import os
import tempfile
from tools import GitHubManager

def test_line_ending_fix():
    """测试行尾符修复功能"""
    print("Testing line ending fix functionality...")
    
    # 创建测试目录
    test_dir = "./test_line_endings"
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建一个带有Windows行尾符的configure脚本
    configure_content = "#!/bin/sh\r\necho 'Hello World'\r\nexit 0\r\n"
    
    configure_path = os.path.join(test_dir, "configure")
    with open(configure_path, 'wb') as f:
        f.write(configure_content.encode('utf-8'))
    
    print(f"Created test configure script with CRLF line endings")
    
    # 检查原始文件
    with open(configure_path, 'rb') as f:
        original_content = f.read()
    
    print(f"Original content has CRLF: {b'\\r\\n' in original_content}")
    
    # 使用GitHubManager的修复功能
    github_manager = GitHubManager()
    github_manager._fix_line_endings(test_dir)
    
    # 检查修复后的文件
    with open(configure_path, 'rb') as f:
        fixed_content = f.read()
    
    print(f"Fixed content has CRLF: {b'\\r\\n' in fixed_content}")
    print(f"Fixed content has LF only: {b'\\n' in fixed_content and b'\\r\\n' not in fixed_content}")
    
    # 清理
    import shutil
    shutil.rmtree(test_dir)
    
    if b'\r\n' not in fixed_content and b'\n' in fixed_content:
        print("✅ Line ending fix test PASSED!")
        return True
    else:
        print("❌ Line ending fix test FAILED!")
        return False

if __name__ == "__main__":
    test_line_ending_fix()
