# Optimized Dockerfile for compilation - 使用现有镜像
FROM homebrew/ubuntu22.04:latest

ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# 安装完整的编译环境和SSH
RUN apt-get update && apt-get install -y \
    build-essential gcc g++ make cmake autoconf automake libtool \
    pkg-config ninja-build meson \
    openssh-server git vim tree \
    python3 python3-pip \
    wget curl unzip tar \
    yasm nasm \
    zlib1g-dev libssl-dev libncurses5-dev \
    && rm -rf /var/lib/apt/lists/*

# SSH配置
RUN mkdir -p /var/run/sshd && \
    echo 'root:root' | chpasswd && \
    sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config && \
    sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config && \
    ssh-keygen -A

# Git配置
RUN git config --global user.email "<EMAIL>" && \
    git config --global user.name "AutoCompile" && \
    git config --global init.defaultBranch main && \
    git config --global safe.directory '*'

# 工作目录
WORKDIR /work
RUN echo "cd /work" >> ~/.bashrc

# 编译环境
ENV MAKEFLAGS="-j$(nproc)"
ENV CC=gcc
ENV CXX=g++

EXPOSE 22
CMD ["/bin/bash"]
