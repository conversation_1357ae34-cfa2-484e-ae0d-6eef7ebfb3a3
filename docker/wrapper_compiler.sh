#!/bin/bash

# Compiler Wrapper for IDA Pro Analysis
# 专门为IDA Pro反汇编分析优化的编译器包装器
# 确保编译产物保留符号信息，便于控制流图分析

# 检查是否有足够的参数
if [ "$#" -lt 1 ]; then
  echo "Usage: $0 [source files and options]"
  echo "This is a wrapper for the compiler optimized for IDA Pro analysis."
  exit 1
fi

real_gcc="gcc-real"
real_gxx="g++-real"
real_clang="clang-real"
real_clangxx="clang++-real"
real_cc="gcc-real"
real_cxx="g++-real"

# 如果环境变量 ENABLE_COMPILER_WRAPPER=True 或者设置了 WRAPPER_OPTI，则执行参数替换
if [ -z "$ENABLE_COMPILER_WRAPPER" ] && [ -z "$WRAPPER_OPTI" ]; then
  # 确定要使用的编译器
  compiler=""
  case $(basename "$0") in
    gcc) compiler=$real_gcc ;;
    clang) compiler=$real_clang ;;
    g++) compiler=$real_gxx ;;
    clang++) compiler=$real_clangxx ;;
    cc) compiler=$real_cc ;;
    c++) compiler=$real_cxx ;;
    *) echo "Error: Unknown compiler '$0'."; exit 1 ;;
  esac
  # 直接执行原始命令
  exec "$compiler" "$@"
fi

# 函数：检查并跳过会影响IDA Pro分析的参数
skip_base_arg() {
  local arg="$1"
  case "$arg" in
    -s|-DNDEBUG)  # 保留调试信息，不跳过-g相关参数
      return 0 # 跳过这些参数
      ;;
    *)
      return 1 # 不跳过
      ;;
  esac
}

skip_opti_arg() {
  local arg="$1"
  case "$arg" in
    -O|-O0|-O1|-O2|-O3|-Os|-Ofast|-Og|-Oz)
      return 0 # 跳过这些参数
      ;;
    *)
      return 1 # 不跳过
      ;;
  esac
}

# 确定要使用的编译器
compiler=""
case $(basename "$0") in
  gcc) compiler=${WRAPPER_GCC:-$real_gcc} ;;
  clang) compiler=${WRAPPER_CLANG:-$real_clang} ;;
  g++) compiler=${WRAPPER_GXX:-$real_gxx} ;;
  clang++) compiler=${WRAPPER_CLANGXX:-$real_clangxx} ;;
  cc) compiler=${WRAPPER_CC:-$real_cc} ;;
  c++) compiler=${WRAPPER_CXX:-$real_cxx} ;;
  *) echo "Error: Unknown compiler '$0'."; exit 1 ;;
esac

# 判断 WRAPPER_OPTI 中是否包含优化选项
IS_WRAP_OPTI=false
if [[ "$WRAPPER_OPTI" =~ -O[0-3sfastgz]* ]]; then
  IS_WRAP_OPTI=true
fi

# 初始化参数数组
params=()

# 遍历所有传入的参数
for arg in "$@"; do
  if skip_base_arg "$arg"; then
    continue # 跳过必须跳过的参数
  fi
  # 如果 IS_WRAP_OPTI 为 true，则使用 skip_opti_arg 跳过原始的优化选项
  if $IS_WRAP_OPTI && skip_opti_arg "$arg"; then
    continue # 跳过原始的优化选项
  fi

  params+=("$arg") # 添加参数到数组
done

# 添加IDA Pro分析优化的编译选项
if [ -n "$WRAPPER_OPTI" ]; then
  # 将环境变量中的逗号替换为空格，以统一分隔符
  WRAPPER_OPTI_MODIFIED="${WRAPPER_OPTI//,/ }"
  # 读取并分割优化选项
  read -ra OPTI_ARR <<< "$WRAPPER_OPTI_MODIFIED"
  for opt in "${OPTI_ARR[@]}"; do
    opt_trimmed=$(echo $opt | xargs)  # Trim whitespace
    params+=("$opt_trimmed")
  done
fi

# 执行编译器
"$compiler" "${params[@]}"
