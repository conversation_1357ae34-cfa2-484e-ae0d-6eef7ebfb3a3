[["compile_project", "FFmpeg_FFmpeg", "Master agent compilation failed: MasterAgent._build_tools.<locals>.fix_line_endings() takes 0 positional arguments but 1 was given"], ["analyze_comprehensive", "FFmpeg_FFmpeg", "Project analysis failed: ProjectAnalyzer.analyze_comprehensive.<locals>.<lambda>() missing 1 required positional argument: 'doc_deps'"], ["cd /work && pwd", "root@c0mpi1er-c0nta1ner:/work# \n/tmp/exec_script.sh: line 2: cd: $'/work\\r': No such file or directory\n/tmp/exec_script.sh: line 3: $'pwd\\r': command not found\n\nroot@c0mpi1er-c0nta1ner:/work# ", 12.397059440612793], ["ls -la G:\\project\\autocompile\\autocompile-master\\ffmpeg_test_output\\projects\\FFmpeg_FFmpeg-ec1d4a7a-c6de-42a5-bb6c-310c3180d4e7", "root@c0mpi1er-c0nta1ner:/work# \n-la: -c: line 1: unexpected EOF while looking for matching `''\n-la: -c: line 2: syntax error: unexpected end of file\n\nroot@c0mpi1er-c0nta1ner:/work# ", 6.207058429718018], ["ls -la /work", "root@c0mpi1er-c0nta1ner:/work# \n-la: -c: line 1: unexpected EOF while looking for matching `''\n-la: -c: line 2: syntax error: unexpected end of file\n\nroot@c0mpi1er-c0nta1ner:/work# ", 6.210163831710815], ["ls", "root@c0mpi1er-c0nta1ner:/work# CONTRIBUTING.md\nCOPYING.GPLv2\nCOPYING.GPLv3\nCOPYING.LGPLv2.1\nCOPYING.LGPLv3\nCREDITS\nChangelog\nFUNDING.json\nINSTALL.md\nLICENSE.md\nMAINTAINERS\nMakefile\nREADME.md\nRELEASE\ncompat\nconfigure\ndoc\nffbuild\nfftools\nlibavcodec\nlibavdevice\nlibavfilter\nlibavformat\nlibavutil\nlibswresample\nlibswscale\npresets\ntests\ntools\n\nroot@c0mpi1er-c0nta1ner:/work# ", 6.212763547897339], ["./configure && make", "root@c0mpi1er-c0nta1ner:/work# \n/tmp/exec_script.sh: line 2: cd: $'/work\\r': No such file or directory\n/tmp/exec_script.sh: line 3: ./configure: No such file or directory\n\nroot@c0mpi1er-c0nta1ner:/work# ", 12.***************]]