#!/usr/bin/env python3
# quick_test.py - 快速系统验证脚本
import os
import sys
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_imports():
    """测试所有模块导入"""
    print("Testing module imports...")
    
    try:
        from tools import LineEndingFixer, InteractiveDockerShell, GitHubManager, DependencyScanner
        print("✅ tools module imported successfully")
    except Exception as e:
        print(f"❌ tools module import failed: {str(e)}")
        return False
    
    try:
        from MasterAgent import MasterAgent
        print("✅ MasterAgent imported successfully")
    except Exception as e:
        print(f"❌ MasterAgent import failed: {str(e)}")
        return False
    
    try:
        from ProjectAnalyzer import ProjectAnalyzer
        print("✅ ProjectAnalyzer imported successfully")
    except Exception as e:
        print(f"❌ ProjectAnalyzer import failed: {str(e)}")
        return False
    
    try:
        from ErrorSolver import ErrorSolver
        print("✅ ErrorSolver imported successfully")
    except Exception as e:
        print(f"❌ ErrorSolver import failed: {str(e)}")
        return False
    
    try:
        from main import AutoCompileSystem
        print("✅ AutoCompileSystem imported successfully")
    except Exception as e:
        print(f"❌ AutoCompileSystem import failed: {str(e)}")
        return False
    
    return True

def test_line_ending_fixer():
    """测试行尾符修复功能"""
    print("\nTesting LineEndingFixer...")
    
    try:
        from tools import LineEndingFixer
        
        # 创建测试文件
        test_file = "./test_crlf.sh"
        with open(test_file, 'wb') as f:
            f.write(b"#!/bin/bash\r\necho 'test'\r\n")
        
        fixer = LineEndingFixer()
        
        # 检测行尾符
        detected = fixer.detect_line_endings(test_file)
        if detected != 'CRLF':
            print(f"❌ Line ending detection failed: expected CRLF, got {detected}")
            return False
        
        # 修复行尾符
        result = fixer.fix_line_endings(test_file, 'LF')
        if result['status'] != 'success':
            print(f"❌ Line ending fix failed: {result}")
            return False
        
        # 验证修复结果
        final_detected = fixer.detect_line_endings(test_file)
        if final_detected != 'LF':
            print(f"❌ Line ending fix verification failed: expected LF, got {final_detected}")
            return False
        
        # 清理
        os.unlink(test_file)
        print("✅ LineEndingFixer test passed")
        return True
        
    except Exception as e:
        print(f"❌ LineEndingFixer test failed: {str(e)}")
        return False

def test_error_analyzer():
    """测试错误分析功能"""
    print("\nTesting ErrorSolver error analysis...")
    
    try:
        from ErrorSolver import ErrorSolver
        
        solver = ErrorSolver("test_project")
        
        # 测试不同类型的错误
        test_cases = [
            ("fatal error: stdio.h: No such file or directory", "dependency_missing"),
            ("undefined reference to `main'", "linker_error"),
            ("/bin/sh^M: bad interpreter: No such file or directory", "line_ending_error"),
            ("make: command not found", "environment_error")
        ]
        
        for error_msg, expected_category in test_cases:
            result = solver.analyze_error_pattern(error_msg)
            analysis = json.loads(result)
            
            if analysis.get("primary_category") != expected_category:
                print(f"❌ Error analysis failed for '{error_msg}': expected {expected_category}, got {analysis.get('primary_category')}")
                return False
        
        print("✅ ErrorSolver analysis test passed")
        return True
        
    except Exception as e:
        print(f"❌ ErrorSolver test failed: {str(e)}")
        return False

def test_docker_availability():
    """测试Docker可用性"""
    print("\nTesting Docker availability...")
    
    try:
        import docker
        client = docker.from_env()
        client.ping()
        
        # 检查镜像
        images = client.images.list()
        autocompiler_images = [img for img in images if any('autocompiler' in tag or 'homebrew' in tag for tag in img.tags)]
        
        if autocompiler_images:
            print(f"✅ Docker available with {len(autocompiler_images)} AutoCompiler images")
            return True
        else:
            print("⚠️  Docker available but no AutoCompiler images found")
            print("   Please run: cd docker && bash build.sh")
            return False
            
    except Exception as e:
        print(f"❌ Docker test failed: {str(e)}")
        return False

def test_config():
    """测试配置文件"""
    print("\nTesting configuration...")
    
    try:
        import config
        
        # 检查必要的配置项
        required_configs = [
            'LLM1_API_KEY', 'LLM2_API_KEY', 'LLM3_API_KEY',
            'DOCKER_IMAGES', 'DEFAULT_UBUNTU_VERSION',
            'LLM_CONFIGS'
        ]
        
        for config_name in required_configs:
            if not hasattr(config, config_name):
                print(f"❌ Missing configuration: {config_name}")
                return False
        
        # 检查API密钥是否设置
        if not config.LLM1_API_KEY or config.LLM1_API_KEY == "your-api-key-here":
            print("⚠️  LLM API keys not configured properly")
            print("   Please update config.py with valid API keys")
            return False
        
        print("✅ Configuration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("AutoCompile System Quick Verification")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration", test_config),
        ("LineEndingFixer", test_line_ending_fixer),
        ("Error Analyzer", test_error_analyzer),
        ("Docker Availability", test_docker_availability)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        if result:
            print(f"✅ {test_name}: PASSED")
            passed += 1
        else:
            print(f"❌ {test_name}: FAILED")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for use.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
