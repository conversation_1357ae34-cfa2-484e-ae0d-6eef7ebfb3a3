# MasterAgent.py - 主控智能体（参考AutoCompiler实现）
import json
import logging
import os
from langchain_openai import ChatOpenAI
from langchain.agents import create_react_agent, AgentExecutor, Tool
from langchain_core.prompts import PromptTemplate
from tools import InteractiveDockerShell, GitHubManager, DependencyTracker
from ProjectAnalyzer import ProjectAnalyzer
from ErrorSolver import ErrorSolver
from config import (
    LLM1_BASE_URL, LLM1_MODEL, LLM1_API_KEY,
    LLM_CONFIGS, MASTER_AGENT_PROMPT, DEFAULT_UBUNTU_VERSION
)

class MasterAgent:
    """主控智能体 - 整体编译流程的调度和决策"""
    
    def __init__(self, project_name: str, project_url: str, local_path: str):
        self.project_name = project_name
        self.project_url = project_url
        self.local_path = local_path
        self.logger = []
        
        # LLM配置
        config = LLM_CONFIGS["master_agent"]
        self.llm = ChatOpenAI(
            base_url=LLM1_BASE_URL,
            model=LLM1_MODEL,
            api_key=LLM1_API_KEY,
            temperature=config["temperature"],
            timeout=config["timeout"],
            max_tokens=config["max_tokens"]
        )
        
        # 初始化工具实例
        self.docker_shell = None
        self.github_manager = GitHubManager()
        self.dependency_tracker = DependencyTracker()
        self.project_analyzer = None
        self.error_solver = None

    def compile_project(self) -> str:
        """
        Execute complete project compilation workflow.
        
        @return: Compilation result (COMPILATION-SUCCESS, COMPILATION-FAIL, or COMPILATION-UNCERTAIN)
        """
        try:
            # 初始化智能体实例
            self.project_analyzer = ProjectAnalyzer(self.local_path, self.project_name)
            self.error_solver = ErrorSolver(self.project_name, self.project_url)
            
            # 构建工具集
            tools = self._build_tools()
            
            # 创建提示词模板
            prompt = PromptTemplate(
                template=MASTER_AGENT_PROMPT,
                input_variables=["input", "agent_scratchpad", "tools", "tool_names", "project_name"]
            )
            
            # 创建ReAct智能体
            agent = create_react_agent(llm=self.llm, tools=tools, prompt=prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                max_iterations=LLM_CONFIGS["master_agent"]["max_iterations"],
                verbose=True,
                handle_parsing_errors=True
            )
            
            # 执行编译任务
            compilation_prompt = f"Compile C/C++ project {self.project_name} located at {self.local_path}"
            result = agent_executor.invoke({
                "input": compilation_prompt,
                "project_name": self.project_name
            })
            
            # 记录日志
            self.logger.append([
                "compile_project",
                self.project_name,
                result["output"]
            ])
            
            return result["output"]
            
        except Exception as e:
            error_msg = f"Master agent compilation failed: {str(e)}"
            self.logger.append([
                "compile_project",
                self.project_name,
                error_msg
            ])
            return "COMPILATION-FAIL"
        finally:
            # 清理资源
            if self.docker_shell:
                self.docker_shell.close()

    def _build_tools(self) -> list:
        """构建工具集"""
        tools = []
        
        # Docker Shell工具
        def create_docker_shell(local_path: str = None) -> str:
            """
            Create Docker container and establish SSH connection for compilation.

            @param local_path: Path to project directory (optional, uses self.local_path if not provided)
            @return: Status message
            """
            try:
                if self.docker_shell:
                    self.docker_shell.close()

                target_path = local_path or self.local_path
                self.docker_shell = InteractiveDockerShell(
                    local_path=target_path,
                    ubuntu_version=DEFAULT_UBUNTU_VERSION
                )
                return f"Docker container created successfully with Ubuntu {DEFAULT_UBUNTU_VERSION} for path: {target_path}"
            except Exception as e:
                return f"Failed to create Docker container: {str(e)}"
        
        def execute_shell_command(command: str) -> str:
            """
            Execute command in Docker container via SSH.
            
            @param command: Shell command to execute
            @return: Command output
            """
            if not self.docker_shell:
                return "Error: No Docker container available. Create container first."
            
            try:
                return self.docker_shell.execute_command(command)
            except Exception as e:
                return f"Command execution failed: {str(e)}"
        
        def switch_ubuntu_version(target_version: str) -> str:
            """
            Switch to different Ubuntu version for compatibility issues.
            
            @param target_version: Target Ubuntu version (18.04/20.04/22.04)
            @return: Switch result message
            """
            if not self.docker_shell:
                return "Error: No Docker container available."
            
            try:
                return self.docker_shell.switch_ubuntu_version(target_version)
            except Exception as e:
                return f"Version switch failed: {str(e)}"
        
        def create_git_snapshot() -> str:
            """
            Create git snapshot for artifact detection.
            
            @return: Status message
            """
            if not self.docker_shell:
                return "Error: No Docker container available."
            
            try:
                return self.docker_shell.create_git_snapshot("/work")
            except Exception as e:
                return f"Git snapshot creation failed: {str(e)}"
        
        def detect_artifacts() -> str:
            """
            Detect compilation artifacts after build.

            @return: JSON string with detected artifacts
            """
            if not self.docker_shell:
                return json.dumps({"artifacts": [], "status": "no_container"})

            try:
                return self.docker_shell.detect_compilation_artifacts("/work")
            except Exception as e:
                return json.dumps({"artifacts": [], "status": "failed", "error": str(e)})

        def fix_line_endings() -> str:
            """
            Fix line ending issues in shell scripts (Windows CRLF to Unix LF).

            @return: Status message
            """
            if not self.docker_shell:
                return "Error: No Docker container available."

            try:
                # 在容器中安装dos2unix工具并修复configure脚本
                commands = [
                    "apt-get update",
                    "apt-get install -y dos2unix",
                    "find /work -name 'configure' -o -name '*.sh' -o -name 'autogen*' -o -name 'bootstrap' | xargs dos2unix",
                    "chmod +x /work/configure"
                ]

                results = []
                for cmd in commands:
                    result = self.docker_shell.execute_command(cmd)
                    results.append(f"Command: {cmd}\nResult: {result[:200]}...")

                return "Line endings fixed successfully:\n" + "\n".join(results)
            except Exception as e:
                return f"Failed to fix line endings: {str(e)}"
        
        # 添加工具到列表
        tools.extend([
            Tool(
                name="CreateDockerShell",
                description=create_docker_shell.__doc__,
                func=create_docker_shell
            ),
            Tool(
                name="Shell",
                description=execute_shell_command.__doc__,
                func=execute_shell_command
            ),
            Tool(
                name="VersionSwitcher",
                description=switch_ubuntu_version.__doc__,
                func=switch_ubuntu_version
            ),
            Tool(
                name="GitSnapshot",
                description=create_git_snapshot.__doc__,
                func=create_git_snapshot
            ),
            Tool(
                name="ArtifactDetector",
                description=detect_artifacts.__doc__,
                func=detect_artifacts
            ),
            Tool(
                name="ProjectAnalyzer",
                description=self._get_project_analyzer_description(),
                func=self._call_project_analyzer
            ),
            Tool(
                name="ErrorSolver",
                description=self._get_error_solver_description(),
                func=self._call_error_solver
            ),
            Tool(
                name="GitHubCloner",
                description=self.github_manager.clone_project.__doc__,
                func=self.github_manager.clone_project
            ),
            Tool(
                name="ProjectCopyManager",
                description=self.github_manager.create_project_copy.__doc__,
                func=self.github_manager.create_project_copy
            ),
            Tool(
                name="LineEndingFixer",
                description=fix_line_endings.__doc__,
                func=fix_line_endings
            )
        ])
        
        return tools

    def _get_project_analyzer_description(self) -> str:
        """获取项目分析器描述"""
        return """Analyze C/C++ project structure, dependencies and build instructions.
        Returns JSON with dependencies list and build commands."""

    def _call_project_analyzer(self, *args) -> str:
        """调用项目分析器"""
        if not self.project_analyzer:
            return json.dumps({"error": "ProjectAnalyzer not initialized"})
        
        try:
            return self.project_analyzer.analyze_comprehensive()
        except Exception as e:
            return json.dumps({"error": f"Project analysis failed: {str(e)}"})

    def _get_error_solver_description(self) -> str:
        """获取错误解决器描述"""
        return """Solve compilation errors using GitHub Issues and Google search.
        Input: JSON string with error context. Returns solutions with commands."""

    def _call_error_solver(self, error_context: str) -> str:
        """调用错误解决器"""
        if not self.error_solver:
            return json.dumps({"error": "ErrorSolver not initialized"})
        
        try:
            return self.error_solver.solve(error_context)
        except Exception as e:
            return json.dumps({"error": f"Error solving failed: {str(e)}"})

    def get_compilation_logs(self) -> list:
        """获取编译日志"""
        all_logs = self.logger.copy()
        
        if self.project_analyzer:
            all_logs.extend(self.project_analyzer.get_analysis_logs())
        
        if self.error_solver:
            all_logs.extend(self.error_solver.get_error_logs())
        
        if self.docker_shell:
            all_logs.extend(self.docker_shell.logger)
        
        return all_logs
