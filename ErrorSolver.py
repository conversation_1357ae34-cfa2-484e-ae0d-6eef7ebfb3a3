# ErrorSolver.py - 错误处理智能体（参考AutoCompiler实现）
import json
import logging
import re
from langchain_openai import ChatOpenAI
from langchain.agents import create_react_agent, AgentExecutor, Tool
from langchain_core.prompts import PromptTemplate
from tools import GitHubManager, GoogleSearchAgent
from config import (
    LLM3_BASE_URL, LLM3_MODEL, LLM3_API_KEY,
    LLM_CONFIGS, ERROR_SOLVER_PROMPT
)

class ErrorSolver:
    """错误处理智能体 - 采用ReAct框架的专业化错误诊断智能体"""
    
    def __init__(self, project_name: str, project_url: str = ""):
        self.project_name = project_name
        self.project_url = project_url
        self.logger = []

        # LLM配置（完全参考AutoCompiler的方式）
        config = LLM_CONFIGS["error_solver"]
        self.llm = ChatOpenAI(
            base_url=LLM3_BASE_URL,
            model=LLM3_MODEL,
            api_key=LLM3_API_KEY,
            temperature=config["temperature"],
            timeout=config["timeout"],
            max_tokens=config["max_tokens"]
        )

        # 初始化工具实例
        self.github_manager = GitHubManager()
        self.google_search = GoogleSearchAgent()

    def solve(self, error_context: str) -> str:
        """
        Comprehensive analysis and resolution of C/C++ compilation errors.
        Uses GitHub Issues search and Google search to find similar problems and solutions.

        @param error_context: JSON string containing error details, project context, and compilation stage
        @return: JSON string with analyzed solutions and confidence ratings
        """
        try:
            # 构建内部工具集
            tools = [
                Tool(
                    name="GitHubIssuesSearcher",
                    description=self.github_manager.search_issues.__doc__,
                    func=lambda project_url, error_keywords, max_results=10: self.github_manager.search_issues(project_url, error_keywords, max_results)
                ),
                Tool(
                    name="GoogleSearcher",
                    description=self.google_search.search_compilation_solutions.__doc__,
                    func=self.google_search.search_compilation_solutions
                ),
                Tool(
                    name="QueryConstructor",
                    description=self.google_search.construct_search_query.__doc__,
                    func=self.google_search.construct_search_query
                ),
                Tool(
                    name="SolutionExtractor",
                    description=self.google_search.extract_solutions_from_results.__doc__,
                    func=self.google_search.extract_solutions_from_results
                ),
                Tool(
                    name="ErrorAnalyzer",
                    description=self.analyze_error_pattern.__doc__,
                    func=self.analyze_error_pattern
                ),
                Tool(
                    name="SolutionValidator",
                    description=self.validate_solution.__doc__,
                    func=self.validate_solution
                )
            ]

            # 创建提示词模板
            prompt = PromptTemplate(
                template=ERROR_SOLVER_PROMPT,
                input_variables=["input", "agent_scratchpad", "tools", "tool_names", "project_name", "error_message"]
            )

            # 创建ReAct智能体
            agent = create_react_agent(llm=self.llm, tools=tools, prompt=prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                max_iterations=LLM_CONFIGS["error_solver"]["max_iterations"],
                verbose=True,
                handle_parsing_errors=True
            )

            # 解析错误上下文
            try:
                error_data = json.loads(error_context)
                error_message = error_data.get('error_message', error_context)
            except:
                error_message = error_context

            # 执行错误解决任务
            result = agent_executor.invoke({
                "input": error_context,
                "project_name": self.project_name,
                "error_message": error_message
            })

            # 记录日志
            self.logger.append([
                "solve",
                self.project_name,
                result["output"]
            ])

            return result["output"]
            
        except Exception as e:
            error_msg = f"Error solving failed: {str(e)}"
            self.logger.append([
                "solve",
                self.project_name,
                error_msg
            ])
            return json.dumps({
                "error_analysis": "Failed to analyze error",
                "solutions": [],
                "status": "failed",
                "error": error_msg
            })

    def analyze_error_pattern(self, error_message: str, compilation_stage: str = "unknown") -> str:
        """
        Analyze error patterns to categorize the type of compilation problem.
        Identifies common error categories: dependency missing, version conflicts, syntax errors, etc.

        @param error_message: Raw error message from compilation output
        @param compilation_stage: Stage where error occurred (dependency_install/source_compile/main_compile)
        @return: JSON string with error categorization and preliminary analysis
        """
        try:
            # 错误类型分类
            error_categories = {
                "dependency_missing": ["no such file", "cannot find", "not found", "missing"],
                "compile_error": ["error:", "compilation terminated", "syntax error"],
                "linker_error": ["undefined reference", "cannot find -l", "ld: error"],
                "permission_denied": ["permission denied", "access denied"],
                "timeout_error": ["timeout", "killed", "terminated"]
            }

            error_lower = error_message.lower()
            detected_category = "unknown"
            confidence = 0.0

            for category, keywords in error_categories.items():
                for keyword in keywords:
                    if keyword in error_lower:
                        detected_category = category
                        confidence = 0.8
                        break
                if detected_category != "unknown":
                    break

            # 提取关键错误信息
            key_terms = []
            # 提取可能的库名、文件名等
            patterns = [
                r'lib\w+',  # 库名
                r'\w+\.h',  # 头文件
                r'\w+\.so', # 动态库
                r'\w+\.a'   # 静态库
            ]

            for pattern in patterns:
                matches = re.findall(pattern, error_message)
                key_terms.extend(matches)

            return json.dumps({
                "error_category": detected_category,
                "confidence": confidence,
                "key_terms": key_terms[:5],  # 限制数量
                "compilation_stage": compilation_stage,
                "analysis_status": "success"
            })

        except Exception as e:
            return json.dumps({
                "error_category": "unknown",
                "confidence": 0.0,
                "key_terms": [],
                "analysis_status": "failed",
                "error": str(e)
            })

    def validate_solution(self, proposed_solution: str, error_context: str) -> str:
        """
        Validate the feasibility and safety of proposed solutions.
        Checks for potential side effects and compatibility with the target environment.

        @param proposed_solution: Solution commands or instructions to validate
        @param error_context: Original error context for validation reference
        @return: JSON string with validation results and risk assessment
        """
        try:
            solution_data = json.loads(proposed_solution) if isinstance(proposed_solution, str) else proposed_solution

            # 基本安全检查
            risk_level = "low"
            warnings = []

            commands = solution_data.get("commands", [])
            for command in commands:
                if isinstance(command, str):
                    # 检查危险命令
                    dangerous_patterns = ["rm -rf", "sudo rm", "format", "mkfs"]
                    for pattern in dangerous_patterns:
                        if pattern in command.lower():
                            risk_level = "high"
                            warnings.append(f"Dangerous command detected: {pattern}")

                    # 检查系统级修改
                    system_patterns = ["sudo", "chmod 777", "chown"]
                    for pattern in system_patterns:
                        if pattern in command.lower():
                            if risk_level == "low":
                                risk_level = "medium"
                            warnings.append(f"System-level modification: {pattern}")

            return json.dumps({
                "validation_status": "success",
                "risk_level": risk_level,
                "warnings": warnings,
                "is_safe": risk_level != "high",
                "recommendations": self._generate_safety_recommendations(risk_level, warnings)
            })

        except Exception as e:
            return json.dumps({
                "validation_status": "failed",
                "risk_level": "unknown",
                "warnings": [],
                "is_safe": False,
                "error": str(e)
            })

    def _generate_safety_recommendations(self, risk_level: str, warnings: list) -> list:
        """生成安全建议"""
        recommendations = []

        if risk_level == "high":
            recommendations.append("Review commands carefully before execution")
            recommendations.append("Consider running in isolated environment")
        elif risk_level == "medium":
            recommendations.append("Monitor system changes during execution")
            recommendations.append("Have rollback plan ready")

        if warnings:
            recommendations.append("Address specific warnings before proceeding")

        return recommendations

    def get_error_logs(self) -> list:
        """获取错误处理日志"""
        return self.logger.copy()
